#!/usr/bin/env python3
"""
SteamDT抓取服务FastAPI主应用

独立的FastAPI服务端，提供RESTful API接口包装现有的抓取系统。
解决Playwright在Streamlit环境中的兼容性问题。
"""

import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
import uvicorn

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

# 使用统一日志系统
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)

# 导入路由
from src.cs2_investment.api.routers.analysis_router import router as analysis_router
from src.cs2_investment.api.routers.task_router import router as task_router

# 导入任务引擎
from src.cs2_investment.api.services.task_engine import task_engine

# 导入简化版投资分析调度器
from src.cs2_investment.scheduler.simple_investment_scheduler import simple_investment_scheduler

# 全局变量
scheduler_manager = None
realtime_scheduler_thread = None
investment_analysis_scheduler = None
investment_scheduler_thread = None

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger

logger = get_logger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="SteamDT抓取服务",
    description="独立的抓取服务端，提供RESTful API接口包装现有的抓取系统",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:8504",  # Streamlit前端
        "http://127.0.0.1:8504",
        "http://localhost:3000",  # 开发环境
        "http://127.0.0.1:3000"
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(analysis_router)
app.include_router(task_router)

# 统一响应格式
class APIResponse:
    """统一API响应格式"""
    
    @staticmethod
    def success(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
        """成功响应"""
        return {
            "success": True,
            "data": data,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "error": None
        }
    
    @staticmethod
    def error(message: str, error_code: str = "UNKNOWN_ERROR", data: Any = None) -> Dict[str, Any]:
        """错误响应"""
        return {
            "success": False,
            "data": data,
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "error": {
                "code": error_code,
                "message": message
            }
        }


# 全局异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """HTTP异常处理器"""
    logger.error(f"HTTP异常: {exc.status_code} - {exc.detail}")
    return JSONResponse(
        status_code=exc.status_code,
        content=APIResponse.error(
            message=exc.detail,
            error_code=f"HTTP_{exc.status_code}"
        )
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """请求验证异常处理器"""
    logger.error(f"请求验证失败: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content=APIResponse.error(
            message="请求参数验证失败",
            error_code="VALIDATION_ERROR",
            data={"errors": exc.errors()}
        )
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """通用异常处理器"""
    logger.error(f"未处理的异常: {type(exc).__name__} - {str(exc)}")
    return JSONResponse(
        status_code=500,
        content=APIResponse.error(
            message="服务器内部错误",
            error_code="INTERNAL_SERVER_ERROR"
        )
    )


# 健康检查接口
@app.get("/health", summary="健康检查", tags=["系统"])
async def health_check():
    """
    健康检查接口
    
    返回服务状态和基本信息
    """
    try:
        # 检查数据库连接
        db_status = "connected"
        try:
            from src.cs2_investment.config.database import create_database_engine
            engine = create_database_engine()
            with engine.connect() as conn:
                conn.execute("SELECT 1")
            db_status = "connected"
        except Exception as e:
            logger.warning(f"数据库连接检查失败: {e}")
            db_status = "disconnected"
        
        return APIResponse.success({
            "service": "SteamDT抓取服务",
            "status": "healthy",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "database": db_status,
            "port": 8000
        })
    
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail="健康检查失败")


# 服务信息接口
@app.get("/info", summary="服务信息", tags=["系统"])
async def service_info():
    """
    获取服务详细信息
    """
    return APIResponse.success({
        "name": "SteamDT抓取服务",
        "description": "独立的抓取服务端，提供RESTful API接口包装现有的抓取系统",
        "version": "1.0.0",
        "author": "SteamDT Team",
        "endpoints": {
            "health": "/health",
            "info": "/info",
            "docs": "/docs",
            "redoc": "/redoc"
        },
        "features": [
            "Playwright浏览器抓取",
            "异步任务处理",
            "任务状态管理",
            "批量分析支持",
            "完整错误处理"
        ]
    })


# 调度器状态接口
@app.get("/scheduler/status", summary="调度器状态", tags=["系统"])
async def scheduler_status():
    """
    获取所有调度器的状态信息
    """
    try:
        status = {
            "timestamp": datetime.now().isoformat(),
            "schedulers": {}
        }

        # 排行榜抓取调度器状态
        try:
            global scheduler_manager
            if scheduler_manager:
                ranking_status = scheduler_manager.get_status()
                status["schedulers"]["ranking_scraper"] = {
                    "name": "排行榜抓取调度器",
                    "status": "running" if ranking_status.get("is_running") else "stopped",
                    "is_scraping": ranking_status.get("is_scraping_running", False),
                    "daily_completed": ranking_status.get("daily_scraping_completed", False),
                    "last_scraping_date": ranking_status.get("last_scraping_date"),
                    "scheduled_jobs": ranking_status.get("scheduled_jobs", 0),
                    "next_run": ranking_status.get("next_run")
                }
            else:
                status["schedulers"]["ranking_scraper"] = {
                    "name": "排行榜抓取调度器",
                    "status": "not_initialized"
                }
        except Exception as e:
            status["schedulers"]["ranking_scraper"] = {
                "name": "排行榜抓取调度器",
                "status": "error",
                "error": str(e)
            }

        # 仅实时监控调度器状态
        try:
            from src.cs2_investment.scheduler.realtime_only_scheduler import realtime_only_scheduler
            realtime_status = realtime_only_scheduler.get_status()
            status["schedulers"]["realtime_monitor"] = {
                "name": "仅实时监控调度器",
                "status": "running" if realtime_status.get("is_running") else "stopped",
                "realtime_interval_hours": realtime_status.get("realtime_interval_hours"),
                "item_delay_seconds": realtime_status.get("item_delay_seconds"),
                "max_retry_count": realtime_status.get("max_retry_count"),
                "task_status": realtime_status.get("task_status", {}),
                "note": "已修复Playwright线程安全问题和弹窗处理"
            }
        except Exception as e:
            status["schedulers"]["realtime_monitor"] = {
                "name": "仅实时监控调度器",
                "status": "error",
                "error": str(e)
            }

        # 智能投资分析调度器状态
        try:
            global investment_analysis_scheduler
            if investment_analysis_scheduler:
                investment_status = investment_analysis_scheduler.get_status()
                status["schedulers"]["investment_analysis"] = {
                    "name": "智能投资分析调度器",
                    "status": "running" if investment_status.get("is_running") else "stopped",
                    "last_processed_time": investment_status.get("last_processed_time"),
                    "current_batch": investment_status.get("current_batch"),
                    "total_batches": investment_status.get("total_batches"),
                    "config": investment_status.get("config", {}),
                    "stats": investment_status.get("stats", {}),
                    "note": "自动化投资分析流程：排行榜数据监听 → 常规分析 → 投资筛选算法"
                }
            else:
                status["schedulers"]["investment_analysis"] = {
                    "name": "智能投资分析调度器",
                    "status": "not_initialized"
                }
        except Exception as e:
            status["schedulers"]["investment_analysis"] = {
                "name": "智能投资分析调度器",
                "status": "error",
                "error": str(e)
            }

        return APIResponse.success(status)

    except Exception as e:
        logger.error(f"获取调度器状态失败: {e}")
        raise HTTPException(status_code=500, detail="获取调度器状态失败")


# 投资分析调度器专用接口
@app.get("/scheduler/investment/status", summary="投资分析调度器状态", tags=["调度器"])
async def get_investment_scheduler_status():
    """获取投资分析调度器详细状态"""
    try:
        global investment_analysis_scheduler
        if not investment_analysis_scheduler:
            raise HTTPException(status_code=404, detail="投资分析调度器未初始化")

        status = investment_analysis_scheduler.get_status()
        return APIResponse.success(status)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取投资分析调度器状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")


@app.get("/scheduler/investment/health", summary="投资分析调度器健康状态", tags=["调度器"])
async def get_investment_scheduler_health():
    """获取投资分析调度器健康状态"""
    try:
        global investment_analysis_scheduler
        if not investment_analysis_scheduler:
            raise HTTPException(status_code=404, detail="投资分析调度器未初始化")

        health = investment_analysis_scheduler.get_health_status()
        return APIResponse.success(health)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取投资分析调度器健康状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取健康状态失败: {str(e)}")


@app.get("/scheduler/investment/config", summary="投资分析调度器配置", tags=["调度器"])
async def get_investment_scheduler_config():
    """获取投资分析调度器配置"""
    try:
        global investment_analysis_scheduler
        if not investment_analysis_scheduler:
            raise HTTPException(status_code=404, detail="投资分析调度器未初始化")

        config = investment_analysis_scheduler.get_config().to_dict()
        return APIResponse.success(config)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取投资分析调度器配置失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")


# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("🚀 SteamDT抓取服务启动中...")
    logger.info("📍 服务地址: http://localhost:8000")
    logger.info("📖 API文档: http://localhost:8000/docs")

    # 启动任务执行引擎
    try:
        await task_engine.start()
        logger.info("🔧 任务执行引擎启动成功")
    except Exception as e:
        logger.error(f"❌ 任务执行引擎启动失败: {e}")

    # 启动定时爬取调度器（排行榜抓取）
    # try:
    #     from src.cs2_investment.scheduler.scheduler_manager import SchedulerManager

    #     # 创建全局调度器实例
    #     global scheduler_manager
    #     scheduler_manager = SchedulerManager()
    #     scheduler_manager.start()
    #     logger.info("⏰ 定时爬取调度器启动成功")
    # except Exception as e:
    #     logger.error(f"❌ 定时爬取调度器启动失败: {e}")

    # 启动仅实时监控调度器（饰品实时数据抓取）
    try:
        from src.cs2_investment.scheduler.realtime_only_scheduler import realtime_only_scheduler
        import threading
        import asyncio

        def start_realtime_scheduler():
            """在新线程中启动仅实时监控调度器（修复Playwright线程安全问题）"""
            try:
                # 创建新的事件循环
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

                # 设置事件循环策略，确保Playwright能正确工作
                if hasattr(asyncio, 'WindowsProactorEventLoopPolicy'):
                    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

                # 启动仅实时监控调度器
                loop.run_until_complete(realtime_only_scheduler.start())
            except Exception as e:
                logger.error(f"实时监控调度器运行异常: {e}")
                import traceback
                logger.error(f"详细错误: {traceback.format_exc()}")
            finally:
                loop.close()

        # 在守护线程中启动仅实时监控调度器
        global realtime_scheduler_thread
        realtime_scheduler_thread = threading.Thread(
            target=start_realtime_scheduler,
            daemon=True,
            name="RealtimeSchedulerThread"
        )
        realtime_scheduler_thread.start()
        logger.info("📈 仅实时监控调度器启动成功")
    except Exception as e:
        logger.error(f"❌ 仅实时监控调度器启动失败: {e}")

    # 启动简化版投资分析调度器
    # try:
    #     global investment_analysis_scheduler
    #     investment_analysis_scheduler = simple_investment_scheduler

    #     # 在新线程中启动投资分析调度器（避免阻塞主线程）
    #     import threading
    #     import asyncio

    #     def start_investment_scheduler():
    #         """在新线程中启动简化版投资分析调度器"""
    #         loop = None
    #         try:
    #             # 设置事件循环策略，确保Playwright能正确工作（必须在创建事件循环之前设置）
    #             if hasattr(asyncio, 'WindowsProactorEventLoopPolicy'):
    #                 asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())

    #             # 创建新的事件循环
    #             loop = asyncio.new_event_loop()
    #             asyncio.set_event_loop(loop)

    #             # 启动投资分析调度器
    #             loop.run_until_complete(simple_investment_scheduler.start())

    #             # 等待调度器任务完成（通常不会完成，除非被取消）
    #             if simple_investment_scheduler.analysis_task:
    #                 loop.run_until_complete(simple_investment_scheduler.analysis_task)
    #         except Exception as e:
    #             logger.error(f"简化版投资分析调度器运行异常: {e}")
    #             import traceback
    #             logger.error(f"详细错误: {traceback.format_exc()}")
    #         finally:
    #             # 确保正确关闭事件循环
    #             if loop and not loop.is_closed():
    #                 try:
    #                     # 取消所有未完成的任务
    #                     pending_tasks = asyncio.all_tasks(loop)
    #                     if pending_tasks:
    #                         logger.info(f"取消 {len(pending_tasks)} 个未完成的任务")
    #                         for task in pending_tasks:
    #                             task.cancel()
    #                         # 等待所有任务被取消
    #                         loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
    #                     loop.close()
    #                     logger.info("✅ 事件循环已正确关闭")
    #                 except Exception as e:
    #                     logger.warning(f"⚠️ 关闭事件循环时出现异常: {e}")

    #     # 在守护线程中启动简化版投资分析调度器
    #     global investment_scheduler_thread
    #     investment_scheduler_thread = threading.Thread(
    #         target=start_investment_scheduler,
    #         daemon=True,
    #         name="SimpleInvestmentSchedulerThread"
    #     )
    #     investment_scheduler_thread.start()
    #     logger.info("🧠 简化版投资分析调度器启动成功")
    # except Exception as e:
    #     logger.error(f"❌ 简化版投资分析调度器启动失败: {e}")

    logger.info("✅ SteamDT抓取服务启动完成")


# 关闭事件
@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("🛑 SteamDT抓取服务正在关闭...")

    # 停止任务执行引擎
    try:
        await task_engine.stop()
        logger.info("🔧 任务执行引擎已停止")
    except Exception as e:
        logger.error(f"❌ 任务执行引擎停止失败: {e}")

    # 停止定时爬取调度器
    # try:
    #     global scheduler_manager
    #     if 'scheduler_manager' in globals() and scheduler_manager:
    #         scheduler_manager.stop()
    #         logger.info("⏰ 定时爬取调度器已停止")
    # except Exception as e:
    #     logger.error(f"❌ 定时爬取调度器停止失败: {e}")

    # 停止仅实时监控调度器
    try:
        from src.cs2_investment.scheduler.realtime_only_scheduler import realtime_only_scheduler
        realtime_only_scheduler.stop()
        logger.info("📈 仅实时监控调度器已停止")
    except Exception as e:
        logger.error(f"❌ 仅实时监控调度器停止失败: {e}")

    # 停止简化版投资分析调度器
    # try:
    #     global investment_analysis_scheduler
    #     if investment_analysis_scheduler:
    #         await investment_analysis_scheduler.stop()
    #         logger.info("🧠 简化版投资分析调度器已停止")
    # except Exception as e:
    #     logger.error(f"❌ 简化版投资分析调度器停止失败: {e}")

    logger.info("✅ SteamDT抓取服务已关闭")


# 根路径重定向到文档
@app.get("/", summary="根路径", tags=["系统"])
async def root():
    """根路径，重定向到API文档"""
    return APIResponse.success({
        "message": "欢迎使用SteamDT抓取服务",
        "docs_url": "/docs",
        "health_check": "/health",
        "service_info": "/info"
    })


def main():
    """主函数，用于直接运行服务"""
    uvicorn.run(
        "src.cs2_investment.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        workers=1,
        log_level="info"
    )


if __name__ == "__main__":
    main()
