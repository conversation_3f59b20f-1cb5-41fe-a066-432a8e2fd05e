#!/usr/bin/env python3
"""
完整系统启动脚本

同时启动API服务和定时调度器
"""

import sys
import logging
import asyncio
import subprocess
import time
import signal
import os
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class FullSystemManager:
    """完整系统管理器"""
    
    def __init__(self):
        self.api_process = None
        self.scheduler_process = None
        self.is_running = False
        
        # 使用统一日志组件
        from src.cs2_investment.utils.logger import get_logger
        self.logger = get_logger(__name__)
    
    def check_api_service_running(self):
        """检查API服务是否已经在运行"""
        try:
            import requests
            response = requests.get("http://localhost:8000/health", timeout=3)
            if response.status_code == 200:
                return True
        except:
            pass
        return False

    def start_api_service(self):
        """启动API服务"""
        try:
            # 首先检查API服务是否已经在运行
            if self.check_api_service_running():
                self.logger.info("✅ API服务已在运行，跳过启动")
                return True

            self.logger.info("🚀 启动API服务...")
            self.api_process = subprocess.Popen([
                sys.executable, "start_api_service.py"
            ], cwd=str(project_root))

            # 等待API服务启动
            time.sleep(5)

            # 再次检查服务是否成功启动
            if self.check_api_service_running():
                self.logger.info("✅ API服务启动成功")
                return True
            else:
                self.logger.error("❌ API服务启动失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ 启动API服务失败: {e}")
            return False
    
    def start_scheduler(self):
        """启动定时调度器"""
        try:
            self.logger.info("⏰ 启动定时调度器...")
            self.scheduler_process = subprocess.Popen([
                sys.executable, "start_simple_scheduler.py"
            ], cwd=str(project_root))
            
            # 等待调度器启动
            time.sleep(3)
            
            if self.scheduler_process.poll() is None:
                self.logger.info("✅ 定时调度器启动成功")
                return True
            else:
                self.logger.error("❌ 定时调度器启动失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 启动定时调度器失败: {e}")
            return False
    
    def stop_all(self):
        """停止所有服务"""
        self.logger.info("🛑 正在停止所有服务...")
        self.is_running = False
        
        # 停止调度器
        if self.scheduler_process and self.scheduler_process.poll() is None:
            try:
                self.scheduler_process.terminate()
                self.scheduler_process.wait(timeout=10)
                self.logger.info("✅ 定时调度器已停止")
            except subprocess.TimeoutExpired:
                self.scheduler_process.kill()
                self.logger.warning("⚠️ 强制终止定时调度器")
            except Exception as e:
                self.logger.error(f"❌ 停止定时调度器失败: {e}")
        
        # 停止API服务
        if self.api_process and self.api_process.poll() is None:
            try:
                self.api_process.terminate()
                self.api_process.wait(timeout=10)
                self.logger.info("✅ API服务已停止")
            except subprocess.TimeoutExpired:
                self.api_process.kill()
                self.logger.warning("⚠️ 强制终止API服务")
            except Exception as e:
                self.logger.error(f"❌ 停止API服务失败: {e}")
    
    def start(self):
        """启动完整系统"""
        print("=" * 60)
        print("🚀 SteamDT完整系统启动脚本")
        print("=" * 60)
        
        try:
            self.logger.info("🚀 开始启动完整系统...")
            
            # 启动API服务
            if not self.start_api_service():
                self.logger.error("❌ API服务启动失败，退出")
                return False
            
            # 启动定时调度器
            if not self.start_scheduler():
                self.logger.error("❌ 定时调度器启动失败，但API服务继续运行")
            
            self.is_running = True
            
            print("")
            print("✅ 系统启动完成！")
            print("📍 API服务地址: http://localhost:8000")
            print("📖 API文档: http://localhost:8000/docs")
            print("🔍 健康检查: http://localhost:8000/health")
            print("⏰ 定时调度器: 每24小时执行常规分析，每1小时执行实时监控")
            print("")
            print("按 Ctrl+C 停止系统")
            
            # 监控进程状态
            self.monitor_processes()
            
            return True
            
        except KeyboardInterrupt:
            self.logger.info("收到停止信号")
            self.stop_all()
            return True
        except Exception as e:
            self.logger.error(f"❌ 系统启动失败: {e}")
            self.stop_all()
            return False
    
    def monitor_processes(self):
        """监控进程状态"""
        try:
            while self.is_running:
                # 检查API服务状态
                if self.api_process and self.api_process.poll() is not None:
                    self.logger.error("❌ API服务意外停止")
                    break
                
                # 检查调度器状态
                if self.scheduler_process and self.scheduler_process.poll() is not None:
                    self.logger.warning("⚠️ 定时调度器意外停止")
                    # 尝试重启调度器
                    self.logger.info("🔄 尝试重启定时调度器...")
                    if self.start_scheduler():
                        self.logger.info("✅ 定时调度器重启成功")
                    else:
                        self.logger.error("❌ 定时调度器重启失败")
                
                time.sleep(30)  # 每30秒检查一次
                
        except KeyboardInterrupt:
            pass
    
    def get_status(self):
        """获取系统状态"""
        api_status = "运行中" if self.api_process and self.api_process.poll() is None else "已停止"
        scheduler_status = "运行中" if self.scheduler_process and self.scheduler_process.poll() is None else "已停止"
        
        return {
            "api_service": api_status,
            "scheduler": scheduler_status,
            "system_running": self.is_running
        }


def signal_handler(signum, frame):
    """信号处理器"""
    print("\n收到停止信号，正在关闭系统...")
    if hasattr(signal_handler, 'manager'):
        signal_handler.manager.stop_all()
    sys.exit(0)


def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建系统管理器
    manager = FullSystemManager()
    signal_handler.manager = manager
    
    try:
        # 启动系统
        success = manager.start()
        
        if success:
            print("✅ 系统正常退出")
        else:
            print("❌ 系统异常退出")
            sys.exit(1)
            
    except Exception as e:
        print(f"❌ 系统启动失败: {e}")
        manager.stop_all()
        sys.exit(1)


if __name__ == "__main__":
    main()
