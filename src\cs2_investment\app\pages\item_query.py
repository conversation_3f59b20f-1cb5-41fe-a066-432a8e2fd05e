"""
饰品查询页面

提供饰品的多条件查询和展示功能。
"""

import streamlit as st
import pandas as pd
from typing import List, Dict, Any, Optional
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.services.item_service import ItemService
from src.cs2_investment.app.utils.data_formatter import format_price, format_percentage
from src.cs2_investment.app.components.item_card import display_item_card
from src.cs2_investment.app.components import item_analysis_component


def show_page():
    """显示饰品查询页面"""
    # 初始化页面状态
    if 'current_view' not in st.session_state:
        st.session_state.current_view = 'list'
    if 'selected_item_id' not in st.session_state:
        st.session_state.selected_item_id = None

    # 根据当前视图显示不同页面
    if st.session_state.current_view == 'detail' and st.session_state.selected_item_id:
        show_item_detail_page(st.session_state.selected_item_id)
    else:
        show_item_list_page()


def show_item_list_page():
    """显示饰品列表页面"""
    st.title("🔍 饰品查询")

    # 查询条件区域
    with st.expander("查询条件", expanded=True):
        show_query_filters()

    # 查询结果区域
    show_query_results()


def show_item_detail_page(item_id: str):
    """显示饰品详情页面"""
    from src.cs2_investment.app.components.item_card import show_item_detail_page
    show_item_detail_page(item_id)


def show_query_filters():
    """显示查询过滤条件"""
    try:
        # 初始化服务
        if 'item_service' not in st.session_state:
            st.session_state.item_service = ItemService()

        # 获取筛选选项
        if 'filter_options' not in st.session_state:
            with st.spinner("加载筛选选项..."):
                st.session_state.filter_options = st.session_state.item_service.get_filter_options()

        filter_options = st.session_state.filter_options
    except Exception as e:
        st.error(f"初始化服务失败: {str(e)}")
        st.info("请检查数据库连接配置")
        # 使用默认选项
        filter_options = {
            'item_types': [],
            'qualities': [],
            'rarities': []
        }

    col1, col2, col3 = st.columns(3)

    with col1:
        # 名称搜索
        name_query = st.text_input(
            "饰品名称",
            placeholder="输入饰品名称关键词...",
            help="支持模糊搜索"
        )

        # 饰品类型
        item_types = filter_options.get('item_types', [])
        selected_types = st.multiselect(
            "饰品类型",
            options=item_types,
            help="选择一个或多个饰品类型"
        )

    with col2:
        # 品质选择
        qualities = filter_options.get('qualities', [])
        selected_qualities = st.multiselect(
            "品质",
            options=qualities,
            help="选择饰品品质"
        )

        # 稀有度选择
        rarities = filter_options.get('rarities', [])
        selected_rarities = st.multiselect(
            "稀有度",
            options=rarities,
            help="选择稀有度等级"
        )

    with col3:
        # 价格范围
        price_range = st.slider(
            "价格范围 (¥)",
            min_value=0,
            max_value=100000,
            value=(0, 10000),
            step=100,
            help="设置价格筛选范围"
        )

        # 排序方式
        sort_options = {
            "name_asc": "名称 (A-Z)",
            "name_desc": "名称 (Z-A)",
            "price_asc": "价格 (低到高)",
            "price_desc": "价格 (高到低)",
            "updated_desc": "最新更新"
        }
        sort_by = st.selectbox(
            "排序方式",
            options=list(sort_options.keys()),
            format_func=lambda x: sort_options[x],
            index=4  # 默认按最新更新排序
        )
    
    # 查询按钮
    col1, col2, col3 = st.columns([1, 1, 1])
    with col2:
        if st.button("🔍 查询", type="primary", use_container_width=True):
            # 构建查询参数
            query_params = {
                'name_query': name_query if name_query else None,
                'item_types': selected_types if selected_types else None,
                'qualities': selected_qualities if selected_qualities else None,
                'rarities': selected_rarities if selected_rarities else None,
                'price_min': price_range[0] if price_range[0] > 0 else None,
                'price_max': price_range[1] if price_range[1] < 100000 else None,
                'sort_by': sort_by,
                'limit': 5000  # 增加查询限制到5000条
            }

            # 保存查询参数到session state
            st.session_state.query_params = query_params
            st.session_state.query_executed = True


def show_query_results():
    """显示查询结果"""
    if not hasattr(st.session_state, 'query_executed') or not st.session_state.query_executed:
        st.info("请设置查询条件并点击查询按钮")
        return
    
    try:
        # 执行查询
        with st.spinner("正在查询数据..."):
            results = execute_query(st.session_state.query_params)
        
        if not results:
            st.warning("未找到符合条件的饰品")
            return
        
        # 显示结果统计
        st.subheader(f"查询结果 ({len(results)} 个饰品)")
        
        # 分页显示
        items_per_page = 50
        total_pages = (len(results) - 1) // items_per_page + 1
        
        if total_pages > 1:
            page = st.selectbox(
                "页码",
                range(1, total_pages + 1),
                format_func=lambda x: f"第 {x} 页"
            )
        else:
            page = 1
        
        # 计算当前页的数据范围
        start_idx = (page - 1) * items_per_page
        end_idx = min(start_idx + items_per_page, len(results))
        current_page_results = results[start_idx:end_idx]
        
        # 显示结果 - 列表格式
        for item in current_page_results:
            display_item_row(item)
    
    except Exception as e:
        st.error(f"查询失败: {str(e)}")


def display_item_row(item: Dict[str, Any]):
    """显示饰品行格式"""
    with st.container():
        col1, col2, col3, col4, col5, col6, col7 = st.columns([3, 2, 2, 2, 1, 1, 1])

        with col1:
            # 饰品名称和类型
            item_name = item.get('name', '未知饰品')
            st.markdown(f"**{item_name}**")
            st.caption(f"ID: {item.get('item_id', '未知')}")

        with col2:
            # 当前价格
            current_price = item.get('current_price', 0)
            if current_price > 0:
                st.metric("当前价格", f"¥{current_price:.2f}")
            else:
                st.write("💰 价格未知")

        with col3:
            # 7天变化
            price_change_7d = item.get('price_change_7d', 0)
            if price_change_7d != 0:
                color = "🔴" if price_change_7d < 0 else "🟢"
                st.write(f"{color} {price_change_7d:+.2f}% (7天)")
            else:
                st.write("📊 无变化")

        with col4:
            # 交易量
            volume_30d = item.get('volume_30d', 0)
            if volume_30d > 0:
                st.write(f"📈 {volume_30d} 笔 (30天)")
            else:
                st.write("📈 无交易数据")

        with col5:
            # 收藏按钮
            item_id = item.get('item_id')
            if item_id:
                # 检查是否已收藏
                if 'favorite_service' not in st.session_state:
                    from src.cs2_investment.app.services.favorite_service import FavoriteService
                    st.session_state.favorite_service = FavoriteService()

                is_favorited = st.session_state.favorite_service.is_favorited(
                    user_id="default_user",
                    item_id=item_id
                )

                button_text = "💖" if is_favorited else "🤍"
                if st.button(button_text, key=f"fav_{item_id}", help="收藏/取消收藏"):
                    if is_favorited:
                        success = st.session_state.favorite_service.remove_favorite(
                            user_id="default_user",
                            item_id=item_id
                        )
                        if success:
                            st.success("已取消收藏")
                            st.rerun()
                    else:
                        success = st.session_state.favorite_service.add_favorite(
                            user_id="default_user",
                            item_id=item_id,
                            item_name=item_name
                        )
                        if success:
                            st.success("已添加收藏")
                            st.rerun()

        with col6:
            # 分析按钮
            if item_id:
                item_analysis_component.render_analysis_button(
                    item_id=item_id,
                    button_key=f"item_analysis_{item_id}",
                    help_text="查看饰品分析结果"
                )

        with col7:
            # 详情按钮
            if item_id:
                if st.button("📋", key=f"detail_{item_id}", help="查看详情"):
                    st.session_state.current_view = 'detail'
                    st.session_state.selected_item_id = item_id
                    st.rerun()

        # 分析对话框
        if item_id:
            item_analysis_component.render_analysis_dialog(
                item_data={
                    'item_id': item_id,
                    'item_name': item_name
                },
                dialog_key_suffix="_items"
            )

        st.divider()


def execute_query(params: Dict[str, Any]) -> List[Dict]:
    """执行查询"""
    try:
        item_service = st.session_state.item_service
        return item_service.search_items_with_prices(**params)
    except Exception as e:
        st.error(f"查询失败: {str(e)}")
        return []



