# 排行榜饰品投资分析系统

## 📋 系统概述

排行榜饰品投资分析系统是一个基于SteamDT数据的自动化投资分析平台，通过定时抓取和分析各类排行榜数据，为CS2饰品投资提供专业的技术分析和投资建议。

## 🔧 系统配置

### 核心参数
- **检查间隔**: 10分钟
- **饰品间隔**: 10-30秒随机等待
- **批次大小**: 最多50个饰品/批次
- **榜单限制**: 每个榜单最多500个饰品
- **数据源**: 全榜单覆盖（13个榜单）

### 技术配置
```python
class SimpleInvestmentScheduler:
    def __init__(self):
        self.check_interval_minutes = 10        # 每10分钟检查一次
        self.item_delay_seconds = (10, 30)      # 饰品之间间隔10-30秒
        self.max_items_per_batch = 50           # 每批最多处理50个饰品
        self.max_ranking_items = 500            # 每个榜单最大处理数量
        self.last_processed_time = None         # 上次处理时间
```

## 📊 数据源覆盖

### 全榜单数据获取
系统覆盖SteamDT平台的所有主要排行榜：

#### 1. 热度排行榜
- **数量**: 最多500个饰品
- **排序**: 按热度排名升序
- **用途**: 识别市场关注度高的饰品

#### 2. 涨幅榜（4个时间周期）
- **1天涨幅榜**: 200个饰品
- **3天涨幅榜**: 200个饰品  
- **7天涨幅榜**: 200个饰品
- **1个月涨幅榜**: 200个饰品
- **用途**: 捕捉价格上涨趋势的饰品

#### 3. 跌幅榜（4个时间周期）
- **1天跌幅榜**: 200个饰品
- **3天跌幅榜**: 200个饰品
- **7天跌幅榜**: 200个饰品
- **1个月跌幅榜**: 200个饰品
- **用途**: 识别价格下跌的潜在抄底机会

#### 4. 高成交量榜（4个时间周期）
- **1天成交量榜**: 200个饰品
- **3天成交量榜**: 200个饰品
- **7天成交量榜**: 200个饰品
- **1个月成交量榜**: 200个饰品
- **用途**: 发现交易活跃的流动性好的饰品

### 数据处理流程
```python
# 数据获取示例
all_ranking_items = []

# 获取各类榜单数据
hot_items = market_dao.get_hot_ranking_items_by_date(today, limit=500)
for period in ['1d', '3d', '7d', '1m']:
    gainers = market_dao.get_top_gainers_by_date(today, period=period, limit=200)
    losers = market_dao.get_top_losers_by_date(today, period=period, limit=200)
    volume_items = market_dao.get_high_volume_items_by_date(today, period=period, limit=200)
    
    all_ranking_items.extend([hot_items, gainers, losers, volume_items])

# 去重处理
unique_items = {item.item_id: item for item in all_ranking_items}
```

## 🔍 智能执行机制

### 数据更新检查
系统采用智能数据更新检查机制，避免重复处理：

```python
async def _check_ranking_data_update(self) -> bool:
    # 获取当天最新快照时间
    latest_time = session.query(func.max(MarketSnapshot.snapshot_time)).scalar()
    
    # 首次运行检查
    if self.last_processed_time is None:
        return True
    
    # 时间差检查（超过1分钟才认为是新数据）
    time_diff = (latest_time - self.last_processed_time).total_seconds()
    return time_diff > 60
```

### 已推荐饰品过滤
防止重复分析已有推荐记录的饰品：

```python
async def _filter_unanalyzed_items(self, ranking_items, target_date):
    # 查询当天已有推荐记录的饰品ID
    from src.cs2_investment.dao.investment_recommendation_dao import investment_recommendation_dao
    recommended_item_ids = investment_recommendation_dao.get_recommended_item_ids_by_date(target_date)
    recommended_set = set(recommended_item_ids)

    # 过滤出需要处理的饰品（排除已有推荐记录的饰品）
    unprocessed_items = [item for item in ranking_items if item.item_id not in recommended_set]
    return unprocessed_items
```

## 📋 执行流程详解

### 1. 系统启动阶段
- 🚀 初始化简化版投资分析调度器
- 🔧 创建IntegratedAnalysisSystem实例
- 📊 初始化数据访问对象
- 🧵 在独立线程中启动异步事件循环

### 2. 数据更新检查阶段
- 🔍 检查当天排行榜数据是否更新
- ⏰ 基于`last_processed_time`判断是否需要处理
- 📊 验证有效热度排名数据存在
- 🆕 首次运行或数据更新超过1分钟才执行

### 3. 全榜单数据获取阶段
- 📈 并行获取13个排行榜数据
- 🔄 基于item_id进行去重处理
- 📊 统计去重前后的数据量
- 🔍 过滤已有推荐记录的饰品

### 4. 单饰品分析阶段
对每个饰品执行完整的6步分析流程：

#### 4.1 数据抓取
- 🌐 使用market_hash_name构建正确URL
- 📊 抓取完整数据集（日K/周K/时K/6个月趋势）
- 🔄 处理弹窗和页面交互

#### 4.2 数据转换
- 🔄 将抓取数据转换为分析系统格式
- 📁 保存到指定目录结构
- ✅ 验证数据完整性

#### 4.3 专业分析
- 📊 运行CS2AnalysisSystemV2分析系统
- 🔍 技术指标计算
- 📈 分层分析处理
- 📋 生成综合报告

#### 4.4 图表生成
- 📈 生成专业技术分析图表
- 💾 保存图表文件
- ⚠️ 图表失败不影响整体流程

#### 4.5 数据库保存
- 💾 保存分析结果到数据库
- 📊 更新分析状态
- 🔄 记录处理时间

#### 4.6 投资推荐
- 🧠 运行投资筛选算法
- 📊 生成投资推荐结果
- 💾 保存推荐数据

### 5. 循环控制阶段
- ⏳ 饰品间随机等待10-30秒
- 📊 统计成功/失败数量
- 🔄 更新`last_processed_time`状态
- ⏰ 10分钟后开始下一轮检查

## 🎯 关键技术特性

### URL构建优化
```python
# 使用market_hash_name构建正确的URL
import urllib.parse
encoded_name = urllib.parse.quote(market_hash_name, safe='')
item_url = f"https://steamdt.com/cs2/{encoded_name}"
```

### 异步任务管理
```python
# 正确的Playwright实例管理
class SteamDTScraper:
    def __init__(self):
        self.playwright = None  # 保存playwright实例
        
    async def start(self):
        self.playwright = await async_playwright().start()
        
    async def close(self):
        if self.playwright:
            await self.playwright.stop()
```

### 错误处理机制
- 🔄 单饰品失败不影响整体流程
- ⏰ 异常后5分钟重试
- 📊 详细的成功/失败统计
- 🛡️ Playwright连接正确关闭

## 📈 性能优化

### 批次处理优化
- **批次大小**: 从20个提升到50个饰品
- **并发控制**: 串行处理避免IP封禁
- **间隔控制**: 10-30秒随机间隔

### 数据覆盖优化
- **榜单数量**: 从1个扩展到13个榜单
- **数据量**: 理论最大3300个饰品（去重后通常100-500个）
- **更新频率**: 从30分钟优化到10分钟

### 重复执行防护
- **数据更新检查**: 避免处理相同数据
- **已分析过滤**: 避免重复分析饰品
- **状态跟踪**: 精确的处理时间记录

## 🔧 部署与运行

### 启动方式
```bash
# 通过API服务启动
python start_api_service.py

# 直接运行调度器
python -m src.cs2_investment.scheduler.simple_investment_scheduler
```

### 监控端点
- **服务地址**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health
- **调度器状态**: http://localhost:8000/scheduler/status

## 📊 执行统计

### 典型执行数据
- **数据获取**: 13个榜单，去重前~3300个，去重后~100-500个
- **处理时间**: 每个饰品约2-5分钟
- **批次耗时**: 50个饰品约2-4小时
- **检查频率**: 每10分钟检查一次数据更新

### 日志示例
```
2025-07-30 16:59:01 - 获取当天热度排行榜饰品: 14 个
2025-07-30 16:59:01 - 获取当天1d涨幅榜饰品: 14 个
2025-07-30 16:59:01 - 获取当天1m高成交量饰品: 14 个
2025-07-30 16:59:01 - 去重前总数: 122, 去重后: 14
2025-07-30 16:59:01 - 当天分析状态: 总饰品 14 个, 已分析 0 个, 待分析 14 个
2025-07-30 16:59:01 - 📋 发现 14 个饰品需要分析
```

## � 系统流程图

### 完整执行流程
```mermaid
flowchart TD
    A[🚀 系统启动] --> B[🔧 初始化调度器]
    B --> C[🧵 启动独立线程]
    C --> D[📊 开始分析循环]

    D --> E{🔍 检查数据更新}
    E -->|首次运行| F[🆕 首次运行标记]
    E -->|数据更新>1分钟| G[📈 数据更新检测]
    E -->|无更新| H[📊 跳过本次分析]

    F --> I[📋 获取全榜单数据]
    G --> I

    I --> J[📈 热度榜500个]
    I --> K[📊 涨幅榜4期×200个]
    I --> L[📉 跌幅榜4期×200个]
    I --> M[💰 成交量榜4期×200个]

    J --> N[🔄 数据去重]
    K --> N
    L --> N
    M --> N

    N --> O[🔍 过滤已推荐饰品]
    O --> P{有待处理饰品?}
    P -->|否| H
    P -->|是| Q[📦 限制批次≤50个]

    Q --> R[📋 开始逐个分析]
    R --> S[📍 处理单个饰品]

    S --> T[🌐 数据抓取]
    T --> U{抓取成功?}
    U -->|失败| V[❌ 记录失败]
    U -->|成功| W[🔄 数据转换]

    W --> X{转换成功?}
    X -->|失败| V
    X -->|成功| Y[📊 专业分析]

    Y --> Z{分析成功?}
    Z -->|失败| V
    Z -->|成功| AA[📈 图表生成]

    AA --> BB[💾 数据库保存]
    BB --> CC[🧠 投资推荐]
    CC --> DD[✅ 记录成功]

    V --> EE{还有饰品?}
    DD --> EE
    EE -->|是| FF[⏳ 等待10-30秒]
    FF --> S
    EE -->|否| GG[📊 统计结果]

    GG --> HH[🔄 更新处理时间]
    H --> II[⏰ 等待10分钟]
    HH --> II
    II --> D

    D --> JJ{收到停止信号?}
    JJ -->|是| KK[🛑 停止调度器]
    JJ -->|否| E
```

## 🔧 技术实现细节

### 数据抓取配置
```python
# 完整数据抓取要求
data_requirements = {
    'daily_kline_1': True,      # 日K线数据1
    'daily_kline_2': True,      # 日K线数据2
    'weekly_kline': True,       # 周K线数据
    'trend_data_6m': True,      # 6个月趋势数据
    'hourly_kline': True,       # 时K线数据
    'trend_data_3m': False      # 不需要3个月数据
}
```

### 数据库表结构
```sql
-- 分析日志表
CREATE TABLE analysis_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    item_id VARCHAR(255) NOT NULL,
    analysis_date DATE NOT NULL,
    analysis_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_item_date (item_id, analysis_date)
);

-- 投资推荐结果表
CREATE TABLE screening_results (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    item_id VARCHAR(255) NOT NULL,
    algorithm_type VARCHAR(100) NOT NULL,
    recommendation_score DECIMAL(5,2),
    risk_level VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 关键算法实现
```python
# 投资筛选算法示例
class InvestmentScreeningAlgorithm:
    def __init__(self):
        self.algorithms = [
            'momentum_breakout',      # 动量突破算法
            'mean_reversion',         # 均值回归算法
            'volume_price_trend',     # 量价趋势算法
            'support_resistance',     # 支撑阻力算法
            'volatility_breakout',    # 波动率突破算法
            'pattern_recognition',    # 形态识别算法
            'risk_reward_ratio'       # 风险收益比算法
        ]

    def run_screening(self, analysis_data):
        recommendations = []
        for algorithm in self.algorithms:
            result = self._run_algorithm(algorithm, analysis_data)
            if result['score'] > 0.7:  # 高分推荐
                recommendations.append(result)
        return recommendations
```

## 📈 性能监控指标

### 系统性能指标
- **数据获取成功率**: >95%
- **分析完成率**: >90%
- **平均处理时间**: 2-5分钟/饰品
- **系统可用性**: >99%
- **内存使用**: <2GB
- **CPU使用**: <50%

### 业务指标
- **日处理饰品数**: 100-500个
- **投资推荐生成率**: >80%
- **推荐算法覆盖**: 7种算法
- **数据更新延迟**: <10分钟
- **重复分析率**: <5%

## �🚀 未来优化方向

1. **并发处理**: 考虑适度并发提高处理速度
2. **算法优化**: 改进投资筛选算法精度
3. **数据扩展**: 增加更多数据源和指标
4. **实时监控**: 添加实时价格变动监控
5. **智能调度**: 基于市场活跃度动态调整检查频率
6. **机器学习**: 引入ML模型提高预测准确性
7. **风险管理**: 完善风险评估和预警机制

## 📞 技术支持

### 常见问题
1. **Q: 为什么有些饰品分析失败？**
   A: 可能是网络问题、页面结构变化或数据不完整，系统会自动重试。

2. **Q: 如何查看分析进度？**
   A: 可通过API端点 `/scheduler/status` 查看实时状态。

3. **Q: 投资推荐的准确性如何？**
   A: 系统提供技术分析建议，实际投资需结合市场情况和个人判断。

### 联系方式
- **技术文档**: `/docs` 目录
- **API文档**: http://localhost:8000/docs
- **日志文件**: `logs/` 目录
- **配置文件**: `src/cs2_investment/config/`

---

*文档版本: v2.1*
*最后更新: 2025-07-30*
*系统状态: 生产环境运行中*
*维护团队: SteamDT投资分析团队*
