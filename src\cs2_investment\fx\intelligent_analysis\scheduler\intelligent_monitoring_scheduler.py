"""
智能监控调度系统

基于现有RealtimeOnlyScheduler的调度框架，集成智能分析引擎，
实现自适应监控频率、优先级调度、异常检测和自动恢复等功能。
"""

import asyncio
import logging
import random
import json
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
from loguru import logger

from ..core.decision_fusion_engine import DecisionFusionEngine
from ..core.multi_timeframe_engine import MultiTimeframeEngine

# 可选导入DAO模块
try:
    from ...dao.favorite_dao import FavoriteDAO
    from ...dao.holding_dao import HoldingDAO
    from ...dao.arbitrage_item_dao import ArbitrageItemDAO
    from ...dao.item_dao import ItemDAO
    DAO_AVAILABLE = True
except ImportError:
    # 如果DAO模块不可用，创建模拟类
    class MockDAO:
        def get_by_id(self, item_id): return {'name': 'Mock Item', 'market_hash_name': 'Mock Item', 'url': 'https://mock.com'}
        def get_all(self): return []

    FavoriteDAO = MockDAO
    HoldingDAO = MockDAO
    ArbitrageItemDAO = MockDAO
    ItemDAO = MockDAO
    DAO_AVAILABLE = False


class MonitoringPriority(Enum):
    """监控优先级"""
    CRITICAL = 1    # 关键：持仓饰品
    HIGH = 2        # 高：收藏饰品
    MEDIUM = 3      # 中：搬砖饰品
    LOW = 4         # 低：其他饰品


class MonitoringStatus(Enum):
    """监控状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"


class AnalysisLayer(Enum):
    """分析层级"""
    STRATEGIC = "strategic"      # 战略分析：24小时
    TACTICAL = "tactical"        # 战术分析：4小时
    EXECUTION = "execution"      # 执行分析：1小时
    SUPPLY_DEMAND = "supply_demand"  # 供需分析：30分钟


@dataclass
class LayeredMonitoringTask:
    """分层监控任务"""
    task_id: str
    item_id: str
    item_name: str
    market_hash_name: str
    url: str
    priority: MonitoringPriority
    analysis_layer: AnalysisLayer
    source: str
    status: MonitoringStatus = MonitoringStatus.PENDING
    created_at: datetime = None
    started_at: datetime = None
    completed_at: datetime = None
    retry_count: int = 0
    max_retries: int = 3
    last_error: str = None
    analysis_result: Dict = None
    next_run_time: datetime = None
    layer_interval_minutes: int = None  # 该层级的监控间隔
    last_analysis_time: datetime = None  # 上次分析时间

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.layer_interval_minutes is None:
            self.layer_interval_minutes = self._get_default_interval()

    def _get_default_interval(self) -> int:
        """获取默认监控间隔（分钟）"""
        intervals = {
            AnalysisLayer.STRATEGIC: 24 * 60,      # 24小时
            AnalysisLayer.TACTICAL: 4 * 60,        # 4小时
            AnalysisLayer.EXECUTION: 1 * 60,       # 1小时
            AnalysisLayer.SUPPLY_DEMAND: 30        # 30分钟
        }
        return intervals.get(self.analysis_layer, 60)

    def is_due_for_analysis(self) -> bool:
        """检查是否到了分析时间"""
        if self.last_analysis_time is None:
            return True

        time_since_last = datetime.now() - self.last_analysis_time
        return time_since_last.total_seconds() >= (self.layer_interval_minutes * 60)

    def get_next_analysis_time(self) -> datetime:
        """获取下次分析时间"""
        if self.last_analysis_time is None:
            return datetime.now()
        return self.last_analysis_time + timedelta(minutes=self.layer_interval_minutes)


@dataclass
class MonitoringTask:
    """监控任务 - 保持向后兼容"""
    task_id: str
    item_id: str
    item_name: str
    market_hash_name: str
    url: str
    priority: MonitoringPriority
    source: str
    status: MonitoringStatus = MonitoringStatus.PENDING
    created_at: datetime = None
    started_at: datetime = None
    completed_at: datetime = None
    retry_count: int = 0
    max_retries: int = 3
    last_error: str = None
    analysis_result: Dict = None
    next_run_time: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


class IntelligentMonitoringScheduler:
    """智能监控调度系统"""
    
    def __init__(self, data_base_path: str = "data/scraped_data"):
        """
        初始化智能监控调度系统
        
        Args:
            data_base_path: 数据基础路径
        """
        self.data_base_path = data_base_path
        self.logger = logger.bind(scheduler=self.__class__.__name__)
        
        # 调度器状态
        self.is_running = False
        self.scheduler_task = None
        
        # 任务队列和状态
        self.task_queue: List[MonitoringTask] = []
        self.running_tasks: Dict[str, MonitoringTask] = {}
        self.completed_tasks: Dict[str, MonitoringTask] = {}
        self.failed_tasks: Dict[str, MonitoringTask] = {}

        # 分层监控任务队列
        self.layered_task_queues: Dict[AnalysisLayer, List[LayeredMonitoringTask]] = {
            AnalysisLayer.STRATEGIC: [],
            AnalysisLayer.TACTICAL: [],
            AnalysisLayer.EXECUTION: [],
            AnalysisLayer.SUPPLY_DEMAND: []
        }
        self.layered_running_tasks: Dict[str, LayeredMonitoringTask] = {}
        self.layered_completed_tasks: List[LayeredMonitoringTask] = []
        
        # 配置参数
        self.config = {
            'base_interval_minutes': 60,        # 基础监控间隔60分钟
            'max_concurrent_tasks': 3,          # 最大并发任务数
            'task_timeout_minutes': 15,         # 任务超时时间15分钟
            'retry_delay_minutes': 5,           # 重试延迟5分钟
            'priority_multipliers': {           # 优先级频率倍数
                MonitoringPriority.CRITICAL: 2.0,  # 关键任务频率x2
                MonitoringPriority.HIGH: 1.5,      # 高优先级频率x1.5
                MonitoringPriority.MEDIUM: 1.0,    # 中优先级基础频率
                MonitoringPriority.LOW: 0.5        # 低优先级频率x0.5
            },
            'adaptive_thresholds': {            # 自适应阈值
                'volatility_threshold': 0.05,   # 波动率阈值5%
                'volume_change_threshold': 0.3, # 成交量变化阈值30%
                'price_change_threshold': 0.1   # 价格变化阈值10%
            }
        }

        # 分层监控配置
        self.layered_config = {
            'layer_intervals': {                # 各层级监控间隔（分钟）
                AnalysisLayer.STRATEGIC: 24 * 60,      # 战略分析：24小时
                AnalysisLayer.TACTICAL: 4 * 60,        # 战术分析：4小时
                AnalysisLayer.EXECUTION: 1 * 60,       # 执行分析：1小时
                AnalysisLayer.SUPPLY_DEMAND: 30        # 供需分析：30分钟
            },
            'layer_priorities': {               # 各层级优先级权重
                AnalysisLayer.EXECUTION: 1.0,          # 执行分析优先级最高
                AnalysisLayer.SUPPLY_DEMAND: 0.9,      # 供需分析次之
                AnalysisLayer.TACTICAL: 0.7,           # 战术分析中等
                AnalysisLayer.STRATEGIC: 0.5           # 战略分析优先级最低
            },
            'max_concurrent_per_layer': {       # 每层级最大并发数
                AnalysisLayer.STRATEGIC: 1,            # 战略分析：1个
                AnalysisLayer.TACTICAL: 2,             # 战术分析：2个
                AnalysisLayer.EXECUTION: 3,            # 执行分析：3个
                AnalysisLayer.SUPPLY_DEMAND: 2         # 供需分析：2个
            }
        }

        # DAO实例
        try:
            self.favorite_dao = FavoriteDAO()
            self.holding_dao = HoldingDAO()
            self.arbitrage_dao = ArbitrageItemDAO()
            self.item_dao = ItemDAO()
            self.logger.info("DAO模块加载成功")
        except Exception as e:
            self.logger.warning(f"DAO模块加载失败，使用模拟DAO: {e}")
            self.favorite_dao = MockDAO()
            self.holding_dao = MockDAO()
            self.arbitrage_dao = MockDAO()
            self.item_dao = MockDAO()
        
        # 分析引擎缓存
        self.analysis_engines: Dict[str, DecisionFusionEngine] = {}
        
        # 统计信息
        self.stats = {
            'total_tasks_created': 0,
            'total_tasks_completed': 0,
            'total_tasks_failed': 0,
            'total_analysis_time': 0,
            'average_analysis_time': 0,
            'last_update_time': datetime.now()
        }
        
        self.logger.info("智能监控调度系统初始化完成")
    
    async def start(self):
        """启动智能监控调度系统"""
        if self.is_running:
            self.logger.warning("智能监控调度系统已在运行中")
            return
        
        self.is_running = True
        self.logger.info("🚀 启动智能监控调度系统")
        
        # 初始化任务队列
        await self._initialize_task_queue()
        
        # 启动调度器主循环
        self.scheduler_task = asyncio.create_task(self._scheduler_main_loop())
        
        self.logger.info("✅ 智能监控调度系统启动完成")
        
        try:
            await self.scheduler_task
        except asyncio.CancelledError:
            self.logger.info("智能监控调度系统被取消")
    
    def stop(self):
        """停止智能监控调度系统"""
        if not self.is_running:
            return
        
        self.logger.info("🛑 停止智能监控调度系统")
        self.is_running = False
        
        # 取消所有运行中的任务
        for task in self.running_tasks.values():
            task.status = MonitoringStatus.SKIPPED
        
        if self.scheduler_task and not self.scheduler_task.done():
            self.scheduler_task.cancel()
        
        self.logger.info("✅ 智能监控调度系统已停止")
    
    async def _initialize_task_queue(self):
        """初始化任务队列"""
        try:
            self.logger.info("初始化监控任务队列...")
            
            # 获取所有需要监控的饰品
            monitor_items = await self._get_items_for_monitoring()
            
            # 创建监控任务
            for item in monitor_items:
                task = self._create_monitoring_task(item)
                self.task_queue.append(task)
                self.stats['total_tasks_created'] += 1
            
            # 按优先级排序
            self.task_queue.sort(key=lambda t: t.priority.value)
            
            self.logger.info(f"任务队列初始化完成，共创建 {len(self.task_queue)} 个监控任务")
            
        except Exception as e:
            self.logger.error(f"初始化任务队列失败: {e}")
    
    async def _scheduler_main_loop(self):
        """调度器主循环"""
        self.logger.info("🔄 启动智能监控调度主循环")
        
        while self.is_running:
            try:
                # 检查并启动待执行任务
                await self._check_and_start_tasks()
                
                # 检查运行中任务状态
                await self._check_running_tasks()
                
                # 清理完成的任务
                await self._cleanup_completed_tasks()
                
                # 更新统计信息
                self._update_statistics()
                
                # 自适应调整监控频率
                await self._adaptive_frequency_adjustment()
                
                # 短暂休眠
                await asyncio.sleep(30)  # 每30秒检查一次
                
            except Exception as e:
                self.logger.error(f"调度器主循环异常: {e}")
                await asyncio.sleep(60)  # 异常时等待1分钟
    
    async def _check_and_start_tasks(self):
        """检查并启动待执行任务"""
        try:
            current_time = datetime.now()
            
            # 获取可以启动的任务
            ready_tasks = [
                task for task in self.task_queue
                if (task.status == MonitoringStatus.PENDING and
                    (task.next_run_time is None or task.next_run_time <= current_time) and
                    len(self.running_tasks) < self.config['max_concurrent_tasks'])
            ]
            
            # 按优先级排序
            ready_tasks.sort(key=lambda t: t.priority.value)
            
            # 启动任务
            for task in ready_tasks[:self.config['max_concurrent_tasks'] - len(self.running_tasks)]:
                await self._start_monitoring_task(task)
                
        except Exception as e:
            self.logger.error(f"检查并启动任务失败: {e}")
    
    async def _start_monitoring_task(self, task: MonitoringTask):
        """启动监控任务"""
        try:
            task.status = MonitoringStatus.RUNNING
            task.started_at = datetime.now()
            self.running_tasks[task.task_id] = task
            
            # 从队列中移除
            if task in self.task_queue:
                self.task_queue.remove(task)
            
            self.logger.info(f"🚀 启动监控任务: {task.item_name} (优先级: {task.priority.name})")
            
            # 异步执行分析
            asyncio.create_task(self._execute_analysis_task(task))
            
        except Exception as e:
            self.logger.error(f"启动监控任务失败: {e}")
            task.status = MonitoringStatus.FAILED
            task.last_error = str(e)
    
    async def _execute_analysis_task(self, task: MonitoringTask):
        """执行分析任务"""
        try:
            # 获取或创建分析引擎
            analysis_engine = self._get_analysis_engine(task.item_id)
            
            # 执行智能分析
            start_time = datetime.now()
            
            analysis_result = await asyncio.wait_for(
                self._run_intelligent_analysis(analysis_engine, task),
                timeout=self.config['task_timeout_minutes'] * 60
            )
            
            end_time = datetime.now()
            analysis_duration = (end_time - start_time).total_seconds()
            
            # 更新任务状态
            task.status = MonitoringStatus.COMPLETED
            task.completed_at = end_time
            task.analysis_result = analysis_result
            
            # 移动到完成队列
            self.running_tasks.pop(task.task_id, None)
            self.completed_tasks[task.task_id] = task
            
            # 更新统计
            self.stats['total_tasks_completed'] += 1
            self.stats['total_analysis_time'] += analysis_duration
            
            # 计算下次运行时间
            task.next_run_time = self._calculate_next_run_time(task, analysis_result)
            
            # 重新加入队列
            task.status = MonitoringStatus.PENDING
            task.retry_count = 0
            self.task_queue.append(task)
            
            self.logger.info(f"✅ 监控任务完成: {task.item_name} (耗时: {analysis_duration:.1f}秒)")
            
        except asyncio.TimeoutError:
            await self._handle_task_timeout(task)
        except Exception as e:
            await self._handle_task_error(task, e)
    
    async def _run_intelligent_analysis(self, analysis_engine: DecisionFusionEngine, task: MonitoringTask) -> Dict:
        """运行智能分析"""
        try:
            # 执行决策融合分析
            fusion_result = analysis_engine.execute_intelligent_fusion()
            
            if fusion_result.get('success', False):
                return {
                    'success': True,
                    'fusion_result': fusion_result,
                    'analysis_timestamp': datetime.now(),
                    'task_info': {
                        'item_id': task.item_id,
                        'item_name': task.item_name,
                        'priority': task.priority.name,
                        'source': task.source
                    }
                }
            else:
                return {
                    'success': False,
                    'error': fusion_result.get('error', '分析失败'),
                    'analysis_timestamp': datetime.now()
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'analysis_timestamp': datetime.now()
            }

    async def _handle_task_timeout(self, task: MonitoringTask):
        """处理任务超时"""
        try:
            task.retry_count += 1
            task.last_error = f"任务超时 (第{task.retry_count}次重试)"

            if task.retry_count <= task.max_retries:
                # 重新调度
                task.status = MonitoringStatus.PENDING
                task.next_run_time = datetime.now() + timedelta(minutes=self.config['retry_delay_minutes'])
                self.task_queue.append(task)

                self.logger.warning(f"⚠️ 任务超时，将重试: {task.item_name} (第{task.retry_count}次)")
            else:
                # 超过最大重试次数
                task.status = MonitoringStatus.FAILED
                self.failed_tasks[task.task_id] = task
                self.stats['total_tasks_failed'] += 1

                self.logger.error(f"❌ 任务超时失败: {task.item_name} (已重试{task.max_retries}次)")

            # 从运行队列移除
            self.running_tasks.pop(task.task_id, None)

        except Exception as e:
            self.logger.error(f"处理任务超时异常: {e}")

    async def _handle_task_error(self, task: MonitoringTask, error: Exception):
        """处理任务错误"""
        try:
            task.retry_count += 1
            task.last_error = str(error)

            if task.retry_count <= task.max_retries:
                # 重新调度
                task.status = MonitoringStatus.PENDING
                task.next_run_time = datetime.now() + timedelta(minutes=self.config['retry_delay_minutes'])
                self.task_queue.append(task)

                self.logger.warning(f"⚠️ 任务执行异常，将重试: {task.item_name} - {error} (第{task.retry_count}次)")
            else:
                # 超过最大重试次数
                task.status = MonitoringStatus.FAILED
                self.failed_tasks[task.task_id] = task
                self.stats['total_tasks_failed'] += 1

                self.logger.error(f"❌ 任务执行失败: {task.item_name} - {error} (已重试{task.max_retries}次)")

            # 从运行队列移除
            self.running_tasks.pop(task.task_id, None)

        except Exception as e:
            self.logger.error(f"处理任务错误异常: {e}")

    async def _check_running_tasks(self):
        """检查运行中任务状态"""
        try:
            current_time = datetime.now()
            timeout_minutes = self.config['task_timeout_minutes']

            timeout_tasks = []
            for task in self.running_tasks.values():
                if task.started_at and (current_time - task.started_at).total_seconds() > timeout_minutes * 60:
                    timeout_tasks.append(task)

            # 处理超时任务
            for task in timeout_tasks:
                await self._handle_task_timeout(task)

        except Exception as e:
            self.logger.error(f"检查运行中任务状态失败: {e}")

    async def _cleanup_completed_tasks(self):
        """清理完成的任务"""
        try:
            current_time = datetime.now()
            cleanup_hours = 24  # 24小时后清理

            # 清理完成的任务
            expired_completed = [
                task_id for task_id, task in self.completed_tasks.items()
                if task.completed_at and (current_time - task.completed_at).total_seconds() > cleanup_hours * 3600
            ]

            for task_id in expired_completed:
                self.completed_tasks.pop(task_id, None)

            # 清理失败的任务
            expired_failed = [
                task_id for task_id, task in self.failed_tasks.items()
                if task.started_at and (current_time - task.started_at).total_seconds() > cleanup_hours * 3600
            ]

            for task_id in expired_failed:
                self.failed_tasks.pop(task_id, None)

            if expired_completed or expired_failed:
                self.logger.info(f"清理过期任务: 完成任务 {len(expired_completed)} 个, 失败任务 {len(expired_failed)} 个")

        except Exception as e:
            self.logger.error(f"清理完成任务失败: {e}")

    def _update_statistics(self):
        """更新统计信息"""
        try:
            if self.stats['total_tasks_completed'] > 0:
                self.stats['average_analysis_time'] = self.stats['total_analysis_time'] / self.stats['total_tasks_completed']

            self.stats['last_update_time'] = datetime.now()

        except Exception as e:
            self.logger.error(f"更新统计信息失败: {e}")

    async def _adaptive_frequency_adjustment(self):
        """自适应频率调整"""
        try:
            # 基于分析结果调整监控频率
            for task in list(self.task_queue):
                if task.analysis_result and task.analysis_result.get('success'):
                    adjustment_factor = self._calculate_frequency_adjustment(task.analysis_result)

                    if adjustment_factor != 1.0:
                        # 调整下次运行时间
                        if task.next_run_time:
                            base_interval = self.config['base_interval_minutes']
                            priority_multiplier = self.config['priority_multipliers'][task.priority]
                            adjusted_interval = base_interval / (priority_multiplier * adjustment_factor)

                            task.next_run_time = datetime.now() + timedelta(minutes=adjusted_interval)

                            self.logger.debug(f"调整监控频率: {task.item_name}, 调整因子: {adjustment_factor:.2f}")

        except Exception as e:
            self.logger.error(f"自适应频率调整失败: {e}")

    def _calculate_frequency_adjustment(self, analysis_result: Dict) -> float:
        """计算频率调整因子"""
        try:
            fusion_result = analysis_result.get('fusion_result', {})
            final_decision = fusion_result.get('final_decision', {})

            # 基于推荐类型调整
            recommendation_type = final_decision.get('recommendation_type', 'HOLD')
            confidence_level = final_decision.get('confidence_level', 50)
            risk_level = final_decision.get('risk_level', 'MEDIUM')

            adjustment_factor = 1.0

            # 强烈推荐时增加监控频率
            if recommendation_type in ['STRONG_BUY', 'STRONG_SELL']:
                adjustment_factor *= 2.0
            elif recommendation_type in ['BUY', 'SELL']:
                adjustment_factor *= 1.5

            # 高置信度时增加监控频率
            if confidence_level > 80:
                adjustment_factor *= 1.3
            elif confidence_level < 40:
                adjustment_factor *= 0.8

            # 高风险时增加监控频率
            if risk_level == 'HIGH':
                adjustment_factor *= 1.5
            elif risk_level == 'LOW':
                adjustment_factor *= 0.8

            return min(3.0, max(0.3, adjustment_factor))  # 限制在0.3-3.0倍之间

        except Exception as e:
            self.logger.error(f"计算频率调整因子失败: {e}")
            return 1.0

    def _calculate_next_run_time(self, task: MonitoringTask, analysis_result: Dict) -> datetime:
        """计算下次运行时间"""
        try:
            base_interval = self.config['base_interval_minutes']
            priority_multiplier = self.config['priority_multipliers'][task.priority]

            # 自适应调整
            adjustment_factor = self._calculate_frequency_adjustment(analysis_result)

            # 计算实际间隔
            actual_interval = base_interval / (priority_multiplier * adjustment_factor)

            return datetime.now() + timedelta(minutes=actual_interval)

        except Exception as e:
            self.logger.error(f"计算下次运行时间失败: {e}")
            return datetime.now() + timedelta(minutes=self.config['base_interval_minutes'])

    def _get_analysis_engine(self, item_id: str) -> DecisionFusionEngine:
        """获取或创建分析引擎"""
        try:
            if item_id not in self.analysis_engines:
                self.analysis_engines[item_id] = DecisionFusionEngine(item_id, self.data_base_path)

            return self.analysis_engines[item_id]

        except Exception as e:
            self.logger.error(f"获取分析引擎失败: {e}")
            # 返回默认引擎
            return DecisionFusionEngine(item_id, self.data_base_path)

    async def _get_items_for_monitoring(self) -> List[Dict[str, Any]]:
        """获取需要监控的饰品（基于现有RealtimeOnlyScheduler逻辑）"""
        try:
            # 获取收藏饰品
            favorites = await self._get_favorites_for_monitor()

            # 获取持仓饰品
            holdings = await self._get_holdings_for_monitor()

            # 获取搬砖饰品
            arbitrage_items = await self._get_arbitrage_items_for_monitor()

            # 合并并去重
            seen_items = set()
            monitor_items = []

            # 先添加持仓饰品（优先级最高）
            for holding in holdings:
                item_id = holding.get('item_id')
                hash_name = holding.get('market_hash_name')
                unique_key = item_id or hash_name
                if unique_key and unique_key not in seen_items:
                    seen_items.add(unique_key)
                    holding['priority'] = MonitoringPriority.CRITICAL
                    monitor_items.append(holding)

            # 再添加收藏饰品
            for favorite in favorites:
                item_id = favorite.get('item_id')
                hash_name = favorite.get('market_hash_name')
                unique_key = item_id or hash_name
                if unique_key and unique_key not in seen_items:
                    seen_items.add(unique_key)
                    favorite['priority'] = MonitoringPriority.HIGH
                    monitor_items.append(favorite)

            # 最后添加搬砖饰品
            for arbitrage in arbitrage_items:
                item_id = arbitrage.get('item_id')
                hash_name = arbitrage.get('market_hash_name')
                unique_key = item_id or hash_name
                if unique_key and unique_key not in seen_items:
                    seen_items.add(unique_key)
                    arbitrage['priority'] = MonitoringPriority.MEDIUM
                    monitor_items.append(arbitrage)

            self.logger.info(f"获取监控饰品完成: 持仓 {len(holdings)} 个, 收藏 {len(favorites)} 个, 搬砖 {len(arbitrage_items)} 个, 去重后 {len(monitor_items)} 个")

            return monitor_items

        except Exception as e:
            self.logger.error(f"获取监控饰品失败: {e}")
            return []

    async def _get_favorites_for_monitor(self) -> List[Dict[str, Any]]:
        """获取收藏饰品（基于现有逻辑）"""
        try:
            default_user_id = "default_user"
            favorites = self.favorite_dao.get_user_favorites(default_user_id, limit=1000)

            if not favorites:
                return []

            monitor_list = []
            for fav in favorites:
                try:
                    item_id = fav.get('item_id', '')
                    item_name = fav.get('item_name', '')

                    if not item_id:
                        continue

                    item = self.item_dao.get_by_item_id(item_id)
                    if not item:
                        continue

                    market_hash_name = item.get('market_hash_name')
                    if not market_hash_name:
                        continue

                    import urllib.parse
                    encoded_name = urllib.parse.quote(market_hash_name, safe='')
                    url = f"https://steamdt.com/cs2/{encoded_name}"

                    monitor_list.append({
                        'id': f"favorite_{fav['id']}",
                        'name': item_name or item.get('name', '未知饰品'),
                        'url': url,
                        'item_id': item_id,
                        'market_hash_name': market_hash_name,
                        'source': 'favorite'
                    })

                except Exception as item_error:
                    self.logger.warning(f"处理收藏饰品失败: {item_error}")
                    continue

            return monitor_list

        except Exception as e:
            self.logger.error(f"获取收藏饰品失败: {e}")
            return []

    async def _get_holdings_for_monitor(self) -> List[Dict[str, Any]]:
        """获取持仓饰品（基于现有逻辑）"""
        try:
            default_user_id = "default_user"
            holdings = self.holding_dao.get_user_holdings(default_user_id, limit=1000)

            if not holdings:
                return []

            monitor_list = []
            for holding in holdings:
                try:
                    item_id = holding.get('item_id', '')
                    item_name = holding.get('item_name', '')

                    if not item_id:
                        continue

                    item = self.item_dao.get_by_item_id(item_id)
                    if not item:
                        continue

                    market_hash_name = item.get('market_hash_name')
                    if not market_hash_name:
                        continue

                    import urllib.parse
                    encoded_name = urllib.parse.quote(market_hash_name, safe='')
                    url = f"https://steamdt.com/cs2/{encoded_name}"

                    monitor_list.append({
                        'id': f"holding_{holding['id']}",
                        'name': item_name or item.get('name', '未知饰品'),
                        'url': url,
                        'item_id': item_id,
                        'market_hash_name': market_hash_name,
                        'source': 'holding',
                        'quantity': holding.get('quantity', 0),
                        'avg_cost': holding.get('avg_cost', 0)
                    })

                except Exception as item_error:
                    self.logger.warning(f"处理持仓饰品失败: {item_error}")
                    continue

            return monitor_list

        except Exception as e:
            self.logger.error(f"获取持仓饰品失败: {e}")
            return []

    async def _get_arbitrage_items_for_monitor(self) -> List[Dict[str, Any]]:
        """获取搬砖饰品（基于现有逻辑）"""
        try:
            arbitrage_items = self.arbitrage_dao.get_top_profit_items(limit=50)

            if not arbitrage_items:
                return []

            monitor_list = []
            for item in arbitrage_items:
                try:
                    market_hash_name = getattr(item, 'market_name', None)
                    if not market_hash_name:
                        continue

                    import urllib.parse
                    encoded_name = urllib.parse.quote(market_hash_name, safe='')
                    url = f"https://steamdt.com/cs2/{encoded_name}"

                    monitor_list.append({
                        'item_id': getattr(item, 'item_id', ''),
                        'market_hash_name': market_hash_name,
                        'name': market_hash_name,
                        'url': url,
                        'source': 'arbitrage',
                        'profit_rate': float(item.youpin_profit_rate) if item.youpin_profit_rate else 0,
                        'profit': float(item.youpin_profit) if item.youpin_profit else 0
                    })

                except Exception as e:
                    self.logger.warning(f"处理搬砖饰品失败: {e}")
                    continue

            return monitor_list

        except Exception as e:
            self.logger.error(f"获取搬砖饰品失败: {e}")
            return []

    def _create_monitoring_task(self, item: Dict[str, Any]) -> MonitoringTask:
        """创建监控任务"""
        try:
            task_id = f"{item['source']}_{item['item_id']}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            return MonitoringTask(
                task_id=task_id,
                item_id=item['item_id'],
                item_name=item['name'],
                market_hash_name=item['market_hash_name'],
                url=item['url'],
                priority=item['priority'],
                source=item['source']
            )

        except Exception as e:
            self.logger.error(f"创建监控任务失败: {e}")
            return None

    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        try:
            return {
                'is_running': self.is_running,
                'config': self.config,
                'queue_status': {
                    'pending_tasks': len(self.task_queue),
                    'running_tasks': len(self.running_tasks),
                    'completed_tasks': len(self.completed_tasks),
                    'failed_tasks': len(self.failed_tasks)
                },
                'statistics': self.stats,
                'analysis_engines': len(self.analysis_engines)
            }

        except Exception as e:
            self.logger.error(f"获取调度器状态失败: {e}")
            return {'error': str(e)}

    def get_task_details(self, task_id: str = None) -> Dict[str, Any]:
        """获取任务详情"""
        try:
            if task_id:
                # 查找特定任务
                for task_dict in [self.running_tasks, self.completed_tasks, self.failed_tasks]:
                    if task_id in task_dict:
                        return asdict(task_dict[task_id])

                # 在队列中查找
                for task in self.task_queue:
                    if task.task_id == task_id:
                        return asdict(task)

                return {'error': f'任务 {task_id} 未找到'}
            else:
                # 返回所有任务概览
                all_tasks = []

                # 队列中的任务
                for task in self.task_queue:
                    all_tasks.append({
                        'task_id': task.task_id,
                        'item_name': task.item_name,
                        'priority': task.priority.name,
                        'status': task.status.value,
                        'next_run_time': task.next_run_time.isoformat() if task.next_run_time else None
                    })

                # 运行中的任务
                for task in self.running_tasks.values():
                    all_tasks.append({
                        'task_id': task.task_id,
                        'item_name': task.item_name,
                        'priority': task.priority.name,
                        'status': task.status.value,
                        'started_at': task.started_at.isoformat() if task.started_at else None
                    })

                return {'tasks': all_tasks}

        except Exception as e:
            self.logger.error(f"获取任务详情失败: {e}")
            return {'error': str(e)}

    def add_layered_monitoring_task(self, item_id: str, item_name: str, market_hash_name: str,
                                  url: str, priority: MonitoringPriority, analysis_layer: AnalysisLayer,
                                  source: str = "manual") -> str:
        """
        添加分层监控任务

        Args:
            item_id: 饰品ID
            item_name: 饰品名称
            market_hash_name: 市场哈希名称
            url: 监控URL
            priority: 监控优先级
            analysis_layer: 分析层级
            source: 任务来源

        Returns:
            str: 任务ID
        """
        try:
            task_id = f"layered_{analysis_layer.value}_{item_id}_{int(datetime.now().timestamp())}"

            task = LayeredMonitoringTask(
                task_id=task_id,
                item_id=item_id,
                item_name=item_name,
                market_hash_name=market_hash_name,
                url=url,
                priority=priority,
                analysis_layer=analysis_layer,
                source=source
            )

            # 添加到对应层级的队列
            self.layered_task_queues[analysis_layer].append(task)

            # 按优先级排序
            self._sort_layered_queue(analysis_layer)

            self.logger.info(f"添加分层监控任务: {task_id}, 层级: {analysis_layer.value}, 优先级: {priority.name}")
            return task_id

        except Exception as e:
            self.logger.error(f"添加分层监控任务失败: {e}")
            raise

    def add_item_to_layered_monitoring(self, item_id: str, priority: MonitoringPriority = MonitoringPriority.MEDIUM) -> Dict[str, str]:
        """
        将饰品添加到所有层级的监控

        Args:
            item_id: 饰品ID
            priority: 监控优先级

        Returns:
            Dict[str, str]: 各层级的任务ID
        """
        try:
            # 获取饰品信息
            item_info = self.item_dao.get_by_id(item_id)
            if not item_info:
                raise ValueError(f"饰品不存在: {item_id}")

            task_ids = {}

            # 为每个分析层级创建监控任务
            for layer in AnalysisLayer:
                task_id = self.add_layered_monitoring_task(
                    item_id=item_id,
                    item_name=item_info.get('name', ''),
                    market_hash_name=item_info.get('market_hash_name', ''),
                    url=item_info.get('url', ''),
                    priority=priority,
                    analysis_layer=layer,
                    source="layered_monitoring"
                )
                task_ids[layer.value] = task_id

            self.logger.info(f"饰品 {item_id} 已添加到所有层级监控")
            return task_ids

        except Exception as e:
            self.logger.error(f"添加饰品到分层监控失败: {e}")
            raise

    def _sort_layered_queue(self, layer: AnalysisLayer):
        """对指定层级的任务队列按优先级排序"""
        try:
            queue = self.layered_task_queues[layer]

            # 按优先级和创建时间排序
            queue.sort(key=lambda task: (
                task.priority.value,  # 优先级数值越小优先级越高
                task.created_at       # 创建时间越早优先级越高
            ))

        except Exception as e:
            self.logger.error(f"排序分层队列失败: {e}")

    def get_next_layered_task(self) -> Optional[LayeredMonitoringTask]:
        """
        获取下一个需要执行的分层监控任务

        Returns:
            Optional[LayeredMonitoringTask]: 下一个任务，如果没有则返回None
        """
        try:
            current_time = datetime.now()
            best_task = None
            best_score = -1
            best_layer = None

            # 遍历所有层级，找到最优任务
            for layer in AnalysisLayer:
                queue = self.layered_task_queues[layer]

                # 检查该层级是否已达到最大并发数
                running_count = sum(1 for task in self.layered_running_tasks.values()
                                  if task.analysis_layer == layer)
                max_concurrent = self.layered_config['max_concurrent_per_layer'][layer]

                if running_count >= max_concurrent:
                    continue

                # 找到该层级中到期且优先级最高的任务
                for task in queue:
                    if task.status != MonitoringStatus.PENDING:
                        continue

                    if not task.is_due_for_analysis():
                        continue

                    # 计算任务评分（优先级 + 层级权重 + 延迟惩罚）
                    priority_score = (5 - task.priority.value) / 4  # 0.25-1.0
                    layer_weight = self.layered_config['layer_priorities'][layer]

                    # 延迟惩罚：超过预定时间越久，分数越高
                    if task.last_analysis_time:
                        delay_minutes = (current_time - task.get_next_analysis_time()).total_seconds() / 60
                        delay_bonus = max(0, delay_minutes / 60)  # 每小时延迟+1分
                    else:
                        delay_bonus = 1.0  # 首次执行有额外分数

                    score = priority_score * layer_weight + delay_bonus

                    if score > best_score:
                        best_score = score
                        best_task = task
                        best_layer = layer

            # 从队列中移除选中的任务
            if best_task and best_layer:
                self.layered_task_queues[best_layer].remove(best_task)
                self.logger.info(f"选择分层任务: {best_task.task_id}, 层级: {best_layer.value}, 评分: {best_score:.2f}")

            return best_task

        except Exception as e:
            self.logger.error(f"获取下一个分层任务失败: {e}")
            return None

    async def execute_layered_task(self, task: LayeredMonitoringTask) -> bool:
        """
        执行分层监控任务

        Args:
            task: 分层监控任务

        Returns:
            bool: 执行是否成功
        """
        try:
            task.status = MonitoringStatus.RUNNING
            task.started_at = datetime.now()
            self.layered_running_tasks[task.task_id] = task

            self.logger.info(f"开始执行分层任务: {task.task_id}, 层级: {task.analysis_layer.value}")

            # 根据分析层级选择相应的分析器
            analysis_result = await self._execute_layer_analysis(task)

            if analysis_result:
                task.status = MonitoringStatus.COMPLETED
                task.completed_at = datetime.now()
                task.analysis_result = analysis_result
                task.last_analysis_time = datetime.now()
                task.retry_count = 0

                # 移动到完成队列
                self.layered_completed_tasks.append(task)

                # 保存分析结果
                await self._save_layered_analysis_result(task, analysis_result)

                self.logger.info(f"分层任务执行成功: {task.task_id}")
                return True
            else:
                task.status = MonitoringStatus.FAILED
                task.retry_count += 1
                task.last_error = "分析结果为空"

                # 如果还有重试机会，重新加入队列
                if task.retry_count < task.max_retries:
                    task.status = MonitoringStatus.PENDING
                    task.next_run_time = datetime.now() + timedelta(minutes=self.config['retry_delay_minutes'])
                    self.layered_task_queues[task.analysis_layer].append(task)
                    self._sort_layered_queue(task.analysis_layer)

                self.logger.warning(f"分层任务执行失败: {task.task_id}, 重试次数: {task.retry_count}")
                return False

        except Exception as e:
            task.status = MonitoringStatus.FAILED
            task.last_error = str(e)
            task.retry_count += 1

            self.logger.error(f"分层任务执行异常: {task.task_id}, 错误: {e}")
            return False

        finally:
            # 从运行队列中移除
            if task.task_id in self.layered_running_tasks:
                del self.layered_running_tasks[task.task_id]

    async def _execute_layer_analysis(self, task: LayeredMonitoringTask) -> Optional[Dict]:
        """
        执行指定层级的分析

        Args:
            task: 分层监控任务

        Returns:
            Optional[Dict]: 分析结果
        """
        try:
            # 获取数据
            data_result = await self._fetch_item_data(task.url)
            if not data_result:
                return None

            # 根据分析层级执行相应的分析
            if task.analysis_layer == AnalysisLayer.STRATEGIC:
                return await self._execute_strategic_analysis(task, data_result)
            elif task.analysis_layer == AnalysisLayer.TACTICAL:
                return await self._execute_tactical_analysis(task, data_result)
            elif task.analysis_layer == AnalysisLayer.EXECUTION:
                return await self._execute_execution_analysis(task, data_result)
            elif task.analysis_layer == AnalysisLayer.SUPPLY_DEMAND:
                return await self._execute_supply_demand_analysis(task, data_result)
            else:
                self.logger.error(f"未知的分析层级: {task.analysis_layer}")
                return None

        except Exception as e:
            self.logger.error(f"执行层级分析失败: {e}")
            return None

    async def _execute_strategic_analysis(self, task: LayeredMonitoringTask, data: Dict) -> Optional[Dict]:
        """执行战略分析"""
        try:
            # 这里调用重构后的战略分析器
            from cs2_investment.fx.intelligent_analysis.strategies.strategic_analyzer import StrategicAnalyzer

            # 准备周K数据
            weekly_data = self._prepare_weekly_data(data)
            if weekly_data.empty:
                return None

            analyzer = StrategicAnalyzer(weekly_data)
            result = analyzer.execute_comprehensive_strategic_analysis()

            return {
                'analysis_type': 'strategic',
                'analysis_layer': task.analysis_layer.value,
                'item_id': task.item_id,
                'analysis_result': result,
                'analysis_timestamp': datetime.now(),
                'next_analysis_time': task.get_next_analysis_time()
            }

        except Exception as e:
            self.logger.error(f"战略分析执行失败: {e}")
            return None

    async def _execute_tactical_analysis(self, task: LayeredMonitoringTask, data: Dict) -> Optional[Dict]:
        """执行战术分析"""
        try:
            # 这里调用重构后的战术分析器
            from cs2_investment.fx.intelligent_analysis.strategies.tactical_analyzer import TacticalAnalyzer

            # 准备日K和周K数据
            daily_data = self._prepare_daily_data(data)
            weekly_data = self._prepare_weekly_data(data)

            if daily_data.empty:
                return None

            analyzer = TacticalAnalyzer(daily_data, weekly_data)
            result = analyzer.execute_comprehensive_tactical_analysis()

            return {
                'analysis_type': 'tactical',
                'analysis_layer': task.analysis_layer.value,
                'item_id': task.item_id,
                'analysis_result': result,
                'analysis_timestamp': datetime.now(),
                'next_analysis_time': task.get_next_analysis_time()
            }

        except Exception as e:
            self.logger.error(f"战术分析执行失败: {e}")
            return None

    async def _execute_execution_analysis(self, task: LayeredMonitoringTask, data: Dict) -> Optional[Dict]:
        """执行执行分析"""
        try:
            # 这里调用重构后的执行分析器
            from cs2_investment.fx.intelligent_analysis.strategies.execution_analyzer import ExecutionAnalyzer

            # 准备时K和日K数据
            hourly_data = self._prepare_hourly_data(data)
            daily_data = self._prepare_daily_data(data)

            if hourly_data.empty:
                return None

            analyzer = ExecutionAnalyzer(hourly_data, daily_data)
            result = analyzer.execute_comprehensive_execution_analysis()

            return {
                'analysis_type': 'execution',
                'analysis_layer': task.analysis_layer.value,
                'item_id': task.item_id,
                'analysis_result': result,
                'analysis_timestamp': datetime.now(),
                'next_analysis_time': task.get_next_analysis_time()
            }

        except Exception as e:
            self.logger.error(f"执行分析执行失败: {e}")
            return None

    async def _execute_supply_demand_analysis(self, task: LayeredMonitoringTask, data: Dict) -> Optional[Dict]:
        """执行供需分析"""
        try:
            # 这里调用重构后的供需分析器
            from cs2_investment.fx.intelligent_analysis.indicators.supply_demand_analyzer import SupplyDemandAnalyzer

            # 准备供需数据
            supply_demand_data = self._prepare_supply_demand_data(data)
            if not supply_demand_data:
                return None

            analyzer = SupplyDemandAnalyzer(supply_demand_data)
            result = analyzer.execute_comprehensive_supply_demand_analysis()

            return {
                'analysis_type': 'supply_demand',
                'analysis_layer': task.analysis_layer.value,
                'item_id': task.item_id,
                'analysis_result': result,
                'analysis_timestamp': datetime.now(),
                'next_analysis_time': task.get_next_analysis_time()
            }

        except Exception as e:
            self.logger.error(f"供需分析执行失败: {e}")
            return None

    async def _save_layered_analysis_result(self, task: LayeredMonitoringTask, result: Dict):
        """保存分层分析结果"""
        try:
            # 保存到数据库
            analysis_record = {
                'item_id': task.item_id,
                'analysis_type': task.analysis_layer.value,
                'analysis_result': result,
                'created_at': datetime.now(),
                'task_id': task.task_id
            }

            # 这里可以调用DAO保存结果
            # self.analysis_dao.save_analysis_result(analysis_record)

            self.logger.info(f"分层分析结果已保存: {task.task_id}")

        except Exception as e:
            self.logger.error(f"保存分层分析结果失败: {e}")

    def get_layered_monitoring_status(self) -> Dict[str, Any]:
        """
        获取分层监控状态

        Returns:
            Dict[str, Any]: 分层监控状态信息
        """
        try:
            status = {
                'timestamp': datetime.now(),
                'layers': {}
            }

            for layer in AnalysisLayer:
                queue_size = len(self.layered_task_queues[layer])
                running_count = sum(1 for task in self.layered_running_tasks.values()
                                  if task.analysis_layer == layer)

                # 计算下次执行时间
                next_due_tasks = [task for task in self.layered_task_queues[layer]
                                if task.status == MonitoringStatus.PENDING]
                next_execution_time = None
                if next_due_tasks:
                    next_execution_time = min(task.get_next_analysis_time() for task in next_due_tasks)

                status['layers'][layer.value] = {
                    'interval_minutes': self.layered_config['layer_intervals'][layer],
                    'queue_size': queue_size,
                    'running_count': running_count,
                    'max_concurrent': self.layered_config['max_concurrent_per_layer'][layer],
                    'priority_weight': self.layered_config['layer_priorities'][layer],
                    'next_execution_time': next_execution_time
                }

            # 总体统计
            status['summary'] = {
                'total_queued': sum(len(queue) for queue in self.layered_task_queues.values()),
                'total_running': len(self.layered_running_tasks),
                'total_completed': len(self.layered_completed_tasks)
            }

            return status

        except Exception as e:
            self.logger.error(f"获取分层监控状态失败: {e}")
            return {'error': str(e)}

    def run_layered_monitoring_cycle(self) -> Dict[str, Any]:
        """
        运行一次分层监控周期

        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            executed_tasks = []

            # 获取并执行任务，直到没有可执行的任务或达到总并发限制
            total_running = len(self.layered_running_tasks)
            max_total_concurrent = sum(self.layered_config['max_concurrent_per_layer'].values())

            while total_running < max_total_concurrent:
                task = self.get_next_layered_task()
                if not task:
                    break

                # 异步执行任务（这里简化为同步调用）
                try:
                    # 在实际应用中，这里应该是异步执行
                    # success = await self.execute_layered_task(task)
                    # 这里暂时标记为待执行
                    task.status = MonitoringStatus.RUNNING
                    task.started_at = datetime.now()
                    self.layered_running_tasks[task.task_id] = task

                    executed_tasks.append({
                        'task_id': task.task_id,
                        'layer': task.analysis_layer.value,
                        'item_id': task.item_id,
                        'priority': task.priority.name
                    })

                    total_running += 1

                except Exception as e:
                    self.logger.error(f"启动分层任务失败: {task.task_id}, 错误: {e}")

            return {
                'executed_count': len(executed_tasks),
                'executed_tasks': executed_tasks,
                'total_running': total_running,
                'status': 'success'
            }

        except Exception as e:
            self.logger.error(f"分层监控周期执行失败: {e}")
            return {'error': str(e), 'status': 'failed'}

    def _prepare_hourly_data(self, data: Dict) -> pd.DataFrame:
        """准备时K数据"""
        try:
            # 从数据中提取时K数据
            if 'hourly_data' in data:
                return pd.DataFrame(data['hourly_data'])
            elif 'price_history' in data:
                # 从价格历史中构造时K数据
                price_history = data['price_history']
                if isinstance(price_history, list) and price_history:
                    # 简化处理：使用最近的价格数据
                    hourly_data = []
                    for i, price_point in enumerate(price_history[-168:]):  # 最近一周的数据
                        hourly_data.append({
                            'date': datetime.now() - timedelta(hours=len(price_history)-i-1),
                            'open': price_point.get('price', 0),
                            'high': price_point.get('price', 0) * 1.01,
                            'low': price_point.get('price', 0) * 0.99,
                            'close': price_point.get('price', 0),
                            'volume': price_point.get('volume', 100)
                        })
                    return pd.DataFrame(hourly_data)

            return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"准备时K数据失败: {e}")
            return pd.DataFrame()

    def _prepare_daily_data(self, data: Dict) -> pd.DataFrame:
        """准备日K数据"""
        try:
            # 从数据中提取日K数据
            if 'daily_data' in data:
                return pd.DataFrame(data['daily_data'])
            elif 'price_history' in data:
                # 从价格历史中构造日K数据
                price_history = data['price_history']
                if isinstance(price_history, list) and price_history:
                    # 简化处理：使用最近的价格数据
                    daily_data = []
                    for i, price_point in enumerate(price_history[-200:]):  # 最近200天的数据
                        daily_data.append({
                            'date': datetime.now() - timedelta(days=len(price_history)-i-1),
                            'open': price_point.get('price', 0),
                            'high': price_point.get('price', 0) * 1.02,
                            'low': price_point.get('price', 0) * 0.98,
                            'close': price_point.get('price', 0),
                            'volume': price_point.get('volume', 1000)
                        })
                    return pd.DataFrame(daily_data)

            return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"准备日K数据失败: {e}")
            return pd.DataFrame()

    def _prepare_weekly_data(self, data: Dict) -> pd.DataFrame:
        """准备周K数据"""
        try:
            # 从数据中提取周K数据
            if 'weekly_data' in data:
                return pd.DataFrame(data['weekly_data'])
            elif 'price_history' in data:
                # 从价格历史中构造周K数据
                price_history = data['price_history']
                if isinstance(price_history, list) and price_history:
                    # 简化处理：使用最近的价格数据
                    weekly_data = []
                    for i, price_point in enumerate(price_history[-100:]):  # 最近100周的数据
                        weekly_data.append({
                            'date': datetime.now() - timedelta(weeks=len(price_history)-i-1),
                            'open': price_point.get('price', 0),
                            'high': price_point.get('price', 0) * 1.05,
                            'low': price_point.get('price', 0) * 0.95,
                            'close': price_point.get('price', 0),
                            'volume': price_point.get('volume', 5000)
                        })
                    return pd.DataFrame(weekly_data)

            return pd.DataFrame()

        except Exception as e:
            self.logger.error(f"准备周K数据失败: {e}")
            return pd.DataFrame()

    def _prepare_supply_demand_data(self, data: Dict) -> Dict:
        """准备供需数据"""
        try:
            supply_demand_data = {}

            # 从数据中提取供需信息
            if 'supply_demand' in data:
                supply_demand_data = data['supply_demand']
            elif 'market_data' in data:
                # 从市场数据中构造供需数据
                market_data = data['market_data']

                # 构造3m和6m数据
                if 'orders' in market_data:
                    orders = market_data['orders']

                    # 简化处理：构造供需数据
                    supply_demand_data['3m'] = []
                    supply_demand_data['6m'] = []

                    for i in range(10):  # 3m数据
                        supply_demand_data['3m'].append({
                            'timestamp': datetime.now() - timedelta(days=i*9),
                            'supply': len([o for o in orders if o.get('type') == 'sell']),
                            'demand': len([o for o in orders if o.get('type') == 'buy']),
                            'price': data.get('current_price', 0)
                        })

                    for i in range(15):  # 6m数据
                        supply_demand_data['6m'].append({
                            'timestamp': datetime.now() - timedelta(days=i*12),
                            'supply': len([o for o in orders if o.get('type') == 'sell']) + i,
                            'demand': len([o for o in orders if o.get('type') == 'buy']) + i,
                            'price': data.get('current_price', 0)
                        })

            return supply_demand_data

        except Exception as e:
            self.logger.error(f"准备供需数据失败: {e}")
            return {}
