#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品分析指标图表生成器

基于现有分析系统生成静态图表，参考常规分析的方式。
支持技术指标图表、投资分析图表、风险评估图表等多种类型。
"""

import sys
import os
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Any
import argparse

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class AnalysisChartGenerator:
    """分析指标图表生成器"""
    
    def __init__(self):
        """初始化图表生成器"""
        self.project_root = Path(__file__).parent
        self.output_dir = self.project_root / "data" / "analysis_results"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 分析系统路径
        self.syncps_path = self.project_root / "src" / "cs2_investment" / "fx" / "syncps"
        self.ssync_path = self.project_root / "src" / "cs2_investment" / "fx" / "ssync"
        
        print(f"📊 图表生成器初始化完成")
        print(f"📁 输出目录: {self.output_dir}")
    
    def generate_professional_dashboard(self, item_name: str, data_path: str = None) -> Dict:
        """
        生成专业投资分析仪表板
        
        Args:
            item_name: 饰品名称
            data_path: 数据路径（可选）
            
        Returns:
            Dict: 生成结果
        """
        try:
            print(f"\n🎨 开始生成专业投资分析仪表板: {item_name}")
            
            # 切换到syncps系统目录
            original_cwd = os.getcwd()
            original_path = sys.path.copy()
            
            os.chdir(self.syncps_path)
            sys.path.insert(0, str(self.syncps_path))
            
            try:
                # 导入分析系统
                from main_analysis_system import CS2AnalysisSystemV2
                from professional_chart_system import ProfessionalChartSystem
                
                # 创建分析系统实例
                if data_path:
                    analyzer = CS2AnalysisSystemV2(data_path)
                else:
                    analyzer = CS2AnalysisSystemV2(item_name)
                
                # 运行完整分析
                print("📊 运行投资分析...")
                analysis_result = analyzer.run_complete_analysis()
                
                if not analysis_result:
                    return {'success': False, 'error': '投资分析失败'}
                
                # 创建图表生成系统
                chart_system = ProfessionalChartSystem(analyzer)
                
                # 生成保存路径
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                item_id = Path(data_path).name if data_path else item_name.replace('/', '_').replace('\\', '_')
                
                chart_dir = self.output_dir / str(item_id)
                chart_dir.mkdir(parents=True, exist_ok=True)
                
                chart_filename = f"{item_id}_专业投资仪表板_{timestamp}.png"
                chart_path = chart_dir / chart_filename
                
                print(f"📈 生成专业仪表板图表...")
                print(f"💾 保存路径: {chart_path}")
                
                # 生成综合仪表板
                chart_system.generate_comprehensive_dashboard(
                    save_path=str(chart_path),
                    show_chart=False
                )
                
                return {
                    'success': True,
                    'chart_path': str(chart_path),
                    'chart_type': 'professional_dashboard',
                    'item_name': item_name,
                    'timestamp': timestamp
                }
                
            finally:
                # 恢复原始工作目录和路径
                os.chdir(original_cwd)
                sys.path = original_path
                
        except Exception as e:
            print(f"❌ 专业仪表板生成失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def generate_technical_chart(self, item_name: str, data_path: str = None) -> Dict:
        """
        生成增强版技术指标图表
        
        Args:
            item_name: 饰品名称
            data_path: 数据路径（可选）
            
        Returns:
            Dict: 生成结果
        """
        try:
            print(f"\n📈 开始生成技术指标图表: {item_name}")
            
            # 切换到ssync系统目录
            original_cwd = os.getcwd()
            original_path = sys.path.copy()
            
            os.chdir(self.ssync_path)
            sys.path.insert(0, str(self.ssync_path))
            
            try:
                # 导入实时监控系统
                from real_time_monitor import FixedRealTimeMonitor
                from enhanced_chart_generator import EnhancedTechnicalChartGenerator
                
                # 创建监控实例
                if data_path:
                    monitor = FixedRealTimeMonitor(data_path)
                else:
                    monitor = FixedRealTimeMonitor(item_name)
                
                # 加载数据
                print("📊 加载技术分析数据...")
                if not monitor.load_data():
                    return {'success': False, 'error': '数据加载失败'}
                
                # 创建图表生成器
                chart_generator = EnhancedTechnicalChartGenerator(monitor)
                
                # 生成保存路径
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                item_id = Path(data_path).name if data_path else item_name.replace('/', '_').replace('\\', '_')
                
                chart_dir = self.output_dir / str(item_id)
                chart_dir.mkdir(parents=True, exist_ok=True)
                
                chart_filename = f"{item_id}_技术指标图表_{timestamp}.png"
                chart_path = chart_dir / chart_filename
                
                print(f"📈 生成技术指标图表...")
                print(f"💾 保存路径: {chart_path}")
                
                # 生成增强版图表
                chart_generator.generate_enhanced_chart(
                    save_path=str(chart_path),
                    show_chart=False
                )
                
                return {
                    'success': True,
                    'chart_path': str(chart_path),
                    'chart_type': 'technical_chart',
                    'item_name': item_name,
                    'timestamp': timestamp
                }
                
            finally:
                # 恢复原始工作目录和路径
                os.chdir(original_cwd)
                sys.path = original_path
                
        except Exception as e:
            print(f"❌ 技术指标图表生成失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def generate_all_charts(self, item_name: str, data_path: str = None) -> Dict:
        """
        生成所有类型的图表
        
        Args:
            item_name: 饰品名称
            data_path: 数据路径（可选）
            
        Returns:
            Dict: 生成结果汇总
        """
        print(f"\n🎯 开始生成所有分析图表: {item_name}")
        print("=" * 60)
        
        results = {
            'item_name': item_name,
            'data_path': data_path,
            'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S"),
            'charts': [],
            'success_count': 0,
            'error_count': 0
        }
        
        # 1. 生成专业投资仪表板
        dashboard_result = self.generate_professional_dashboard(item_name, data_path)
        results['charts'].append(dashboard_result)
        if dashboard_result['success']:
            results['success_count'] += 1
            print(f"✅ 专业投资仪表板生成成功")
        else:
            results['error_count'] += 1
            print(f"❌ 专业投资仪表板生成失败: {dashboard_result['error']}")
        
        # 2. 生成技术指标图表
        technical_result = self.generate_technical_chart(item_name, data_path)
        results['charts'].append(technical_result)
        if technical_result['success']:
            results['success_count'] += 1
            print(f"✅ 技术指标图表生成成功")
        else:
            results['error_count'] += 1
            print(f"❌ 技术指标图表生成失败: {technical_result['error']}")
        
        # 汇总结果
        results['overall_success'] = results['error_count'] == 0
        
        print("\n" + "=" * 60)
        print(f"📊 图表生成完成!")
        print(f"✅ 成功: {results['success_count']} 个")
        print(f"❌ 失败: {results['error_count']} 个")
        
        if results['success_count'] > 0:
            print(f"\n📁 图表保存目录: {self.output_dir}")
            for chart in results['charts']:
                if chart['success']:
                    print(f"   📈 {chart['chart_type']}: {chart['chart_path']}")
        
        return results
    
    def list_available_items(self) -> List[str]:
        """列出可用的饰品数据"""
        try:
            # 查找data目录下的饰品数据
            data_dir = self.project_root / "data" / "scraped_data"
            if not data_dir.exists():
                return []
            
            items = []
            for item_dir in data_dir.iterdir():
                if item_dir.is_dir():
                    items.append(item_dir.name)
            
            return sorted(items)
        except Exception as e:
            print(f"❌ 获取可用饰品列表失败: {e}")
            return []


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='CS2饰品分析指标图表生成器')
    parser.add_argument('--item', '-i', type=str, help='饰品名称或ID')
    parser.add_argument('--data-path', '-d', type=str, help='数据路径（可选）')
    parser.add_argument('--chart-type', '-t', choices=['all', 'dashboard', 'technical'], 
                       default='all', help='图表类型')
    parser.add_argument('--list', '-l', action='store_true', help='列出可用的饰品')
    
    args = parser.parse_args()
    
    # 创建图表生成器
    generator = AnalysisChartGenerator()
    
    # 列出可用饰品
    if args.list:
        items = generator.list_available_items()
        if items:
            print("📋 可用的饰品数据:")
            for i, item in enumerate(items, 1):
                print(f"   {i:2d}. {item}")
        else:
            print("❌ 未找到可用的饰品数据")
        return
    
    # 检查必需参数
    if not args.item:
        print("❌ 请指定饰品名称或ID (使用 --item 参数)")
        print("💡 使用 --list 查看可用的饰品")
        return
    
    # 生成图表
    try:
        if args.chart_type == 'all':
            result = generator.generate_all_charts(args.item, args.data_path)
        elif args.chart_type == 'dashboard':
            result = generator.generate_professional_dashboard(args.item, args.data_path)
        elif args.chart_type == 'technical':
            result = generator.generate_technical_chart(args.item, args.data_path)
        
        if result['success'] if 'success' in result else result['overall_success']:
            print(f"\n🎉 图表生成成功!")
        else:
            print(f"\n❌ 图表生成失败")
            
    except KeyboardInterrupt:
        print(f"\n🛑 用户中断操作")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")


if __name__ == "__main__":
    main()
