#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品市场异常操作检测系统
基于价量背离、供给异常、技术指标等多维度检测市场异常行为
"""

import json
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class AnomalyDetector:
    """异常操作检测器"""
    
    def __init__(self, skin_name, data_path):
        self.skin_name = skin_name
        self.data_path = data_path
        self.hourly_data = None
        self.daily_data = None      # 新增：日K数据
        self.weekly_data = None     # 新增：周K数据
        self.trend_data = None
        self.anomalies = []
        
    def load_data(self):
        """加载多时间框架数据"""
        try:
            # 加载时K数据
            with open(f"{self.data_path}/时k.json", 'r', encoding='utf-8') as f:
                hourly_raw = json.load(f)

            # 加载3个月走势数据（实时分析专用）
            with open(f"{self.data_path}/走势_3m.json", 'r', encoding='utf-8') as f:
                trend_raw = json.load(f)

            # 尝试加载日K数据（正确的合并逻辑）
            try:
                daily_raw = self._load_and_merge_daily_data()
                if daily_raw:
                    self.daily_data = self._process_daily_data(daily_raw)
                    daily_count = len(self.daily_data)
                else:
                    print(f"⚠️ 未找到有效的日K数据文件，将跳过日线分析")
                    self.daily_data = None
                    daily_count = 0
            except Exception as e:
                print(f"⚠️ 日K数据加载失败: {e}，将跳过日线分析")
                self.daily_data = None
                daily_count = 0

            # 尝试加载周K数据
            try:
                with open(f"{self.data_path}/周k.json", 'r', encoding='utf-8') as f:
                    weekly_raw = json.load(f)
                self.weekly_data = self._process_weekly_data(weekly_raw)
                weekly_count = len(self.weekly_data)
            except FileNotFoundError:
                print(f"⚠️ 未找到周K数据文件，将跳过周线分析")
                self.weekly_data = None
                weekly_count = 0

            # 处理数据
            self.hourly_data = self._process_hourly_data(hourly_raw)
            self.trend_data = self._process_trend_data(trend_raw)

            print(f"✅ {self.skin_name} 数据加载成功")
            print(f"   时K数据: {len(self.hourly_data)}条")
            print(f"   日K数据: {daily_count}条")
            print(f"   周K数据: {weekly_count}条")
            print(f"   走势数据: {len(self.trend_data)}条")

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")

    def _load_and_merge_daily_data(self):
        """加载并合并日K数据（日k1.json + 日k2.json）"""
        import os

        # 可能的文件名组合
        file_combinations = [
            ['日k1.json', '日k2.json'],  # M4A1格式
            ['日K1.json', '日k2.json'],  # UMP-45格式
            ['日k1.json', '日K2.json'],  # 其他可能格式
        ]

        for file1, file2 in file_combinations:
            path1 = os.path.join(self.data_path, file1)
            path2 = os.path.join(self.data_path, file2)

            if os.path.exists(path1) and os.path.exists(path2):
                try:
                    # 读取两个文件
                    with open(path1, 'r', encoding='utf-8') as f:
                        data1 = json.load(f)
                    with open(path2, 'r', encoding='utf-8') as f:
                        data2 = json.load(f)

                    # 按时间戳排序合并
                    combined_data = data1 + data2
                    combined_data.sort(key=lambda x: int(x[0]))  # 按时间戳排序

                    print(f"✅ 成功合并日K数据: {file1}({len(data1)}条) + {file2}({len(data2)}条) = {len(combined_data)}条")

                    # 验证时间连续性
                    if len(combined_data) > 1:
                        first_time = pd.to_datetime(int(combined_data[0][0]), unit='s')
                        last_time = pd.to_datetime(int(combined_data[-1][0]), unit='s')
                        print(f"   时间范围: {first_time.date()} 到 {last_time.date()}")

                    return combined_data

                except Exception as e:
                    print(f"⚠️ 合并{file1}和{file2}失败: {e}")
                    continue

        # 如果合并失败，尝试使用现有的合并文件
        fallback_files = ['日K合并.json', '日k合并.json']
        for filename in fallback_files:
            path = os.path.join(self.data_path, filename)
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    print(f"⚠️ 使用备用合并文件: {filename}({len(data)}条)")
                    return data
                except Exception as e:
                    print(f"⚠️ 读取{filename}失败: {e}")

        return None

    def _process_hourly_data(self, raw_data):
        """处理时K数据"""
        df = pd.DataFrame(raw_data, columns=[
            'timestamp', 'open', 'low', 'high', 'close', 'volume', 'amount'
        ])
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
        df = df.sort_values('datetime').reset_index(drop=True)

        # 处理null值：成交量和成交额为null时设为0
        df['volume'] = df['volume'].fillna(0)
        df['amount'] = df['amount'].fillna(0)

        # 处理价格null值：使用前值填充
        df['open'] = df['open'].fillna(method='ffill')
        df['high'] = df['high'].fillna(method='ffill')
        df['low'] = df['low'].fillna(method='ffill')
        df['close'] = df['close'].fillna(method='ffill')

        # 计算技术指标
        df['price_change'] = df['close'].pct_change()
        df['volume_ma'] = df['volume'].rolling(24, min_periods=1).mean()  # 24小时均量
        df['price_ma'] = df['close'].rolling(24, min_periods=1).mean()    # 24小时均价

        return df

    def _process_daily_data(self, raw_data):
        """处理日K数据"""
        df = pd.DataFrame(raw_data, columns=[
            'timestamp', 'open', 'low', 'high', 'close', 'volume', 'amount'
        ])

        # 处理时间戳（可能是字符串格式）
        df['timestamp'] = df['timestamp'].astype(str).astype(int)
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
        df = df.sort_values('datetime').reset_index(drop=True)

        # 处理null值
        df['volume'] = df['volume'].fillna(0)
        df['amount'] = df['amount'].fillna(0)
        df['open'] = df['open'].fillna(method='ffill')
        df['high'] = df['high'].fillna(method='ffill')
        df['low'] = df['low'].fillna(method='ffill')
        df['close'] = df['close'].fillna(method='ffill')

        # 计算日线技术指标
        df['price_change'] = df['close'].pct_change()
        df['volume_ma'] = df['volume'].rolling(20, min_periods=1).mean()  # 20日均量
        df['price_ma20'] = df['close'].rolling(20, min_periods=1).mean()  # 20日均价
        df['price_ma50'] = df['close'].rolling(50, min_periods=1).mean()  # 50日均价

        return df

    def _process_weekly_data(self, raw_data):
        """处理周K数据"""
        df = pd.DataFrame(raw_data, columns=[
            'timestamp', 'open', 'low', 'high', 'close', 'volume', 'amount'
        ])

        # 处理时间戳（可能是字符串格式）
        df['timestamp'] = df['timestamp'].astype(str).astype(int)
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
        df = df.sort_values('datetime').reset_index(drop=True)

        # 处理null值（周K数据经常有null值）
        df['volume'] = df['volume'].fillna(0)
        df['amount'] = df['amount'].fillna(0)
        df['open'] = df['open'].fillna(method='ffill')
        df['high'] = df['high'].fillna(method='ffill')
        df['low'] = df['low'].fillna(method='ffill')
        df['close'] = df['close'].fillna(method='ffill')

        # 计算周线技术指标
        df['price_change'] = df['close'].pct_change()
        df['price_ma10'] = df['close'].rolling(10, min_periods=1).mean()  # 10周均价
        df['price_ma20'] = df['close'].rolling(20, min_periods=1).mean()  # 20周均价

        # 计算主要支撑阻力位
        df['yearly_high'] = df['high'].rolling(52, min_periods=1).max()   # 年度高点
        df['yearly_low'] = df['low'].rolling(52, min_periods=1).min()     # 年度低点

        return df

    def _process_trend_data(self, raw_data):
        """处理走势数据 - 更新正确的字段对应关系"""
        df = pd.DataFrame(raw_data, columns=[
            'timestamp', 'price', 'supply', 'bid_price', 'bid_quantity', 'hourly_amount', 'hourly_count', 'total_supply'
        ])
        df['datetime'] = pd.to_datetime(df['timestamp'], unit='s')
        df = df.sort_values('datetime').reset_index(drop=True)

        # 处理数据类型
        df['total_supply'] = pd.to_numeric(df['total_supply'], errors='coerce')

        # 处理null值：成交量和成交额为null时设为0
        df['hourly_amount'] = df['hourly_amount'].fillna(0)
        df['hourly_count'] = df['hourly_count'].fillna(0)

        # 处理其他null值
        df['price'] = df['price'].fillna(method='ffill')
        df['supply'] = df['supply'].fillna(method='ffill')
        df['bid_price'] = df['bid_price'].fillna(method='ffill')
        df['bid_quantity'] = df['bid_quantity'].fillna(method='ffill')
        df['total_supply'] = df['total_supply'].fillna(method='ffill')

        # 计算供给变化
        df['supply_change'] = df['supply'].pct_change()

        # 计算求购比率（求购价/当前价格）
        df['bid_ratio'] = df['bid_price'] / df['price']

        # 计算平均成交价格（成交额/成交量），避免除零
        df['avg_trade_price'] = df['hourly_amount'] / df['hourly_count'].replace(0, np.nan)

        # 计算资金流动性（小时成交额/在售市值）
        df['market_cap'] = df['supply'] * df['price']
        df['liquidity_ratio'] = df['hourly_amount'] / df['market_cap']

        return df
    
    def detect_price_volume_divergence(self):
        """检测价量背离"""
        anomalies = []
        
        for i in range(1, len(self.hourly_data)):
            row = self.hourly_data.iloc[i]
            
            price_change = abs(row['price_change']) if pd.notna(row['price_change']) else 0
            volume = row['volume']
            
            # 无量大幅波动
            if price_change > 0.05 and volume < 3:
                anomalies.append({
                    'type': '无量大幅波动',
                    'datetime': row['datetime'],
                    'price_change': price_change,
                    'volume': volume,
                    'severity': 'HIGH',
                    'description': f"价格变动{price_change:.2%}，但成交量仅{volume}"
                })
            
            # 天量不涨不跌 - 修复逻辑错误
            if pd.notna(row['volume_ma']) and row['volume_ma'] > 0:
                if volume > row['volume_ma'] * 5 and price_change < 0.01:
                    anomalies.append({
                        'type': '天量不涨不跌',
                        'datetime': row['datetime'],
                        'price_change': price_change,
                        'volume': volume,
                        'severity': 'MEDIUM',
                        'description': f"成交量{volume}为均量{row['volume_ma']:.1f}的{volume/row['volume_ma']:.1f}倍，但价格变动仅{price_change:.2%}"
                    })
        
        return anomalies
    
    def detect_supply_anomaly(self):
        """检测供给异常"""
        anomalies = []
        
        for i in range(1, len(self.trend_data)):
            row = self.trend_data.iloc[i]
            
            supply_change = abs(row['supply_change']) if pd.notna(row['supply_change']) else 0
            
            # 供给量瞬时剧变
            if supply_change > 0.3:
                anomalies.append({
                    'type': '供给量瞬时剧变',
                    'datetime': row['datetime'],
                    'supply_change': supply_change,
                    'supply': row['supply'],
                    'severity': 'HIGH' if supply_change > 0.5 else 'MEDIUM',
                    'description': f"在售量变动{supply_change:.2%}，当前在售{row['supply']}"
                })
        
        return anomalies
    
    def calculate_obv(self):
        """计算OBV指标"""
        obv = [0]
        
        for i in range(1, len(self.hourly_data)):
            prev_close = self.hourly_data.iloc[i-1]['close']
            curr_close = self.hourly_data.iloc[i]['close']
            volume = self.hourly_data.iloc[i]['volume']
            
            if curr_close > prev_close:
                obv.append(obv[-1] + volume)
            elif curr_close < prev_close:
                obv.append(obv[-1] - volume)
            else:
                obv.append(obv[-1])
        
        self.hourly_data['obv'] = obv
        
    def detect_obv_divergence(self):
        """检测OBV背离"""
        anomalies = []
        
        # 计算OBV
        self.calculate_obv()
        
        # 寻找价格新高但OBV未创新高的情况
        for i in range(20, len(self.hourly_data)):
            current_price = self.hourly_data.iloc[i]['close']
            current_obv = self.hourly_data.iloc[i]['obv']
            
            # 检查是否为20日内价格新高
            recent_prices = self.hourly_data.iloc[i-20:i]['close']
            recent_obvs = self.hourly_data.iloc[i-20:i]['obv']
            
            if current_price == recent_prices.max() and current_obv < recent_obvs.max():
                anomalies.append({
                    'type': 'OBV顶背离',
                    'datetime': self.hourly_data.iloc[i]['datetime'],
                    'price': current_price,
                    'obv': current_obv,
                    'severity': 'MEDIUM',
                    'description': f"价格创20日新高{current_price:.2f}，但OBV未创新高"
                })
        
        return anomalies
    
    def calculate_bollinger_bands(self, period=20, std_dev=2):
        """计算布林线"""
        self.hourly_data['bb_middle'] = self.hourly_data['close'].rolling(period).mean()
        self.hourly_data['bb_std'] = self.hourly_data['close'].rolling(period).std()
        self.hourly_data['bb_upper'] = self.hourly_data['bb_middle'] + (std_dev * self.hourly_data['bb_std'])
        self.hourly_data['bb_lower'] = self.hourly_data['bb_middle'] - (std_dev * self.hourly_data['bb_std'])
    
    def detect_bollinger_anomaly(self):
        """检测布林线异常突破"""
        anomalies = []
        
        # 计算布林线
        self.calculate_bollinger_bands()
        
        for i in range(20, len(self.hourly_data)):
            row = self.hourly_data.iloc[i]
            
            # 低成交量下极端突破布林带
            if row['volume'] < 5:
                if row['close'] > row['bb_upper']:
                    anomalies.append({
                        'type': '低量突破布林上轨',
                        'datetime': row['datetime'],
                        'price': row['close'],
                        'bb_upper': row['bb_upper'],
                        'volume': row['volume'],
                        'severity': 'MEDIUM',
                        'description': f"价格{row['close']:.2f}突破布林上轨{row['bb_upper']:.2f}，但成交量仅{row['volume']}"
                    })
                elif row['close'] < row['bb_lower']:
                    anomalies.append({
                        'type': '低量跌破布林下轨',
                        'datetime': row['datetime'],
                        'price': row['close'],
                        'bb_lower': row['bb_lower'],
                        'volume': row['volume'],
                        'severity': 'MEDIUM',
                        'description': f"价格{row['close']:.2f}跌破布林下轨{row['bb_lower']:.2f}，但成交量仅{row['volume']}"
                    })
        
        return anomalies
    
    def detect_time_anomaly(self):
        """检测交易时间异常"""
        anomalies = []
        
        for i, row in self.hourly_data.iterrows():
            hour = row['datetime'].hour
            
            # 深夜大额交易
            if 0 <= hour <= 6 and row['volume'] > 10:
                anomalies.append({
                    'type': '深夜大额交易',
                    'datetime': row['datetime'],
                    'volume': row['volume'],
                    'price': row['close'],
                    'severity': 'LOW',
                    'description': f"深夜{hour}点大额交易，成交量{row['volume']}"
                })
        
        return anomalies
    
    def detect_statistical_anomaly(self):
        """检测统计学异常"""
        anomalies = []
        
        # 计算30日均价和标准差
        self.hourly_data['price_30ma'] = self.hourly_data['close'].rolling(30*24).mean()
        self.hourly_data['price_30std'] = self.hourly_data['close'].rolling(30*24).std()
        self.hourly_data['z_score'] = (self.hourly_data['close'] - self.hourly_data['price_30ma']) / self.hourly_data['price_30std']
        
        for i, row in self.hourly_data.iterrows():
            if pd.notna(row['z_score']) and abs(row['z_score']) > 3:
                anomalies.append({
                    'type': '统计学异常',
                    'datetime': row['datetime'],
                    'price': row['close'],
                    'z_score': row['z_score'],
                    'severity': 'HIGH' if abs(row['z_score']) > 4 else 'MEDIUM',
                    'description': f"价格{row['close']:.2f}的Z-Score为{row['z_score']:.2f}，严重偏离统计均值"
                })
        
        return anomalies
    
    def run_full_detection(self):
        """运行完整的异常检测"""
        print(f"\n🔍 开始检测 {self.skin_name} 的异常操作...")
        
        # 各种异常检测
        pv_anomalies = self.detect_price_volume_divergence()
        supply_anomalies = self.detect_supply_anomaly()
        obv_anomalies = self.detect_obv_divergence()
        bb_anomalies = self.detect_bollinger_anomaly()
        time_anomalies = self.detect_time_anomaly()
        stat_anomalies = self.detect_statistical_anomaly()
        
        # 合并所有异常
        all_anomalies = (pv_anomalies + supply_anomalies + obv_anomalies + 
                        bb_anomalies + time_anomalies + stat_anomalies)
        
        # 按时间排序
        all_anomalies.sort(key=lambda x: x['datetime'])
        
        self.anomalies = all_anomalies
        
        # 统计结果
        high_count = len([a for a in all_anomalies if a['severity'] == 'HIGH'])
        medium_count = len([a for a in all_anomalies if a['severity'] == 'MEDIUM'])
        low_count = len([a for a in all_anomalies if a['severity'] == 'LOW'])
        
        print(f"✅ 异常检测完成")
        print(f"   总异常数: {len(all_anomalies)}")
        print(f"   高风险: {high_count}")
        print(f"   中风险: {medium_count}")
        print(f"   低风险: {low_count}")
        
        return all_anomalies
    
    def generate_report(self):
        """生成异常分析报告"""
        if not self.anomalies:
            return "未检测到异常"
        
        report = f"\n{'='*60}\n"
        report += f"🚨 {self.skin_name} 异常操作检测报告\n"
        report += f"{'='*60}\n\n"
        
        # 按严重程度分组
        high_anomalies = [a for a in self.anomalies if a['severity'] == 'HIGH']
        medium_anomalies = [a for a in self.anomalies if a['severity'] == 'MEDIUM']
        low_anomalies = [a for a in self.anomalies if a['severity'] == 'LOW']
        
        if high_anomalies:
            report += "🔴 高风险异常 (需立即关注)\n"
            report += "-" * 40 + "\n"
            for anomaly in high_anomalies[:10]:  # 只显示前10个
                report += f"时间: {anomaly['datetime'].strftime('%Y-%m-%d %H:%M')}\n"
                report += f"类型: {anomaly['type']}\n"
                report += f"描述: {anomaly['description']}\n\n"
        
        if medium_anomalies:
            report += "🟡 中风险异常 (需密切监控)\n"
            report += "-" * 40 + "\n"
            for anomaly in medium_anomalies[:5]:  # 只显示前5个
                report += f"时间: {anomaly['datetime'].strftime('%Y-%m-%d %H:%M')}\n"
                report += f"类型: {anomaly['type']}\n"
                report += f"描述: {anomaly['description']}\n\n"
        
        if low_anomalies:
            report += f"🟢 低风险异常: {len(low_anomalies)}个 (需关注)\n\n"
        
        return report

if __name__ == "__main__":
    # 示例使用
    detector = AnomalyDetector("测试饰品", "数据路径")
    # detector.load_data()
    # detector.run_full_detection()
    # print(detector.generate_report())
