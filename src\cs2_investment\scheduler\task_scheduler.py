#!/usr/bin/env python3
"""
定时任务调度器

负责管理常规分析和实时监控的定时任务
"""

import asyncio
import random
import sys
from datetime import datetime, timedelta
from typing import List, Dict, Any
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.dao.favorite_dao import FavoriteDAO
from src.cs2_investment.fx.integrated_analysis_system import IntegratedAnalysisSystem
from src.cs2_investment.fx.integrated_ssync_system import IntegratedSSyncSystem

# 使用统一日志系统
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)


class TaskScheduler:
    """定时任务调度器"""
    
    def __init__(self):
        """初始化调度器"""
        self.is_running = False
        self.regular_analysis_task = None
        self.realtime_monitor_task = None

        # 任务配置
        self.regular_interval_hours = 24  # 常规分析每24小时执行一次
        self.realtime_interval_hours = 1  # 实时监控每1小时执行一次
        self.item_delay_seconds = (15, 30)  # 饰品之间间隔15-30秒

        # DAO实例
        self.item_dao = ItemDAO()
        self.favorite_dao = FavoriteDAO()

        # 系统实例
        self.regular_system = IntegratedAnalysisSystem()
        self.realtime_system = IntegratedSSyncSystem()
    
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行中")
            return
        
        self.is_running = True
        logger.info("🚀 启动定时任务调度器")
        
        # 启动常规分析任务
        self.regular_analysis_task = asyncio.create_task(
            self._regular_analysis_loop()
        )
        
        # 启动实时监控任务
        self.realtime_monitor_task = asyncio.create_task(
            self._realtime_monitor_loop()
        )
        
        logger.info("✅ 定时任务调度器启动完成")
        
        # 等待任务完成（通常不会完成，除非手动停止）
        try:
            await asyncio.gather(
                self.regular_analysis_task,
                self.realtime_monitor_task
            )
        except asyncio.CancelledError:
            logger.info("定时任务被取消")
    
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning("调度器未在运行")
            return
        
        logger.info("🛑 停止定时任务调度器")
        self.is_running = False
        
        # 取消任务
        if self.regular_analysis_task:
            self.regular_analysis_task.cancel()
        
        if self.realtime_monitor_task:
            self.realtime_monitor_task.cancel()
        
        # 等待任务完全停止
        try:
            if self.regular_analysis_task:
                await self.regular_analysis_task
        except asyncio.CancelledError:
            pass
        
        try:
            if self.realtime_monitor_task:
                await self.realtime_monitor_task
        except asyncio.CancelledError:
            pass
        
        logger.info("✅ 定时任务调度器已停止")
    
    async def _regular_analysis_loop(self):
        """常规分析循环任务"""
        logger.info("📊 启动常规分析定时任务")
        
        while self.is_running:
            try:
                # 获取需要分析的饰品
                items = self._get_items_for_analysis()

                if not items:
                    logger.warning("没有找到需要分析的饰品")
                    await asyncio.sleep(3600)  # 1小时后重试
                    continue

                logger.info(f"📋 开始常规分析，共 {len(items)} 个饰品")
                
                # 逐个分析饰品
                for i, item in enumerate(items, 1):
                    if not self.is_running:
                        break
                    
                    await self._run_regular_analysis(item, i, len(items))
                    
                    # 饰品之间的随机间隔
                    if i < len(items):  # 不是最后一个饰品
                        delay = random.randint(*self.item_delay_seconds)
                        logger.info(f"⏳ 等待 {delay} 秒后分析下一个饰品...")
                        await asyncio.sleep(delay)
                
                logger.info("✅ 本轮常规分析完成")
                
                # 等待下一轮分析
                next_run = datetime.now() + timedelta(hours=self.regular_interval_hours)
                logger.info(f"⏰ 下次常规分析时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
                await asyncio.sleep(self.regular_interval_hours * 3600)
                
            except asyncio.CancelledError:
                logger.info("常规分析任务被取消")
                break
            except Exception as e:
                logger.error(f"常规分析循环出错: {e}")
                await asyncio.sleep(300)  # 5分钟后重试
    
    async def _realtime_monitor_loop(self):
        """实时监控循环任务"""
        logger.info("📈 启动实时监控定时任务")
        
        while self.is_running:
            try:
                # 获取需要监控的饰品（收藏+持仓，去重合并）
                monitor_items = self._get_items_for_monitor()

                if not monitor_items:
                    logger.warning("没有找到需要监控的饰品（收藏或持仓）")
                    await asyncio.sleep(3600)  # 1小时后重试
                    continue

                logger.info(f"📋 开始实时监控，共 {len(monitor_items)} 个饰品")
                logger.info(f"📊 监控来源统计: {self._get_monitor_source_stats(monitor_items)}")

                # 逐个监控饰品（顺序执行，避免IP封禁）
                for i, item in enumerate(monitor_items, 1):
                    if not self.is_running:
                        break

                    await self._run_realtime_monitor(item, i, len(monitor_items))
                    
                    # 饰品之间的随机间隔
                    if i < len(monitor_items):  # 不是最后一个饰品
                        delay = random.randint(*self.item_delay_seconds)
                        logger.info(f"⏳ 等待 {delay} 秒后监控下一个饰品...")
                        await asyncio.sleep(delay)
                
                logger.info("✅ 本轮实时监控完成")
                
                # 等待下一轮监控
                next_run = datetime.now() + timedelta(hours=self.realtime_interval_hours)
                logger.info(f"⏰ 下次实时监控时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
                await asyncio.sleep(self.realtime_interval_hours * 3600)
                
            except asyncio.CancelledError:
                logger.info("实时监控任务被取消")
                break
            except Exception as e:
                logger.error(f"实时监控循环出错: {e}")
                await asyncio.sleep(300)  # 5分钟后重试

    def _get_items_for_analysis(self) -> List[Dict[str, Any]]:
        """获取需要常规分析的饰品"""
        try:
            # 获取所有饰品，这里可以根据需要添加过滤条件
            items = self.item_dao.get_all()

            # 立即转换为字典格式，避免会话关闭后无法访问属性
            result = []
            for item in items:
                try:
                    # 立即访问所有需要的属性
                    item_id = item.item_id
                    name = item.name
                    market_hash_name = getattr(item, 'market_hash_name', None)
                    item_type = getattr(item, 'item_type', None)
                    quality = getattr(item, 'quality', None)
                    rarity = getattr(item, 'rarity', None)

                    # 构造SteamDT URL - 必须使用market_hash_name，如果没有则跳过
                    if not market_hash_name:
                        logger.warning(f"饰品 {item_id} 缺少 market_hash_name，跳过")
                        continue
                    url = f"https://steamdt.com/cs2/{market_hash_name}"

                    result.append({
                        'item_id': item_id,
                        'name': name,
                        'market_hash_name': market_hash_name,
                        'url': url,
                        'item_type': item_type,
                        'quality': quality,
                        'rarity': rarity
                    })
                except Exception as item_error:
                    logger.warning(f"跳过无法处理的饰品: {item_error}")
                    continue

            logger.info(f"获取到 {len(result)} 个饰品用于常规分析")
            return result

        except Exception as e:
            logger.error(f"获取饰品数据失败: {e}")
            return []

    def _get_favorites_for_monitor(self) -> List[Dict[str, Any]]:
        """获取需要实时监控的收藏饰品"""
        try:
            # 获取所有收藏饰品，并通过JOIN获取item信息
            from ..config.database import get_db_session
            from ..models.favorite import Favorite
            from ..models.item import Item

            result = []
            with get_db_session() as session:
                # 使用JOIN查询获取收藏饰品和对应的item信息
                favorites_with_items = session.query(Favorite, Item).join(
                    Item, Favorite.item_id == Item.item_id
                ).all()

                for favorite, item in favorites_with_items:
                    try:
                        # 立即访问所有需要的属性
                        fav_id = favorite.id
                        item_id = favorite.item_id
                        item_name = getattr(favorite, 'item_name', None) or item.name
                        market_hash_name = getattr(item, 'market_hash_name', None)
                        user_id = getattr(favorite, 'user_id', None)
                        created_at = getattr(favorite, 'created_at', None)

                        # 构造SteamDT URL - 必须使用market_hash_name，如果没有则跳过
                        if not market_hash_name:
                            logger.warning(f"收藏饰品 {item_id} 缺少 market_hash_name，跳过")
                            continue
                        url = f"https://steamdt.com/cs2/{market_hash_name}"

                        result.append({
                            'id': f"favorite_{fav_id}",
                            'item_id': item_id,
                            'name': item_name,
                            'market_hash_name': market_hash_name,
                            'url': url,
                            'user_id': user_id,
                            'created_at': created_at,
                            'source': 'favorite',
                            'priority': 2
                        })
                    except Exception as fav_error:
                        logger.warning(f"跳过无法处理的收藏饰品: {fav_error}")
                        continue

            logger.info(f"获取到 {len(result)} 个收藏饰品用于实时监控")
            return result

        except Exception as e:
            logger.error(f"获取收藏饰品数据失败: {e}")
            return []

    def _get_holdings_for_monitor(self) -> List[Dict[str, Any]]:
        """获取需要实时监控的持仓饰品"""
        try:
            from src.cs2_investment.dao.holding_dao import HoldingDAO
            from src.cs2_investment.dao.item_dao import ItemDAO

            holding_dao = HoldingDAO()
            item_dao = ItemDAO()

            # 获取默认用户的所有持仓
            default_user_id = "default_user"
            holdings = holding_dao.get_user_holdings(default_user_id, limit=1000)

            if not holdings:
                logger.debug("没有找到持仓饰品")
                return []

            logger.info(f"找到 {len(holdings)} 个持仓饰品，开始获取market_hash_name...")

            monitor_list = []
            for holding in holdings:
                try:
                    item_id = holding.get('item_id', '')
                    item_name = holding.get('item_name', '')

                    if not item_id:
                        logger.warning(f"持仓饰品缺少item_id: {holding}")
                        continue

                    # 从items表获取market_hash_name
                    item = item_dao.get_by_item_id(item_id)
                    if not item:
                        logger.warning(f"未找到饰品信息: item_id={item_id}, name={item_name}")
                        continue

                    # item是字典格式
                    market_hash_name = item.get('market_hash_name')
                    if not market_hash_name:
                        logger.warning(f"饰品缺少market_hash_name: item_id={item_id}, name={item_name}")
                        continue

                    # 构造正确的SteamDT URL，使用URL编码的market_hash_name
                    import urllib.parse
                    encoded_name = urllib.parse.quote(market_hash_name, safe='')
                    url = f"https://steamdt.com/cs2/{encoded_name}"

                    monitor_list.append({
                        'id': f"holding_{holding['id']}",
                        'item_id': item_id,
                        'name': item_name or item.get('name', '未知饰品'),
                        'url': url,
                        'market_hash_name': market_hash_name,
                        'user_id': default_user_id,
                        'source': 'holding',
                        'quantity': holding.get('total_quantity', 0),
                        'total_cost': holding.get('total_cost', 0),
                        'priority': 1
                    })

                    logger.debug(f"成功处理持仓饰品: {item_name} -> {url}")

                except Exception as item_error:
                    logger.warning(f"处理持仓饰品失败: {item_error}")
                    continue

            logger.info(f"成功获取 {len(monitor_list)} 个可监控的持仓饰品")
            return monitor_list

        except Exception as e:
            logger.error(f"获取持仓饰品失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return []

    def _get_items_for_monitor(self) -> List[Dict[str, Any]]:
        """获取需要监控的所有饰品（收藏+持仓，去重合并）"""
        try:
            # 获取收藏饰品
            favorites = self._get_favorites_for_monitor()

            # 获取持仓饰品
            holdings = self._get_holdings_for_monitor()

            # 合并列表，使用item_id去重
            seen_items = set()
            monitor_items = []

            # 先添加持仓饰品（优先级更高）
            for holding in holdings:
                item_id = holding.get('item_id')
                if item_id and item_id not in seen_items:
                    seen_items.add(item_id)
                    monitor_items.append(holding)

            # 再添加收藏饰品（去重）
            for favorite in favorites:
                item_id = favorite.get('item_id')
                if item_id and item_id not in seen_items:
                    seen_items.add(item_id)
                    favorite['priority'] = 2  # 收藏优先级
                    monitor_items.append(favorite)

            # 按优先级排序（持仓优先）
            monitor_items.sort(key=lambda x: x.get('priority', 999))

            logger.info(f"合并监控列表完成: 持仓 {len(holdings)} 个, 收藏 {len(favorites)} 个, 去重后 {len(monitor_items)} 个")

            return monitor_items

        except Exception as e:
            logger.error(f"合并监控列表失败: {e}")
            return []

    def _get_monitor_source_stats(self, monitor_items: List[Dict[str, Any]]) -> str:
        """获取监控来源统计信息"""
        try:
            holding_count = sum(1 for item in monitor_items if item.get('source') == 'holding')
            favorite_count = sum(1 for item in monitor_items if item.get('source') == 'favorite')

            return f"持仓 {holding_count} 个, 收藏 {favorite_count} 个"
        except Exception:
            return "统计失败"

    async def _run_regular_analysis(self, item: Dict[str, Any], current: int, total: int):
        """运行单个饰品的常规分析"""
        start_time = datetime.now()
        
        try:
            logger.info(f"📊 [{current}/{total}] 开始常规分析: {item['name']}")

            # 运行常规分析
            result = await self.regular_system.run_complete_analysis(item['url'], item['name'])

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            if result['success']:
                logger.info(f"✅ [{current}/{total}] 常规分析成功: {item['name']} (耗时: {duration:.1f}秒)")
            else:
                error_msg = result.get('error', '未知错误')
                logger.error(f"❌ [{current}/{total}] 常规分析失败: {item['name']} - {error_msg}")

        except Exception as e:
            end_time = datetime.now()
            error_msg = str(e)
            logger.error(f"❌ [{current}/{total}] 常规分析异常: {item['name']} - {error_msg}")
    
    async def _run_realtime_monitor(self, favorite: Dict[str, Any], current: int, total: int):
        """运行单个饰品的实时监控"""
        start_time = datetime.now()

        try:
            logger.info(f"📈 [{current}/{total}] 开始实时监控: {favorite['name']}")

            # 设置超时时间和重试机制
            max_retries = 2  # 最多重试2次
            retry_count = 0

            while retry_count <= max_retries:
                try:
                    # 使用asyncio.wait_for设置总超时时间为10分钟
                    result = await asyncio.wait_for(
                        self.realtime_system.run_complete_analysis(favorite['url'], favorite['name']),
                        timeout=600  # 10分钟超时
                    )

                    end_time = datetime.now()
                    duration = (end_time - start_time).total_seconds()

                    if result['success']:
                        logger.info(f"✅ [{current}/{total}] 实时监控成功: {favorite['name']} (耗时: {duration:.1f}秒)")
                        break  # 成功则跳出重试循环
                    else:
                        error_msg = result.get('error', '未知错误')
                        if retry_count < max_retries:
                            logger.warning(f"⚠️ [{current}/{total}] 实时监控失败，准备重试: {favorite['name']} - {error_msg}")
                            retry_count += 1
                            await asyncio.sleep(30)  # 等待30秒后重试
                            continue
                        else:
                            logger.error(f"❌ [{current}/{total}] 实时监控失败（已重试{max_retries}次）: {favorite['name']} - {error_msg}")
                            break

                except asyncio.TimeoutError:
                    if retry_count < max_retries:
                        logger.warning(f"⚠️ [{current}/{total}] 实时监控超时，准备重试: {favorite['name']} - 超过10分钟未完成")
                        retry_count += 1
                        await asyncio.sleep(60)  # 超时后等待1分钟再重试
                        continue
                    else:
                        logger.error(f"❌ [{current}/{total}] 实时监控超时（已重试{max_retries}次）: {favorite['name']} - 超过10分钟未完成")
                        break
                except Exception as analysis_error:
                    if retry_count < max_retries:
                        logger.warning(f"⚠️ [{current}/{total}] 实时监控异常，准备重试: {favorite['name']} - {analysis_error}")
                        retry_count += 1
                        await asyncio.sleep(30)  # 异常后等待30秒再重试
                        continue
                    else:
                        logger.error(f"❌ [{current}/{total}] 实时监控异常（已重试{max_retries}次）: {favorite['name']} - {analysis_error}")
                        break

        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ [{current}/{total}] 实时监控异常: {favorite['name']} - {error_msg}")
    
    def get_status(self) -> dict:
        """获取调度器状态"""
        return {
            'is_running': self.is_running,
            'regular_analysis_running': self.regular_analysis_task and not self.regular_analysis_task.done(),
            'realtime_monitor_running': self.realtime_monitor_task and not self.realtime_monitor_task.done(),
            'regular_interval_hours': self.regular_interval_hours,
            'realtime_interval_hours': self.realtime_interval_hours,
            'item_delay_seconds': self.item_delay_seconds
        }


# 全局调度器实例
scheduler = TaskScheduler()


async def main():
    """主函数 - 用于测试"""
    # 日志已通过统一组件配置
    
    try:
        # 启动调度器
        await scheduler.start()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止...")
        await scheduler.stop()


if __name__ == "__main__":
    asyncio.run(main())
