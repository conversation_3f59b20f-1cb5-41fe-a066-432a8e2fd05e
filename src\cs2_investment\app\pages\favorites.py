"""
收藏列表页面

提供用户收藏饰品的管理功能。
"""

import streamlit as st
import pandas as pd
from typing import List, Dict, Any
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.services.favorite_service import FavoriteService
from src.cs2_investment.app.utils.data_formatter import format_price, format_percentage, format_item_type, format_quality
from src.cs2_investment.app.components import item_analysis_component


def show_page():
    """显示收藏列表页面"""
    st.title("⭐ 收藏列表")

    try:
        # 初始化服务
        if 'favorite_service' not in st.session_state:
            st.session_state.favorite_service = FavoriteService()

        # 获取收藏统计
        with st.spinner("加载收藏数据..."):
            stats = st.session_state.favorite_service.get_favorite_statistics()

        if stats.get('total_count', 0) == 0:
            show_empty_favorites()
            return

        # 显示收藏统计
        show_favorites_stats(stats)

        # 不再需要复杂的筛选选项，统一收藏表已经简化了筛选

        # 显示收藏列表
        show_favorites_list()

    except Exception as e:
        st.error(f"加载收藏页面失败: {str(e)}")
        st.info("请检查数据库连接配置")
        show_empty_favorites()


def show_empty_favorites():
    """显示空收藏列表"""
    st.info("您还没有收藏任何饰品")
    
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.markdown("""
        ### 如何添加收藏？
        
        1. 前往 **饰品查询** 页面
        2. 搜索您感兴趣的饰品
        3. 点击饰品卡片上的 ⭐ 按钮
        4. 收藏的饰品将出现在这里
        """)
        
        if st.button("🔍 去查询饰品", type="primary", use_container_width=True):
            st.switch_page("pages/item_query.py")


def show_favorites_stats(stats: Dict[str, Any]):
    """显示收藏统计信息"""
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("收藏总数", stats.get('total_count', 0))

    with col2:
        total_value = stats.get('total_value', 0)
        st.metric("总价值", f"¥{total_value:,.2f}")

    with col3:
        avg_price = stats.get('average_price', 0)
        st.metric("平均价格", f"¥{avg_price:,.2f}")

    with col4:
        rising_count = stats.get('rising_count', 0)
        total_count = stats.get('total_count', 0)
        st.metric("7天上涨", f"{rising_count}/{total_count}")


# show_filter_options 函数已删除，使用统一收藏表的简化筛选


def show_favorites_list():
    """显示收藏列表 - 统一饰品收藏"""
    st.subheader("我的收藏")

    # 搜索筛选条件
    with st.expander("筛选条件", expanded=True):
        col1, col2 = st.columns([2, 1])

        with col1:
            name_query = st.text_input(
                "饰品名称搜索",
                placeholder="输入饰品名称进行搜索...",
                key="favorite_name_search"
            )

        with col2:
            if st.button("🔍 搜索", type="primary"):
                st.session_state.favorite_search_executed = True
                st.session_state.favorite_page = 1  # 重置到第一页

    # 分页参数
    items_per_page = 50
    if 'favorite_page' not in st.session_state:
        st.session_state.favorite_page = 1

    offset = (st.session_state.favorite_page - 1) * items_per_page

    # 获取收藏列表
    with st.spinner("加载收藏列表..."):
        if hasattr(st.session_state, 'favorite_search_executed') and st.session_state.favorite_search_executed and name_query:
            # 搜索模式
            favorites = st.session_state.favorite_service.search_user_favorites(
                user_id="default_user",
                name_query=name_query,
                limit=items_per_page,
                offset=offset
            )

            # 获取搜索结果总数
            total_count = st.session_state.favorite_service.count_search_favorites(
                user_id="default_user",
                name_query=name_query
            )
        else:
            # 普通模式
            favorites = st.session_state.favorite_service.get_user_favorites(
                user_id="default_user",
                limit=items_per_page,
                offset=offset
            )

            # 获取总数
            total_count = st.session_state.favorite_service.get_favorite_count(
                user_id="default_user"
            )

    if not favorites:
        if name_query:
            st.info(f"未找到包含 '{name_query}' 的收藏饰品")
        else:
            st.info("暂无收藏饰品")
        return

    # 显示统计信息
    if name_query:
        st.write(f"搜索 '{name_query}' 找到 {total_count} 项收藏")
    else:
        st.write(f"共有 {total_count} 项收藏")

    # 分页控制
    total_pages = (total_count + items_per_page - 1) // items_per_page
    if total_pages > 1:
        col1, col2, col3 = st.columns([1, 2, 1])
        with col1:
            if st.button("上一页", disabled=st.session_state.favorite_page <= 1):
                st.session_state.favorite_page -= 1
                st.rerun()

        with col2:
            st.write(f"第 {st.session_state.favorite_page} 页，共 {total_pages} 页")

        with col3:
            if st.button("下一页", disabled=st.session_state.favorite_page >= total_pages):
                st.session_state.favorite_page += 1
                st.rerun()

    # 显示收藏项
    for item in favorites:
        show_favorite_item(item)


def show_favorite_item(item: Dict):
    """显示单个收藏项"""
    with st.container():
        col1, col2, col3, col4, col5 = st.columns([4, 2, 2, 1, 1])

        with col1:
            # 显示饰品名称和ID
            item_name = item.get('item_name') or item.get('item_id', '未知饰品')
            st.markdown(f"**🎮 {item_name}**")
            st.caption(f"ID: {item.get('item_id', '未知')}")

        with col2:
            # 显示收藏时间
            created_at = item.get('created_at')
            if created_at:
                st.write(f"📅 {created_at.strftime('%Y-%m-%d')}")
                st.caption(f"{created_at.strftime('%H:%M:%S')}")
            else:
                st.write("📅 未知时间")

        with col3:
            # 显示备注
            notes = item.get('notes')
            if notes:
                st.write(f"📝 {notes[:30]}{'...' if len(notes) > 30 else ''}")
            else:
                st.write("📝 无备注")

        with col4:
            # 分析按钮
            item_id = item.get('item_id')
            if item_id:
                item_analysis_component.render_analysis_button(
                    item_id=item_id,
                    button_key=f"fav_analysis_{item_id}",
                    help_text="查看收藏饰品分析结果"
                )

        with col5:
            # 取消收藏按钮
            if st.button("🗑️", key=f"remove_{item['id']}", help="取消收藏"):
                success = st.session_state.favorite_service.remove_favorite(
                    user_id="default_user",
                    item_id=item['item_id']
                )
                if success:
                    st.success("已取消收藏")
                    st.rerun()
                else:
                    st.error("取消收藏失败")

        # 分析对话框
        if item_id:
            item_analysis_component.render_analysis_dialog(
                item_data={
                    'item_id': item_id,
                    'item_name': item_name
                },
                dialog_key_suffix="_favorites"
            )

        st.divider()


# remove_from_favorites 函数已删除，直接使用 FavoriteService 的方法
