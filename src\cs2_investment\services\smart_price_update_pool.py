"""
智能价格更新调用池

实现两层更新策略：
1. 第一层：收藏饰品 - 分钟级更新，优先级最高
2. 第二层：其他饰品 - 利用剩余调用额度更新

基于SteamDT API频率限制优化调用策略。
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Set
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.favorite_dao import FavoriteDAO
from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.dao.platform_price_dao import PlatformPriceDAO
from src.cs2_investment.services.steamdt_api_client import SteamDTAPIClient
from src.cs2_investment.config.database import get_db_session


class UpdatePriority(Enum):
    """更新优先级"""
    HIGH = 1    # 收藏饰品
    LOW = 2     # 其他饰品


@dataclass
class UpdateTask:
    """更新任务"""
    item_id: str
    market_hash_name: str
    priority: UpdatePriority
    last_update: Optional[datetime] = None
    retry_count: int = 0
    
    def should_update(self, now: datetime) -> bool:
        """判断是否需要更新"""
        if not self.last_update:
            return True
        
        if self.priority == UpdatePriority.HIGH:
            # 收藏饰品：5分钟内更新
            return now - self.last_update > timedelta(minutes=5)
        else:
            # 其他饰品：2小时内更新
            return now - self.last_update > timedelta(hours=2)


class SmartPriceUpdatePool:
    """智能价格更新调用池"""
    
    def __init__(self, api_key: str, user_id: str = "default_user"):
        """
        初始化智能价格更新池
        
        Args:
            api_key: SteamDT API密钥
            user_id: 用户ID
        """
        self.api_key = api_key
        self.user_id = user_id
        self.logger = logging.getLogger(__name__)
        
        # 初始化DAO和API客户端
        self.favorite_dao = FavoriteDAO()
        self.item_dao = ItemDAO()
        self.platform_price_dao = PlatformPriceDAO()
        self.api_client = SteamDTAPIClient(api_key)
        
        # API调用限制配置（基于SteamDT官方文档）
        self.api_limits = {
            'batch_size': 100,           # 批量请求最大数量
            'single_requests_per_minute': 60,   # 单个请求每分钟限制
            'batch_requests_per_minute': 10,    # 批量请求每分钟限制
            'batch_request_interval': 6.0,      # 批量请求间隔（60秒/10次=6秒）
            'single_request_interval': 1.0,     # 单个请求间隔（60秒/60次=1秒）
        }
        
        # 调用统计和控制
        self.call_stats = {
            'minute_single_calls': 0,
            'minute_batch_calls': 0,
            'last_minute_reset': datetime.now(),
            'last_batch_call': None,
            'last_single_call': None,
            'total_calls_today': 0
        }
        
        # 任务队列
        self.high_priority_queue: List[UpdateTask] = []
        self.low_priority_queue: List[UpdateTask] = []
        self.processing_tasks: Set[str] = set()
        
        # 运行状态
        self.is_running = False
        self.continuous_mode = True  # 持续运行模式
        self.continuous_task = None  # 持续更新任务引用
        self.update_stats = {
            'total_processed': 0,
            'successful_updates': 0,
            'failed_updates': 0,
            'high_priority_processed': 0,
            'low_priority_processed': 0
        }
    
    async def initialize_queues(self):
        """初始化任务队列"""
        self.logger.info("🔄 初始化价格更新任务队列...")
        
        # 1. 获取收藏列表（高优先级）
        favorites = self.favorite_dao.get_user_favorites(self.user_id)
        favorite_item_ids = set()
        
        for favorite in favorites:
            item_id = favorite['item_id']
            favorite_item_ids.add(item_id)
            
            # 获取饰品详细信息
            item_data = self.item_dao.get_by_item_id(item_id)
            if item_data and item_data.get('market_hash_name'):
                task = UpdateTask(
                    item_id=item_id,
                    market_hash_name=item_data['market_hash_name'],
                    priority=UpdatePriority.HIGH
                )
                self.high_priority_queue.append(task)
        
        self.logger.info(f"✅ 高优先级队列: {len(self.high_priority_queue)}个收藏饰品")
        
        # 2. 获取其他饰品（低优先级）
        all_items = self.item_dao.get_all_active_items(limit=5000)  # 限制数量避免过载
        
        for item_data in all_items:
            item_id = item_data['item_id']
            
            # 跳过已在收藏列表中的饰品
            if item_id in favorite_item_ids:
                continue
            
            if item_data.get('market_hash_name'):
                task = UpdateTask(
                    item_id=item_id,
                    market_hash_name=item_data['market_hash_name'],
                    priority=UpdatePriority.LOW
                )
                self.low_priority_queue.append(task)
        
        self.logger.info(f"✅ 低优先级队列: {len(self.low_priority_queue)}个其他饰品")
        self.logger.info(f"📊 总任务数: {len(self.high_priority_queue) + len(self.low_priority_queue)}")
    
    def _reset_minute_limits(self):
        """重置分钟级限制"""
        now = datetime.now()
        if now - self.call_stats['last_minute_reset'] >= timedelta(minutes=1):
            self.call_stats['minute_single_calls'] = 0
            self.call_stats['minute_batch_calls'] = 0
            self.call_stats['last_minute_reset'] = now

    def _can_make_api_call(self, is_batch: bool = False) -> bool:
        """检查是否可以进行API调用"""
        now = datetime.now()
        self._reset_minute_limits()

        # 检查分钟级限制
        if is_batch:
            # 检查批量请求限制
            if self.call_stats['minute_batch_calls'] >= self.api_limits['batch_requests_per_minute']:
                return False

            # 检查批量请求间隔（6秒）
            if self.call_stats['last_batch_call']:
                time_since_last = (now - self.call_stats['last_batch_call']).total_seconds()
                if time_since_last < self.api_limits['batch_request_interval']:
                    return False
        else:
            # 检查单个请求限制
            if self.call_stats['minute_single_calls'] >= self.api_limits['single_requests_per_minute']:
                return False

            # 检查单个请求间隔（1秒）
            if self.call_stats['last_single_call']:
                time_since_last = (now - self.call_stats['last_single_call']).total_seconds()
                if time_since_last < self.api_limits['single_request_interval']:
                    return False

        return True

    def _get_wait_time(self, is_batch: bool = False) -> float:
        """获取需要等待的时间"""
        now = datetime.now()

        if is_batch and self.call_stats['last_batch_call']:
            elapsed = (now - self.call_stats['last_batch_call']).total_seconds()
            return max(0, self.api_limits['batch_request_interval'] - elapsed)
        elif not is_batch and self.call_stats['last_single_call']:
            elapsed = (now - self.call_stats['last_single_call']).total_seconds()
            return max(0, self.api_limits['single_request_interval'] - elapsed)

        return 0
    
    def _record_api_call(self, is_batch: bool = False, count: int = 1):
        """记录API调用"""
        now = datetime.now()
        self.call_stats['total_calls_today'] += count

        if is_batch:
            self.call_stats['minute_batch_calls'] += 1
            self.call_stats['last_batch_call'] = now
        else:
            self.call_stats['minute_single_calls'] += count
            self.call_stats['last_single_call'] = now
    
    async def _process_batch_tasks(self, tasks: List[UpdateTask]) -> Dict[str, Any]:
        """批量处理任务"""
        if not tasks:
            return {'processed': 0, 'successful': 0, 'failed': 0}

        # 检查是否可以调用API，如果不能则等待
        if not self._can_make_api_call(is_batch=True):
            wait_time = self._get_wait_time(is_batch=True)
            if wait_time > 0:
                self.logger.debug(f"等待 {wait_time:.1f} 秒后进行批量API调用")
                await asyncio.sleep(wait_time)

            # 重新检查
            if not self._can_make_api_call(is_batch=True):
                return {'processed': 0, 'successful': 0, 'failed': 0}
        
        # 限制批量大小
        batch_tasks = tasks[:self.api_limits['batch_size']]
        market_hash_names = [task.market_hash_name for task in batch_tasks]
        
        try:
            # 调用批量API
            self._record_api_call(is_batch=True, count=len(batch_tasks))

            # 使用try-except包装API调用，确保连接正确关闭
            try:
                price_responses = await self.api_client.get_batch_prices(market_hash_names)
            except Exception as api_error:
                self.logger.error(f"批量API调用失败: {api_error}")
                raise
            
            successful = 0
            failed = 0
            
            for task in batch_tasks:
                try:
                    response = price_responses.get(task.market_hash_name)
                    if response and response.success:
                        # 保存价格数据（过滤零价格）
                        await self._save_price_data(task, response)
                        task.last_update = datetime.now()
                        successful += 1
                    else:
                        task.retry_count += 1
                        failed += 1
                        
                except Exception as e:
                    self.logger.error(f"处理任务失败: {task.market_hash_name}, 错误: {e}")
                    task.retry_count += 1
                    failed += 1
                finally:
                    self.processing_tasks.discard(task.item_id)
            
            return {'processed': len(batch_tasks), 'successful': successful, 'failed': failed}
            
        except Exception as e:
            self.logger.error(f"批量处理失败: {e}")
            for task in batch_tasks:
                task.retry_count += 1
                self.processing_tasks.discard(task.item_id)
            return {'processed': len(batch_tasks), 'successful': 0, 'failed': len(batch_tasks)}
    
    async def _save_price_data(self, task: UpdateTask, response):
        """保存价格数据，过滤零价格"""
        saved_count = 0
        
        for platform_price in response.platform_prices:
            # 过滤零价格数据
            if platform_price.sell_price <= 0 and platform_price.bidding_price <= 0:
                continue
            
            try:
                self.platform_price_dao.save_platform_price(
                    item_id=task.item_id,
                    market_hash_name=task.market_hash_name,
                    platform=platform_price.platform,
                    platform_item_id=platform_price.platform_item_id,
                    sell_price=platform_price.sell_price,
                    sell_count=platform_price.sell_count,
                    bidding_price=platform_price.bidding_price,
                    bidding_count=platform_price.bidding_count,
                    steamdt_update_time=platform_price.update_time,
                    query_time=response.query_time
                )
                saved_count += 1
            except Exception as e:
                self.logger.error(f"保存价格数据失败: {task.market_hash_name} - {platform_price.platform}, 错误: {e}")
        
        if saved_count > 0:
            self.logger.debug(f"保存了 {saved_count} 条有效价格记录: {task.market_hash_name}")
    
    async def run_continuous_update(self):
        """持续运行更新循环"""
        self.logger.info("🔄 开始持续价格更新循环")

        try:
            while self.is_running and self.continuous_mode:
                try:
                    cycle_stats = await self._run_single_cycle()

                    # 记录统计信息
                    if cycle_stats['high_priority_processed'] > 0 or cycle_stats['low_priority_processed'] > 0:
                        self.logger.info(f"📊 更新周期完成: 高优先级{cycle_stats['high_priority_processed']}个, "
                                       f"低优先级{cycle_stats['low_priority_processed']}个, "
                                       f"耗时{cycle_stats['duration']:.1f}秒")

                    # 短暂休息，避免过度占用CPU
                    await asyncio.sleep(0.5)

                except asyncio.CancelledError:
                    self.logger.info("🛑 持续更新循环被取消")
                    break
                except Exception as e:
                    self.logger.error(f"更新循环异常: {e}", exc_info=True)
                    await asyncio.sleep(5)  # 异常时等待更长时间

        except asyncio.CancelledError:
            self.logger.info("🛑 持续更新循环被取消")
        finally:
            self.logger.info("🛑 持续价格更新循环已停止")

    async def _run_single_cycle(self) -> Dict[str, Any]:
        """运行单次更新周期"""
        cycle_stats = {
            'high_priority_processed': 0,
            'low_priority_processed': 0,
            'start_time': datetime.now()
        }

        now = datetime.now()

        # 1. 优先处理高优先级任务（收藏饰品）
        high_priority_tasks = [
            task for task in self.high_priority_queue
            if task.should_update(now) and task.item_id not in self.processing_tasks
        ]

        if high_priority_tasks:
            # 标记为处理中
            for task in high_priority_tasks:
                self.processing_tasks.add(task.item_id)

            result = await self._process_batch_tasks(high_priority_tasks)
            cycle_stats['high_priority_processed'] = result['processed']
            self.update_stats['high_priority_processed'] += result['successful']

        # 2. 处理低优先级任务（如果还有调用额度）
        elif self._can_make_api_call(is_batch=True):  # 只有高优先级没有任务时才处理低优先级
            low_priority_tasks = [
                task for task in self.low_priority_queue
                if task.should_update(now) and task.item_id not in self.processing_tasks
            ][:self.api_limits['batch_size']]  # 使用完整批量大小

            if low_priority_tasks:
                # 标记为处理中
                for task in low_priority_tasks:
                    self.processing_tasks.add(task.item_id)

                result = await self._process_batch_tasks(low_priority_tasks)
                cycle_stats['low_priority_processed'] = result['processed']
                self.update_stats['low_priority_processed'] += result['successful']

        cycle_stats['end_time'] = datetime.now()
        cycle_stats['duration'] = (cycle_stats['end_time'] - cycle_stats['start_time']).total_seconds()

        return cycle_stats

    async def run_update_cycle(self) -> Dict[str, Any]:
        """运行一次更新周期（兼容性方法）"""
        if not self.is_running:
            return {'message': '更新池未运行'}

        return await self._run_single_cycle()
    
    async def start(self):
        """启动更新池"""
        if self.is_running:
            self.logger.warning("更新池已在运行")
            return

        self.logger.info("🚀 启动智能价格更新池")
        self.is_running = True

        # 初始化队列
        await self.initialize_queues()

        # 启动持续更新循环（在后台运行）
        if self.continuous_mode:
            self.continuous_task = asyncio.create_task(self.run_continuous_update())

        self.logger.info("✅ 智能价格更新池启动成功")
    
    def stop(self):
        """停止更新池"""
        self.logger.info("⏹️ 停止智能价格更新池")
        self.is_running = False

        # 取消持续更新任务
        if self.continuous_task and not self.continuous_task.done():
            self.continuous_task.cancel()
            self.logger.info("✅ 持续更新任务已取消")
    
    def get_status(self) -> Dict[str, Any]:
        """获取更新池状态"""
        return {
            'is_running': self.is_running,
            'queue_status': {
                'high_priority_queue': len(self.high_priority_queue),
                'low_priority_queue': len(self.low_priority_queue),
                'processing_tasks': len(self.processing_tasks)
            },
            'api_limits': self.api_limits,
            'call_stats': self.call_stats,
            'update_stats': self.update_stats
        }
