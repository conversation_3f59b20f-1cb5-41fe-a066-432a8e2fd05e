#!/usr/bin/env python3
"""
定时任务管理器
实现自动定时爬取和检查功能
"""

import asyncio
import schedule
import time
from datetime import datetime, date, timedelta
from threading import Thread
import sys
import os
from pathlib import Path

# 添加项目根路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.scraper.scraping_manager import ScrapingManager
from src.cs2_investment.scraper.steamdt_scraper_final import SteamDTScraperFinal

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)

class SchedulerManager:
    """定时任务管理器"""
    
    def __init__(self):
        self.scraping_manager = ScrapingManager()
        self.scraper = SteamDTScraperFinal()
        self.is_running = False
        self.scheduler_thread = None
        
        # 任务配置
        self.config = {
            'daily_scraping_enabled': True,   # 每日爬取开关
            'retry_interval_minutes': 30,     # 失败重试间隔(分钟)
            'max_retry_attempts': 10,         # 最大重试次数
            'max_daily_items': 1000,          # 每日最大处理饰品数
            'batch_size': 50,                 # 批处理大小
        }

        # 运行状态
        self.daily_scraping_completed = False  # 今日爬取是否完成
        self.last_scraping_date = None         # 上次爬取日期
        self.is_scraping_running = False       # 是否有抓取任务正在运行（并发控制）
        
        logger.info("定时任务管理器初始化完成")
    
    def setup_schedules(self):
        """设置定时任务"""
        # 每30分钟检查是否需要执行爬取或重试失败的任务
        schedule.every(self.config['retry_interval_minutes']).minutes.do(self.check_and_execute_scraping)

        # 每天晚上11点清理日志
        schedule.every().day.at("23:00").do(self.cleanup_logs)

        logger.info("定时任务设置完成 - 每30分钟检查爬取状态")

    def check_and_execute_scraping(self):
        """检查并执行爬取任务"""
        # 并发控制：如果已有抓取任务在运行，跳过本次检查
        if self.is_scraping_running:
            logger.info("⚠️ 检测到抓取任务正在运行，跳过本次检查以避免并发执行")
            return

        current_date = date.today()

        # 检查是否需要开始新的一天的爬取
        if self.last_scraping_date != current_date:
            logger.info(f"开始新的一天的爬取任务: {current_date}")
            self.daily_scraping_completed = False
            self.last_scraping_date = current_date

        # 如果今日爬取已完成，跳过
        if self.daily_scraping_completed:
            logger.debug("今日爬取已完成，跳过检查")
            return

        # 检查并创建今日爬取任务（如果不存在）
        if not self.scraping_manager.check_and_create_daily_tasks():
            logger.error("创建每日爬取任务失败")
            return

        # 获取所有需要处理的任务（包括pending和failed）
        items_to_process = self.scraping_manager.get_items_for_daily_scraping(
            limit=self.config['max_daily_items'],
            include_failed=True
        )

        if not items_to_process:
            logger.info("没有需要处理的任务，今日爬取任务完成")
            self.daily_scraping_completed = True
            self.scraping_manager.record_daily_scraping_completion(current_date)
            return

        # 统计任务类型
        pending_count = sum(1 for item in items_to_process if item.get('status') == 'pending')
        failed_count = sum(1 for item in items_to_process if item.get('status') == 'failed')

        logger.info(f"开始处理 {len(items_to_process)} 个任务 (pending: {pending_count}, failed重试: {failed_count})")

        # 分批处理所有任务
        for i in range(0, len(items_to_process), self.config['batch_size']):
            batch = items_to_process[i:i + self.config['batch_size']]
            self.process_batch(batch, "daily_scraping")

            # 批次间休息
            time.sleep(10)

        # 最终检查是否还有失败的任务
        remaining_failed = self.scraping_manager.get_failed_items_for_retry(
            max_attempts=self.config['max_retry_attempts']
        )

        if not remaining_failed:
            logger.info("所有爬取任务完成，今日爬取结束")
            self.daily_scraping_completed = True
            self.scraping_manager.record_daily_scraping_completion(current_date)
        else:
            logger.info(f"还有 {len(remaining_failed)} 个任务失败，将在下次检查时重试")

    def daily_full_scan(self):
        """每日全量扫描 - 已弃用，保留用于兼容性"""
        logger.warning("daily_full_scan 方法已弃用，请使用 check_and_execute_scraping")
        self.check_and_execute_scraping()
    
    def hourly_check(self):
        """每小时增量检查 - 已弃用，保留用于兼容性"""
        logger.warning("hourly_check 方法已弃用，现在使用统一的爬取检查机制")
        # 不执行任何操作，因为新的机制会自动处理
    
    def priority_check(self):
        """优先级饰品检查 - 已弃用，保留用于兼容性"""
        logger.warning("priority_check 方法已弃用，现在使用统一的爬取检查机制")
        # 不执行任何操作，因为新的机制会自动处理
    
    def process_batch(self, items, task_type):
        """处理任务批次"""
        logger.info(f"开始处理批次: {len(items)} 个任务 (任务类型: {task_type})")

        # 检查是否是排行榜任务
        if items and 'ranking_key' in items[0]:
            logger.info("检测到排行榜任务，使用排行榜处理逻辑")
            self.process_ranking_tasks(items)
        else:
            logger.info("检测到饰品任务，使用饰品处理逻辑")
            self.process_item_tasks(items, task_type)

    def process_ranking_tasks(self, ranking_tasks):
        """处理排行榜任务 - 调用真正的爬虫"""
        logger.info(f"🚀 开始真正处理 {len(ranking_tasks)} 个排行榜任务")

        # 设置运行状态标志，防止并发执行
        self.is_scraping_running = True
        logger.info("🔒 设置抓取运行状态，防止并发执行")

        try:
            # 导入并使用SteamDT爬虫
            from src.cs2_investment.scraper.steamdt_scraper_final import SteamDTScraperFinal

            logger.info("📦 初始化SteamDT爬虫...")
            scraper = SteamDTScraperFinal()

            logger.info("🎯 启动续爬模式，处理数据库中的pending任务...")
            # 使用续爬模式，会自动处理数据库中状态为pending的任务
            result = asyncio.run(scraper.run(resume_mode=True))

            logger.info("✅ 排行榜爬取任务执行完成")

        except Exception as e:
            logger.error(f"❌ 执行排行榜爬取失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

        finally:
            # 无论成功还是失败，都要清除运行状态标志
            self.is_scraping_running = False
            logger.info("🔓 清除抓取运行状态，允许下次执行")

    def process_item_tasks(self, items, task_type):
        """处理饰品任务"""
        logger.info(f"开始处理 {len(items)} 个饰品任务")

        # 设置运行状态标志，防止并发执行
        self.is_scraping_running = True
        logger.info("🔒 设置抓取运行状态，防止并发执行")

        success_count = 0
        error_count = 0

        for item in items:
            try:
                # 构造URL
                url = f"https://steamdt.com/cs2/{item['item_id']}"

                # 执行抓取
                result = asyncio.run(self.scraper.scrape_item_data(
                    url, item['item_name']
                ))

                if result.get('success'):
                    success_count += 1
                    logger.info(f"✅ 成功处理: {item['item_name']}")

                    # 记录成功
                    self.scraping_manager.record_scraping_success(
                        item['item_id'], task_type
                    )
                else:
                    error_count += 1
                    logger.warning(f"❌ 处理失败: {item['item_name']} - {result.get('error')}")

                    # 记录失败
                    self.scraping_manager.record_scraping_error(
                        item['item_id'], task_type, result.get('error')
                    )

                # 请求间隔
                time.sleep(2)

            except Exception as e:
                error_count += 1
                logger.error(f"❌ 处理异常: {item['item_name']} - {e}")

                # 记录异常
                self.scraping_manager.record_scraping_error(
                    item['item_id'], task_type, str(e)
                )

        logger.info(f"饰品批次处理完成: 成功 {success_count}, 失败 {error_count}")

        # 清除运行状态标志
        self.is_scraping_running = False
        logger.info("🔓 清除抓取运行状态，允许下次执行")


    def cleanup_logs(self):
        """清理日志文件"""
        logger.info("开始清理日志文件")
        
        try:
            # 清理超过7天的日志
            cutoff_date = datetime.now() - timedelta(days=7)
            
            # 这里可以添加具体的日志清理逻辑
            # 例如删除旧的日志文件、压缩日志等
            
            logger.info("日志清理完成")
            
        except Exception as e:
            logger.error(f"日志清理失败: {e}")
    
    def start(self):
        """启动定时任务"""
        if self.is_running:
            logger.warning("定时任务已在运行中")
            return

        logger.info("启动定时任务管理器")

        # 设置定时任务
        self.setup_schedules()

        # 启动调度器线程
        self.is_running = True
        self.scheduler_thread = Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()

        # 立即执行一次爬取检查（在单独线程中执行，避免阻塞启动）
        initial_check_thread = Thread(target=self._initial_check, daemon=True)
        initial_check_thread.start()

        logger.info("定时任务管理器启动成功")
    
    def stop(self):
        """停止定时任务"""
        if not self.is_running:
            logger.warning("定时任务未在运行")
            return
        
        logger.info("停止定时任务管理器")
        
        self.is_running = False
        
        # 清空所有任务
        schedule.clear()
        
        logger.info("定时任务管理器已停止")
    
    def _run_scheduler(self):
        """运行调度器"""
        logger.info("调度器线程启动")
        
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"调度器运行异常: {e}")
                time.sleep(60)
        
        logger.info("调度器线程结束")

    def _initial_check(self):
        """启动时的初始检查"""
        try:
            logger.info("🚀 执行启动时的初始爬取检查...")
            # 等待2秒确保系统完全启动
            time.sleep(2)
            # 执行爬取检查
            self.check_and_execute_scraping()
            logger.info("✅ 初始爬取检查完成")
        except Exception as e:
            logger.error(f"❌ 初始爬取检查失败: {e}")

    def get_status(self):
        """获取调度器状态"""
        return {
            'is_running': self.is_running,
            'is_scraping_running': self.is_scraping_running,  # 添加抓取运行状态
            'scheduled_jobs': len(schedule.jobs),
            'next_run': str(schedule.next_run()) if schedule.jobs else None,
            'daily_scraping_completed': self.daily_scraping_completed,
            'last_scraping_date': str(self.last_scraping_date) if self.last_scraping_date else None,
            'config': self.config
        }
    
    def get_statistics(self):
        """获取统计信息"""
        try:
            stats = self.scraping_manager.get_daily_statistics()
            return stats
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='定时任务管理器')
    parser.add_argument('action', choices=['start', 'stop', 'status'], help='操作类型')
    
    args = parser.parse_args()
    
    manager = SchedulerManager()
    
    if args.action == 'start':
        manager.start()
        try:
            # 保持主线程运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("收到停止信号")
            manager.stop()
    
    elif args.action == 'stop':
        manager.stop()
    
    elif args.action == 'status':
        status = manager.get_status()
        stats = manager.get_statistics()
        
        print("📊 定时任务管理器状态:")
        print(f"  运行状态: {'运行中' if status['is_running'] else '已停止'}")
        print(f"  计划任务数: {status['scheduled_jobs']}")
        print(f"  下次运行: {status['next_run']}")
        
        if stats:
            print("\n📈 今日统计:")
            print(f"  处理饰品数: {stats.get('processed_items', 0)}")
            print(f"  成功率: {stats.get('success_rate', 0):.1f}%")

if __name__ == "__main__":
    main()
