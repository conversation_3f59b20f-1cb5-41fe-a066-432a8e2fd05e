"""
SteamDT API客户端

提供与SteamDT开放平台的接口交互功能。
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))


@dataclass
class PlatformPriceData:
    """平台价格数据"""
    platform: str
    platform_item_id: str
    sell_price: float
    sell_count: int
    bidding_price: float
    bidding_count: int
    update_time: int


@dataclass
class ItemPriceResponse:
    """饰品价格响应"""
    market_hash_name: str
    platform_prices: List[PlatformPriceData]
    query_time: datetime
    success: bool
    error_message: str = ""


class SteamDTAPIClient:
    """SteamDT API客户端"""
    
    def __init__(self, api_key: str):
        """
        初始化API客户端
        
        Args:
            api_key: SteamDT API密钥
        """
        self.api_key = api_key
        self.base_url = "https://open.steamdt.com"
        self.logger = logging.getLogger(__name__)
        
        # 请求配置
        self.timeout = aiohttp.ClientTimeout(total=30)
        self.max_retries = 3
        self.retry_delay = 1.0
        
        # 批量请求限制
        self.batch_size = 100  # SteamDT API限制
        
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'CS2-Investment-Analysis/1.0'
        }
    
    async def get_single_price(self, market_hash_name: str) -> ItemPriceResponse:
        """
        获取单个饰品价格
        
        Args:
            market_hash_name: 饰品市场哈希名称
            
        Returns:
            ItemPriceResponse: 价格响应数据
        """
        url = f"{self.base_url}/open/cs2/v1/price/single"
        params = {'marketHashName': market_hash_name}
        
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                for attempt in range(self.max_retries):
                    try:
                        async with session.get(url, params=params, headers=self._get_headers()) as response:
                            if response.status == 200:
                                data = await response.json()
                                return self._parse_single_response(market_hash_name, data)
                            else:
                                error_text = await response.text()
                                self.logger.warning(f"API请求失败 (状态码: {response.status}): {error_text}")
                                
                    except asyncio.TimeoutError:
                        self.logger.warning(f"API请求超时 (尝试 {attempt + 1}/{self.max_retries}): {market_hash_name}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                            continue
                        
                    except Exception as e:
                        self.logger.error(f"API请求异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                            continue
                
                # 所有重试都失败
                return ItemPriceResponse(
                    market_hash_name=market_hash_name,
                    platform_prices=[],
                    query_time=datetime.now(),
                    success=False,
                    error_message="API请求失败，已达到最大重试次数"
                )
                
        except Exception as e:
            self.logger.error(f"获取单个价格失败: {market_hash_name}, 错误: {e}")
            return ItemPriceResponse(
                market_hash_name=market_hash_name,
                platform_prices=[],
                query_time=datetime.now(),
                success=False,
                error_message=str(e)
            )
    
    async def get_batch_prices(self, market_hash_names: List[str]) -> Dict[str, ItemPriceResponse]:
        """
        批量获取饰品价格
        
        Args:
            market_hash_names: 饰品市场哈希名称列表
            
        Returns:
            Dict[str, ItemPriceResponse]: 饰品名称到价格响应的映射
        """
        results = {}
        
        # 分批处理，每批最多100个
        for i in range(0, len(market_hash_names), self.batch_size):
            batch = market_hash_names[i:i + self.batch_size]
            batch_results = await self._get_batch_chunk(batch)
            results.update(batch_results)
            
            # 避免API限制，添加延迟
            if i + self.batch_size < len(market_hash_names):
                await asyncio.sleep(0.5)
        
        return results
    
    async def _get_batch_chunk(self, market_hash_names: List[str]) -> Dict[str, ItemPriceResponse]:
        """
        获取一批饰品价格（内部方法）
        
        Args:
            market_hash_names: 饰品市场哈希名称列表（最多100个）
            
        Returns:
            Dict[str, ItemPriceResponse]: 饰品名称到价格响应的映射
        """
        url = f"{self.base_url}/open/cs2/v1/price/batch"
        payload = {'marketHashNames': market_hash_names}
        
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                for attempt in range(self.max_retries):
                    try:
                        async with session.post(url, json=payload, headers=self._get_headers()) as response:
                            if response.status == 200:
                                data = await response.json()
                                return self._parse_batch_response(data)
                            else:
                                error_text = await response.text()
                                self.logger.warning(f"批量API请求失败 (状态码: {response.status}): {error_text}")
                                
                    except asyncio.TimeoutError:
                        self.logger.warning(f"批量API请求超时 (尝试 {attempt + 1}/{self.max_retries})")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                            continue
                            
                    except Exception as e:
                        self.logger.error(f"批量API请求异常 (尝试 {attempt + 1}/{self.max_retries}): {e}")
                        if attempt < self.max_retries - 1:
                            await asyncio.sleep(self.retry_delay * (attempt + 1))
                            continue
                
                # 所有重试都失败，返回失败响应
                return {
                    name: ItemPriceResponse(
                        market_hash_name=name,
                        platform_prices=[],
                        query_time=datetime.now(),
                        success=False,
                        error_message="批量API请求失败，已达到最大重试次数"
                    )
                    for name in market_hash_names
                }
                
        except Exception as e:
            self.logger.error(f"批量获取价格失败: {e}")
            return {
                name: ItemPriceResponse(
                    market_hash_name=name,
                    platform_prices=[],
                    query_time=datetime.now(),
                    success=False,
                    error_message=str(e)
                )
                for name in market_hash_names
            }
    
    def _parse_single_response(self, market_hash_name: str, data: Dict[str, Any]) -> ItemPriceResponse:
        """解析单个价格响应"""
        query_time = datetime.now()
        
        if not data.get('success', False):
            return ItemPriceResponse(
                market_hash_name=market_hash_name,
                platform_prices=[],
                query_time=query_time,
                success=False,
                error_message=data.get('errorMsg', '未知错误')
            )
        
        platform_prices = []
        for item in data.get('data', []):
            try:
                platform_price = PlatformPriceData(
                    platform=item.get('platform', ''),
                    platform_item_id=item.get('platformItemId', ''),
                    sell_price=float(item.get('sellPrice', 0)),
                    sell_count=int(item.get('sellCount', 0)),
                    bidding_price=float(item.get('biddingPrice', 0)),
                    bidding_count=int(item.get('biddingCount', 0)),
                    update_time=int(item.get('updateTime', 0))
                )
                platform_prices.append(platform_price)
            except (ValueError, TypeError) as e:
                self.logger.warning(f"解析平台价格数据失败: {e}, 数据: {item}")
                continue
        
        return ItemPriceResponse(
            market_hash_name=market_hash_name,
            platform_prices=platform_prices,
            query_time=query_time,
            success=True
        )
    
    def _parse_batch_response(self, data: Dict[str, Any]) -> Dict[str, ItemPriceResponse]:
        """解析批量价格响应"""
        results = {}
        query_time = datetime.now()
        
        if not data.get('success', False):
            error_message = data.get('errorMsg', '未知错误')
            self.logger.error(f"批量API请求失败: {error_message}")
            return results
        
        for item in data.get('data', []):
            market_hash_name = item.get('marketHashName', '')
            if not market_hash_name:
                continue
                
            platform_prices = []
            for price_item in item.get('dataList', []):
                try:
                    platform_price = PlatformPriceData(
                        platform=price_item.get('platform', ''),
                        platform_item_id=price_item.get('platformItemId', ''),
                        sell_price=float(price_item.get('sellPrice', 0)),
                        sell_count=int(price_item.get('sellCount', 0)),
                        bidding_price=float(price_item.get('biddingPrice', 0)),
                        bidding_count=int(price_item.get('biddingCount', 0)),
                        update_time=int(price_item.get('updateTime', 0))
                    )
                    platform_prices.append(platform_price)
                except (ValueError, TypeError) as e:
                    self.logger.warning(f"解析平台价格数据失败: {e}, 数据: {price_item}")
                    continue
            
            results[market_hash_name] = ItemPriceResponse(
                market_hash_name=market_hash_name,
                platform_prices=platform_prices,
                query_time=query_time,
                success=True
            )
        
        return results
    
    async def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 使用一个常见的饰品名称进行测试
            test_item = "AK-47 | Redline (Field-Tested)"
            response = await self.get_single_price(test_item)
            return response.success
        except Exception as e:
            self.logger.error(f"API连接测试失败: {e}")
            return False
