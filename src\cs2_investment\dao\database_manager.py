"""
数据库管理器

提供数据库初始化、表创建、索引管理等功能。
"""

from sqlalchemy import text
from loguru import logger

from ..config.database import (
    create_database_engine, 
    get_db_session, 
    initialize_database,
    create_database_if_not_exists
)
from ..models import (
    Base, Item, MarketSnapshot, PlatformPrice,
    DataSource, ScreeningResult, Favorite,
    AnalysisResult, RealtimeAnalysisResult
)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.logger = logger.bind(manager="DatabaseManager")
    
    def initialize_database(self) -> bool:
        """初始化数据库"""
        try:
            # 创建数据库（如果不存在）
            create_database_if_not_exists()
            
            # 初始化连接
            if not initialize_database():
                return False
            
            self.logger.info("数据库初始化成功")
            return True
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            return False
    
    def create_tables(self) -> bool:
        """创建所有表"""
        try:
            engine = create_database_engine()
            
            # 创建所有表
            Base.metadata.create_all(engine)
            
            self.logger.info("数据库表创建成功")
            return True
        except Exception as e:
            self.logger.error(f"创建数据库表失败: {e}")
            return False
    
    def drop_tables(self) -> bool:
        """删除所有表（谨慎使用）"""
        try:
            engine = create_database_engine()
            
            # 删除所有表
            Base.metadata.drop_all(engine)
            
            self.logger.warning("数据库表已删除")
            return True
        except Exception as e:
            self.logger.error(f"删除数据库表失败: {e}")
            return False
    
    def create_indexes(self) -> bool:
        """创建额外的索引"""
        try:
            with get_db_session() as session:
                # 创建全文索引（MySQL特定）
                indexes = [
                    "CREATE FULLTEXT INDEX idx_items_name_search ON items(name, market_hash_name)",
                    "CREATE INDEX idx_market_snapshots_hot_rank ON market_snapshots(hot_rank) WHERE hot_rank IS NOT NULL",
                    "CREATE INDEX idx_market_snapshots_trans_amount_3m ON market_snapshots(trans_amount_3m) WHERE trans_amount_3m IS NOT NULL",
                    "CREATE INDEX idx_screening_results_score_desc ON screening_results(score DESC) WHERE score IS NOT NULL",
                    "CREATE INDEX idx_market_snapshots_ranking_count ON market_snapshots((JSON_LENGTH(ranking_info))) WHERE ranking_info IS NOT NULL",
                ]
                
                for index_sql in indexes:
                    try:
                        session.execute(text(index_sql))
                        self.logger.info(f"创建索引成功: {index_sql}")
                    except Exception as e:
                        # 索引可能已存在，记录警告但继续
                        self.logger.warning(f"创建索引跳过: {e}")
                
                return True
        except Exception as e:
            self.logger.error(f"创建索引失败: {e}")
            return False

    def add_ranking_info_column(self) -> bool:
        """添加排行榜信息字段"""
        try:
            with get_db_session() as session:
                column_sql = "ALTER TABLE market_snapshots ADD COLUMN ranking_info JSON COMMENT '排行榜排名信息'"
                try:
                    session.execute(text(column_sql))
                    self.logger.info("添加ranking_info字段成功")
                    return True
                except Exception as e:
                    self.logger.warning(f"字段可能已存在: {e}")
                    return True
        except Exception as e:
            self.logger.error(f"添加字段失败: {e}")
            return False

    def create_partitions(self) -> bool:
        """创建分区表（MySQL特定）"""
        try:
            with get_db_session() as session:
                # 为market_snapshots表创建按月分区
                partition_sql = """
                ALTER TABLE market_snapshots 
                PARTITION BY RANGE (YEAR(snapshot_time) * 100 + MONTH(snapshot_time)) (
                    PARTITION p202501 VALUES LESS THAN (202502),
                    PARTITION p202502 VALUES LESS THAN (202503),
                    PARTITION p202503 VALUES LESS THAN (202504),
                    PARTITION p202504 VALUES LESS THAN (202505),
                    PARTITION p202505 VALUES LESS THAN (202506),
                    PARTITION p202506 VALUES LESS THAN (202507),
                    PARTITION p202507 VALUES LESS THAN (202508),
                    PARTITION p202508 VALUES LESS THAN (202509),
                    PARTITION p202509 VALUES LESS THAN (202510),
                    PARTITION p202510 VALUES LESS THAN (202511),
                    PARTITION p202511 VALUES LESS THAN (202512),
                    PARTITION p202512 VALUES LESS THAN (202601),
                    PARTITION p_future VALUES LESS THAN MAXVALUE
                )
                """
                
                try:
                    session.execute(text(partition_sql))
                    self.logger.info("创建分区表成功")
                except Exception as e:
                    self.logger.warning(f"创建分区表跳过: {e}")
                
                return True
        except Exception as e:
            self.logger.error(f"创建分区表失败: {e}")
            return False
    
    def initialize_data_sources(self) -> bool:
        """初始化数据源配置"""
        try:
            with get_db_session() as session:
                # 检查是否已有数据源
                existing_count = session.query(DataSource).count()
                if existing_count > 0:
                    self.logger.info("数据源配置已存在，跳过初始化")
                    return True
                
                # 初始化数据源
                data_sources = [
                    {
                        'source_name': '1天成交额榜单',
                        'source_type': 'ranking',
                        'description': '1天成交额排行榜数据',
                        'update_frequency': 'daily'
                    },
                    {
                        'source_name': '热度榜单',
                        'source_type': 'ranking',
                        'description': '热度排行榜数据',
                        'update_frequency': 'daily'
                    },
                    {
                        'source_name': '1天在售变化榜',
                        'source_type': 'ranking',
                        'description': '1天在售数量变化排行榜',
                        'update_frequency': 'daily'
                    },
                    {
                        'source_name': '7天价格上涨榜',
                        'source_type': 'ranking',
                        'description': '7天价格上涨排行榜',
                        'update_frequency': 'daily'
                    },
                    {
                        'source_name': '30天成交额榜单',
                        'source_type': 'ranking',
                        'description': '30天成交额排行榜数据',
                        'update_frequency': 'daily'
                    }
                ]
                
                for source_data in data_sources:
                    source = DataSource(**source_data)
                    session.add(source)
                
                session.flush()
                self.logger.info(f"初始化数据源配置: {len(data_sources)}个")
                return True
        except Exception as e:
            self.logger.error(f"初始化数据源配置失败: {e}")
            return False
    
    def get_database_info(self) -> dict:
        """获取数据库信息"""
        try:
            with get_db_session() as session:
                # 获取表信息
                tables_info = {}
                for table_name in ['items', 'market_snapshots', 'platform_prices', 'screening_results']:
                    count_sql = f"SELECT COUNT(*) FROM {table_name}"
                    result = session.execute(text(count_sql)).scalar()
                    tables_info[table_name] = result
                
                # 获取数据库大小
                size_sql = """
                SELECT 
                    ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS size_mb
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                """
                db_size = session.execute(text(size_sql)).scalar()
                
                return {
                    'tables': tables_info,
                    'database_size_mb': float(db_size) if db_size else 0
                }
        except Exception as e:
            self.logger.error(f"获取数据库信息失败: {e}")
            return {}
    
    def setup_database(self) -> bool:
        """完整的数据库设置"""
        try:
            self.logger.info("开始数据库设置...")
            
            # 1. 初始化数据库
            if not self.initialize_database():
                return False
            
            # 2. 创建表
            if not self.create_tables():
                return False
            
            # 3. 创建索引
            if not self.create_indexes():
                return False
            
            # 4. 初始化数据源
            if not self.initialize_data_sources():
                return False
            
            self.logger.info("数据库设置完成")
            return True
        except Exception as e:
            self.logger.error(f"数据库设置失败: {e}")
            return False
