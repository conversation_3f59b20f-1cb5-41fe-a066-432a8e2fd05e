"""
数据模型定义

定义抓取数据的结构化模型
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from datetime import datetime


@dataclass
class ItemInfo:
    """饰品基本信息"""
    item_id: str
    name: str
    market_hash_name: str
    wear: str
    rarity: str
    current_price: float
    price_change: float
    price_change_percent: float
    volume_24h: int
    inventory_count: int
    
    
@dataclass
class TrendDataPoint:
    """走势数据点"""
    timestamp: int
    price: float
    inventory_count: int
    volume: int
    date_str: str


@dataclass
class TrendData:
    """走势数据"""
    item_id: str
    platform: str
    time_range: str  # 近6个月
    data_points: List[TrendDataPoint]
    collected_at: datetime
    raw_data: List = None  # 原始API响应数据
    

@dataclass
class KlineDataPoint:
    """K线数据点"""
    timestamp: int
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: int
    date_str: str


@dataclass
class KlineData:
    """K线数据"""
    item_id: str
    kline_type: str  # 时K、日K、周K
    platform: str
    data_points: List[KlineDataPoint]
    collected_at: datetime
    raw_data: List = None  # 原始API响应数据


@dataclass
class ScrapingResult:
    """抓取结果"""
    success: bool
    item_info: Optional[ItemInfo] = None
    trend_data: Optional[TrendData] = None  # 主要趋势数据（向后兼容）
    trend_data_3m: Optional[TrendData] = None  # 3个月趋势数据（小时级，用于ssync系统）
    trend_data_6m: Optional[TrendData] = None  # 6个月趋势数据（日级，用于syncps系统）
    hourly_kline: Optional[KlineData] = None
    daily_kline: Optional[KlineData] = None  # 保持向后兼容，使用第一次日K数据
    daily_kline_1: Optional[KlineData] = None  # 日K数据第一次响应
    daily_kline_2: Optional[KlineData] = None  # 日K数据第二次响应
    weekly_kline: Optional[KlineData] = None
    error_message: Optional[str] = None
    collected_at: Optional[datetime] = None
