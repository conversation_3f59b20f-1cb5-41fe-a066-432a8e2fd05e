#!/usr/bin/env python3
"""
智能定时任务调度器

支持状态记录、中断恢复、定时启动
"""

import asyncio
import logging
import random
import sys
from datetime import datetime, date, time, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.dao.favorite_dao import FavoriteDAO
from src.cs2_investment.dao.analysis_log_dao import analysis_log_dao, daily_schedule_dao
from src.cs2_investment.api.clients.api_client import api_client
from src.cs2_investment.fx.integrated_analysis_system import IntegratedAnalysisSystem
from src.cs2_investment.fx.integrated_ssync_system import IntegratedSSyncSystem
from src.cs2_investment.scheduler.streamlit_compatible_scraper import streamlit_scraper

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)


class SmartTaskScheduler:
    """智能定时任务调度器"""
    
    def __init__(self):
        """初始化调度器"""
        self.is_running = False
        self.regular_analysis_task = None
        self.realtime_monitor_task = None
        
        # 任务配置
        self.daily_start_time = time(8, 0)  # 每天8点开始常规分析
        self.realtime_interval_hours = 1    # 实时监控每1小时执行一次
        self.item_delay_seconds = (15, 30)  # 饰品之间间隔15-30秒
        self.max_retry_count = 3            # 最大重试次数
        
        # DAO实例
        self.item_dao = ItemDAO()
        self.favorite_dao = FavoriteDAO()
        
        # 系统实例（保留作为备用）
        self.regular_system = IntegratedAnalysisSystem()
        self.realtime_system = IntegratedSSyncSystem()

        # API客户端（主要使用）
        self.api_client = api_client
        self.use_api = True  # 默认使用API调用

        logger.info("🚀 智能定时任务调度器初始化完成")
    
    async def start(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("调度器已在运行中")
            return

        # 检查API服务状态
        if self.use_api:
            if self.api_client.check_service_health():
                logger.info("✅ API服务连接正常，使用API调用模式")
            else:
                logger.warning("⚠️ API服务不可用，切换到直接调用模式")
                self.use_api = False
        
        self.is_running = True
        logger.info("🚀 启动智能定时任务调度器")
        
        # 检查并恢复未完成的分析
        await self._check_and_resume_analysis()
        
        # 启动常规分析任务
        self.regular_analysis_task = asyncio.create_task(
            self._daily_analysis_loop()
        )
        
        # 启动实时监控任务
        self.realtime_monitor_task = asyncio.create_task(
            self._realtime_monitor_loop()
        )
        
        logger.info("✅ 智能定时任务调度器启动完成")
        
        # 等待任务完成
        try:
            await asyncio.gather(
                self.regular_analysis_task,
                self.realtime_monitor_task
            )
        except asyncio.CancelledError:
            logger.info("定时任务被取消")
    
    async def stop(self):
        """停止调度器"""
        if not self.is_running:
            logger.warning("调度器未在运行")
            return
        
        logger.info("🛑 停止智能定时任务调度器")
        self.is_running = False
        
        # 取消任务
        if self.regular_analysis_task:
            self.regular_analysis_task.cancel()
        
        if self.realtime_monitor_task:
            self.realtime_monitor_task.cancel()
        
        # 等待任务完全停止
        try:
            if self.regular_analysis_task:
                await self.regular_analysis_task
        except asyncio.CancelledError:
            pass
        
        try:
            if self.realtime_monitor_task:
                await self.realtime_monitor_task
        except asyncio.CancelledError:
            pass
        
        logger.info("✅ 智能定时任务调度器已停止")
    
    async def _check_and_resume_analysis(self):
        """检查并恢复未完成的分析"""
        logger.info("🔍 检查未完成的分析任务...")
        
        today = date.today()
        
        # 检查今天的常规分析
        progress = analysis_log_dao.get_analysis_progress('regular', today)
        if progress['pending'] > 0 or progress['running'] > 0:
            logger.info(f"📊 发现未完成的常规分析: 待处理={progress['pending']}, 运行中={progress['running']}")
            # 重置运行中的任务为待处理
            await self._reset_running_tasks('regular', today)
        
        # 检查失败的任务
        failed_tasks = analysis_log_dao.get_failed_analysis('regular', today)
        if failed_tasks:
            logger.info(f"⚠️ 发现失败的分析任务: {len(failed_tasks)} 个")
    
    async def _reset_running_tasks(self, analysis_type: str, scheduled_date: date):
        """重置运行中的任务为待处理状态"""
        # 这里需要实现重置逻辑
        logger.info(f"🔄 重置运行中的{analysis_type}任务为待处理状态")
    
    async def _daily_analysis_loop(self):
        """每日分析循环任务"""
        logger.info("📊 启动每日分析定时任务")
        
        while self.is_running:
            try:
                now = datetime.now()
                today = now.date()
                
                # 计算今天的开始时间
                today_start = datetime.combine(today, self.daily_start_time)
                
                # 如果还没到今天的开始时间，等待
                if now < today_start:
                    wait_seconds = (today_start - now).total_seconds()
                    logger.info(f"⏰ 等待到 {today_start.strftime('%H:%M:%S')} 开始今日分析，还需等待 {wait_seconds:.0f} 秒")
                    await asyncio.sleep(wait_seconds)
                    continue
                
                # 检查今天是否已经有分析计划
                daily_schedule = daily_schedule_dao.get_daily_schedule(today)
                if not daily_schedule:
                    # 创建今日分析计划
                    await self._create_daily_analysis_plan(today)
                    daily_schedule = daily_schedule_dao.get_daily_schedule(today)
                
                if daily_schedule and daily_schedule['status'] != 'completed':
                    # 执行今日分析
                    await self._execute_daily_analysis(today)
                
                # 等待到明天
                tomorrow = today + timedelta(days=1)
                tomorrow_start = datetime.combine(tomorrow, self.daily_start_time)
                wait_seconds = (tomorrow_start - datetime.now()).total_seconds()
                logger.info(f"✅ 今日分析完成，等待明天 {tomorrow_start.strftime('%Y-%m-%d %H:%M:%S')}")
                await asyncio.sleep(wait_seconds)
                
            except asyncio.CancelledError:
                logger.info("每日分析任务被取消")
                break
            except Exception as e:
                logger.error(f"每日分析循环出错: {e}")
                await asyncio.sleep(300)  # 5分钟后重试
    
    async def _create_daily_analysis_plan(self, target_date: date):
        """创建每日分析计划"""
        logger.info(f"📋 创建 {target_date} 的分析计划")

        try:
            # 获取所有饰品（使用字典格式避免Session问题）
            items = self._get_all_items_as_dict()

            if not items:
                logger.warning("没有找到饰品数据")
                return

            # 创建每日计划
            daily_schedule_dao.create_daily_schedule(target_date, len(items))

            # 为每个饰品创建分析日志
            scheduled_datetime = datetime.combine(target_date, self.daily_start_time)

            for item in items:
                try:
                    item_id = item['item_id']
                    item_name = item.get('name', item_id)

                    analysis_log_dao.create_analysis_log(
                        item_id=item_id,
                        item_name=item_name,
                        analysis_type='regular',
                        scheduled_date=scheduled_datetime
                    )
                except Exception as e:
                    logger.warning(f"创建饰品 {item.get('item_id', 'unknown')} 分析计划失败: {e}")
                    continue

            logger.info(f"✅ 创建分析计划完成，共 {len(items)} 个饰品")

        except Exception as e:
            logger.error(f"创建每日分析计划失败: {e}")
    
    async def _execute_daily_analysis(self, target_date: date):
        """执行每日分析"""
        logger.info(f"🚀 开始执行 {target_date} 的分析任务")
        
        # 更新每日计划状态
        daily_schedule = daily_schedule_dao.get_daily_schedule(target_date)
        if daily_schedule:
            daily_schedule_dao.update_daily_schedule(
                daily_schedule['id'],
                status='running',
                start_time=datetime.now()
            )
        
        try:
            # 获取待分析的任务
            pending_tasks = analysis_log_dao.get_pending_analysis('regular', target_date)

            if not pending_tasks:
                logger.info("✅ 没有待分析的任务")
                return

            # 为每个任务补充market_hash_name信息
            enriched_tasks = self._enrich_tasks_with_item_info(pending_tasks)

            logger.info(f"📋 开始分析，共 {len(enriched_tasks)} 个待处理饰品")
            
            completed_count = 0
            failed_count = 0
            
            # 逐个分析饰品
            for i, task in enumerate(enriched_tasks, 1):
                if not self.is_running:
                    break
                
                success = await self._run_single_analysis(task, i, len(pending_tasks))
                
                if success:
                    completed_count += 1
                else:
                    failed_count += 1
                
                # 饰品之间的随机间隔
                if i < len(enriched_tasks):
                    delay = random.randint(*self.item_delay_seconds)
                    logger.info(f"⏳ 等待 {delay} 秒后分析下一个饰品...")
                    await asyncio.sleep(delay)
            
            # 更新每日计划状态
            if daily_schedule:
                daily_schedule_dao.update_daily_schedule(
                    daily_schedule['id'],
                    status='completed',
                    end_time=datetime.now(),
                    completed_items=completed_count,
                    failed_items=failed_count
                )
            
            logger.info(f"✅ 每日分析完成: 成功={completed_count}, 失败={failed_count}")
            
        except Exception as e:
            logger.error(f"执行每日分析失败: {e}")
            if daily_schedule:
                daily_schedule_dao.update_daily_schedule(
                    daily_schedule['id'],
                    status='failed',
                    end_time=datetime.now()
                )
    
    async def _run_single_analysis(self, task: Dict[str, Any], current: int, total: int) -> bool:
        """运行单个饰品分析"""
        start_time = datetime.now()

        # 更新状态为运行中
        analysis_log_dao.update_analysis_status(
            task['id'],
            'running',
            start_time=start_time
        )

        try:
            logger.info(f"📊 [{current}/{total}] 开始分析: {task['item_name']}")

            # 调试：打印任务数据
            logger.info(f"任务数据: {task}")

            # 构造URL - 必须使用market_hash_name
            # 注意：不能使用中文名称(item_name)或数字ID构建URL
            market_hash_name = task.get('market_hash_name')
            if not market_hash_name:
                logger.error(f"饰品 {task['item_id']} 缺少 market_hash_name，无法构建URL")
                logger.error(f"任务数据: {task}")
                await self._update_task_status(task['id'], 'failed', '缺少market_hash_name')
                return {'success': False, 'error': '缺少market_hash_name'}

            url = f"https://steamdt.com/cs2/{market_hash_name}"
            logger.info(f"📍 URL: {url}")

            # 优先使用API调用
            if self.use_api:
                result = await self._run_api_analysis(
                    task['item_id'],
                    task['item_name'],
                    url,
                    task['id']
                )
            else:
                # 回退到直接调用
                try:
                    result = await self._run_streamlit_compatible_analysis(url, task['item_name'])
                except Exception as scraper_error:
                    logger.warning(f"Streamlit兼容抓取器失败: {scraper_error}")
                    # 回退到原始分析系统
                    result = await self.regular_system.run_complete_analysis(url, task['item_name'])

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            if result['success']:
                logger.info(f"✅ [{current}/{total}] 分析成功: {task['item_name']} (耗时: {duration:.1f}秒)")

                # 更新状态为成功
                analysis_log_dao.update_analysis_status(
                    task['id'],
                    'success',
                    end_time=end_time,
                    result_path=result.get('report_path', '')
                )
                return True
            else:
                error_msg = result.get('error', '未知错误')
                logger.error(f"❌ [{current}/{total}] 分析失败: {task['item_name']} - {error_msg}")

                # 更新状态为失败
                analysis_log_dao.update_analysis_status(
                    task['id'],
                    'failed',
                    end_time=end_time,
                    error_message=error_msg
                )
                return False

        except Exception as e:
            end_time = datetime.now()
            error_msg = str(e)
            logger.error(f"❌ [{current}/{total}] 分析异常: {task['item_name']} - {error_msg}")

            # 更新状态为失败
            analysis_log_dao.update_analysis_status(
                task['id'],
                'failed',
                end_time=end_time,
                error_message=error_msg
            )
            return False
    
    async def _realtime_monitor_loop(self):
        """实时监控循环任务"""
        logger.info("📈 启动实时监控定时任务")
        
        while self.is_running:
            try:
                # 获取需要监控的饰品（收藏+持仓，去重合并）
                monitor_items = self._get_items_for_monitor()

                if not monitor_items:
                    logger.warning("没有找到需要监控的饰品（收藏或持仓）")
                    await asyncio.sleep(3600)  # 1小时后重试
                    continue

                logger.info(f"📋 开始实时监控，共 {len(monitor_items)} 个饰品")
                logger.info(f"📊 监控来源统计: {self._get_monitor_source_stats(monitor_items)}")

                # 逐个监控饰品（顺序执行，避免IP封禁）
                for i, item in enumerate(monitor_items, 1):
                    if not self.is_running:
                        break

                    await self._run_realtime_monitor(item, i, len(monitor_items))
                    
                    # 饰品之间的随机间隔
                    if i < len(monitor_items):
                        delay = random.randint(*self.item_delay_seconds)
                        logger.info(f"⏳ 等待 {delay} 秒后监控下一个饰品...")
                        await asyncio.sleep(delay)
                
                logger.info("✅ 本轮实时监控完成")
                
                # 等待下一轮监控
                next_run = datetime.now() + timedelta(hours=self.realtime_interval_hours)
                logger.info(f"⏰ 下次实时监控时间: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
                await asyncio.sleep(self.realtime_interval_hours * 3600)
                
            except asyncio.CancelledError:
                logger.info("实时监控任务被取消")
                break
            except Exception as e:
                logger.error(f"实时监控循环出错: {e}")
                await asyncio.sleep(300)  # 5分钟后重试
    
    def _get_all_items_as_dict(self) -> List[Dict[str, Any]]:
        """获取所有饰品数据（字典格式）"""
        try:
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.item import Item

            with get_db_session() as session:
                items = session.query(Item).all()

                result = []
                for item in items:
                    try:
                        result.append({
                            'item_id': item.item_id,
                            'name': getattr(item, 'name', item.item_id),
                            'item_type': getattr(item, 'item_type', None),
                            'quality': getattr(item, 'quality', None),
                            'rarity': getattr(item, 'rarity', None),
                            'exterior': getattr(item, 'exterior', None),
                            'image_url': getattr(item, 'image_url', None),
                            'market_hash_name': getattr(item, 'market_hash_name', None),
                            'created_at': getattr(item, 'created_at', None),
                            'updated_at': getattr(item, 'updated_at', None)
                        })
                    except Exception as e:
                        logger.warning(f"跳过无法处理的饰品: {e}")
                        continue

                return result

        except Exception as e:
            logger.error(f"获取饰品数据失败: {e}")
            return []

    def _get_favorites_for_monitor(self) -> List[Dict[str, Any]]:
        """获取需要实时监控的收藏饰品"""
        try:
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.favorite import Favorite

            with get_db_session() as session:
                favorites = session.query(Favorite).all()

                result = []
                for favorite in favorites:
                    try:
                        fav_id = favorite.id
                        item_id = favorite.item_id
                        item_name = getattr(favorite, 'item_name', None)
                        user_id = getattr(favorite, 'user_id', None)

                        url = f"https://steamdt.com/cs2/{item_id}"

                        result.append({
                            'id': f"favorite_{fav_id}",  # 添加前缀避免与持仓ID冲突
                            'item_id': item_id,
                            'name': item_name or item_id,
                            'url': url,
                            'user_id': user_id,
                            'source': 'favorite',
                            'priority': 2  # 收藏饰品优先级较低
                        })
                    except Exception as e:
                        logger.warning(f"跳过无法处理的收藏饰品: {e}")
                        continue

                return result

        except Exception as e:
            logger.error(f"获取收藏饰品失败: {e}")
            return []

    def _get_holdings_for_monitor(self) -> List[Dict[str, Any]]:
        """获取需要实时监控的持仓饰品"""
        try:
            from src.cs2_investment.dao.holding_dao import HoldingDAO
            from src.cs2_investment.dao.item_dao import ItemDAO

            holding_dao = HoldingDAO()
            item_dao = ItemDAO()

            # 获取默认用户的所有持仓
            default_user_id = "default_user"
            holdings = holding_dao.get_user_holdings(default_user_id, limit=1000)

            if not holdings:
                logger.info("没有找到持仓饰品")
                return []

            logger.info(f"找到 {len(holdings)} 个持仓饰品，开始获取market_hash_name...")

            monitor_list = []
            for holding in holdings:
                try:
                    item_id = holding.get('item_id', '')
                    item_name = holding.get('item_name', '')

                    if not item_id:
                        logger.warning(f"持仓饰品缺少item_id: {holding}")
                        continue

                    # 从items表获取market_hash_name
                    item = item_dao.get_by_item_id(item_id)
                    if not item:
                        logger.warning(f"未找到饰品信息: item_id={item_id}, name={item_name}")
                        continue

                    # item是字典格式
                    market_hash_name = item.get('market_hash_name')
                    if not market_hash_name:
                        logger.warning(f"饰品缺少market_hash_name: item_id={item_id}, name={item_name}")
                        continue

                    # 构造正确的SteamDT URL，使用URL编码的market_hash_name
                    import urllib.parse
                    encoded_name = urllib.parse.quote(market_hash_name, safe='')
                    url = f"https://steamdt.com/cs2/{encoded_name}"

                    monitor_list.append({
                        'id': f"holding_{holding['id']}",  # 添加前缀避免与收藏ID冲突
                        'item_id': item_id,
                        'name': item_name or item.get('name', '未知饰品'),
                        'url': url,
                        'market_hash_name': market_hash_name,
                        'user_id': default_user_id,
                        'source': 'holding',
                        'quantity': holding.get('total_quantity', 0),
                        'total_cost': holding.get('total_cost', 0),
                        'priority': 1  # 持仓饰品优先级更高
                    })

                    logger.debug(f"成功处理持仓饰品: {item_name} -> {url}")

                except Exception as item_error:
                    logger.warning(f"处理持仓饰品失败: {item_error}")
                    continue

            logger.info(f"成功获取 {len(monitor_list)} 个可监控的持仓饰品")
            return monitor_list

        except Exception as e:
            logger.error(f"获取持仓饰品失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return []

    def _get_items_for_monitor(self) -> List[Dict[str, Any]]:
        """获取需要监控的所有饰品（收藏+持仓，去重合并）"""
        try:
            # 获取收藏饰品
            favorites = self._get_favorites_for_monitor()

            # 获取持仓饰品
            holdings = self._get_holdings_for_monitor()

            # 合并列表，使用item_id去重
            seen_items = set()
            monitor_items = []

            # 先添加持仓饰品（优先级更高）
            for holding in holdings:
                item_id = holding.get('item_id')
                if item_id and item_id not in seen_items:
                    seen_items.add(item_id)
                    holding['priority'] = 1  # 持仓优先级
                    monitor_items.append(holding)

            # 再添加收藏饰品（去重）
            for favorite in favorites:
                item_id = favorite.get('item_id')
                if item_id and item_id not in seen_items:
                    seen_items.add(item_id)
                    favorite['priority'] = 2  # 收藏优先级
                    monitor_items.append(favorite)

            # 按优先级排序（持仓优先）
            monitor_items.sort(key=lambda x: x.get('priority', 999))

            logger.info(f"合并监控列表完成: 持仓 {len(holdings)} 个, 收藏 {len(favorites)} 个, 去重后 {len(monitor_items)} 个")

            return monitor_items

        except Exception as e:
            logger.error(f"合并监控列表失败: {e}")
            return []

    def _get_monitor_source_stats(self, monitor_items: List[Dict[str, Any]]) -> str:
        """获取监控来源统计信息"""
        try:
            holding_count = sum(1 for item in monitor_items if item.get('source') == 'holding')
            favorite_count = sum(1 for item in monitor_items if item.get('source') == 'favorite')

            return f"持仓 {holding_count} 个, 收藏 {favorite_count} 个"
        except Exception:
            return "统计失败"
    
    async def _run_realtime_monitor(self, favorite: Dict[str, Any], current: int, total: int):
        """运行单个饰品的实时监控"""
        start_time = datetime.now()
        
        try:
            logger.info(f"📈 [{current}/{total}] 开始实时监控: {favorite['name']}")
            
            result = await self.realtime_system.run_complete_analysis(favorite['url'], favorite['name'])
            
            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()
            
            if result['success']:
                logger.info(f"✅ [{current}/{total}] 实时监控成功: {favorite['name']} (耗时: {duration:.1f}秒)")
            else:
                error_msg = result.get('error', '未知错误')
                logger.error(f"❌ [{current}/{total}] 实时监控失败: {favorite['name']} - {error_msg}")
        
        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ [{current}/{total}] 实时监控异常: {favorite['name']} - {error_msg}")
    
    def get_status(self) -> dict:
        """获取调度器状态"""
        today = date.today()
        progress = analysis_log_dao.get_analysis_progress('regular', today)
        daily_schedule = daily_schedule_dao.get_daily_schedule(today)
        
        return {
            'is_running': self.is_running,
            'regular_analysis_running': self.regular_analysis_task and not self.regular_analysis_task.done(),
            'realtime_monitor_running': self.realtime_monitor_task and not self.realtime_monitor_task.done(),
            'daily_start_time': self.daily_start_time.strftime('%H:%M'),
            'realtime_interval_hours': self.realtime_interval_hours,
            'item_delay_seconds': self.item_delay_seconds,
            'today_progress': progress,
            'daily_schedule': daily_schedule
        }

    async def _run_api_analysis(self, item_id: str, item_name: str, item_url: str, task_id: int) -> Dict[str, Any]:
        """通过API运行分析"""
        try:
            # 提交分析任务到API
            result = self.api_client.start_regular_analysis(item_id, item_name, item_url)

            if not result:
                return {
                    'success': False,
                    'error': 'API调用失败'
                }

            api_task_id = result.get('task_id')
            if not api_task_id:
                return {
                    'success': False,
                    'error': 'API返回无效的任务ID'
                }

            logger.info(f"📊 API任务已提交: {api_task_id} - {item_name}")

            # 等待任务完成
            final_status = self.api_client.wait_for_task_completion(
                api_task_id,
                max_wait_time=600,  # 最多等待10分钟
                poll_interval=10    # 每10秒检查一次
            )

            if not final_status:
                return {
                    'success': False,
                    'error': '任务执行超时或状态获取失败'
                }

            status = final_status.get('status')
            if status == 'success':
                # 获取任务结果
                task_result = self.api_client.get_task_result(api_task_id)
                result_path = final_status.get('result_path', '')

                return {
                    'success': True,
                    'report_path': result_path,
                    'api_task_id': api_task_id,
                    'duration': final_status.get('duration_seconds', 0)
                }
            else:
                error_message = final_status.get('error_message', f'任务失败: {status}')
                return {
                    'success': False,
                    'error': error_message,
                    'api_task_id': api_task_id
                }

        except Exception as e:
            logger.error(f"API分析执行异常: {e}")
            return {
                'success': False,
                'error': f'API分析异常: {str(e)}'
            }

    def _enrich_tasks_with_item_info(self, tasks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为任务补充item信息，特别是market_hash_name"""
        try:
            from ..config.database import get_db_session
            from ..models.item import Item

            enriched_tasks = []

            with get_db_session() as session:
                for task in tasks:
                    try:
                        # 查询对应的item信息
                        item = session.query(Item).filter(Item.item_id == task['item_id']).first()

                        if item:
                            # 复制原任务数据并添加item信息
                            enriched_task = task.copy()
                            enriched_task['market_hash_name'] = getattr(item, 'market_hash_name', None)
                            enriched_task['item_type'] = getattr(item, 'item_type', None)
                            enriched_task['quality'] = getattr(item, 'quality', None)
                            enriched_task['rarity'] = getattr(item, 'rarity', None)
                            enriched_tasks.append(enriched_task)
                        else:
                            logger.warning(f"未找到饰品信息: {task['item_id']}")
                            # 仍然添加原任务，但没有额外信息
                            enriched_tasks.append(task)

                    except Exception as e:
                        logger.warning(f"处理任务 {task.get('item_id', 'unknown')} 时出错: {e}")
                        # 添加原任务
                        enriched_tasks.append(task)

            logger.info(f"任务信息补充完成: {len(enriched_tasks)}/{len(tasks)} 个任务")
            return enriched_tasks

        except Exception as e:
            logger.error(f"补充任务信息失败: {e}")
            return tasks  # 返回原任务列表

    async def _update_task_status(self, task_id: int, status: str, error_message: str = None):
        """更新任务状态"""
        try:
            if status == 'failed':
                analysis_log_dao.update_analysis_log(
                    log_id=task_id,
                    status='failed',
                    success=False,
                    error_message=error_message
                )
            elif status == 'completed':
                analysis_log_dao.update_analysis_log(
                    log_id=task_id,
                    status='completed',
                    success=True
                )
            elif status == 'running':
                analysis_log_dao.update_analysis_log(
                    log_id=task_id,
                    status='running'
                )
        except Exception as e:
            logger.error(f"更新任务状态失败: {e}")


# 全局调度器实例
smart_scheduler = SmartTaskScheduler()


async def main():
    """主函数 - 用于测试"""
    # 日志已通过统一组件配置
    
    try:
        await smart_scheduler.start()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在停止...")
        await smart_scheduler.stop()


if __name__ == "__main__":
    asyncio.run(main())
