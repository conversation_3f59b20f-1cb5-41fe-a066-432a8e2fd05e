#!/usr/bin/env python3
"""
SteamDT 数据抓取器 - 最终版本
基于实际页面测试的完整抓取程序

支持的榜单：
1. 价格榜 - 涨跌榜(百分比)
2. 在售数榜 - 在售数变化(百分比)  
3. 成交榜 - 成交量和成交额
4. 饰品热度榜 - 热度榜单和热度上升榜单

时间范围：7天、15天、一个月、三个月（热度榜单除外）
数据量：每个榜单前200条数据
"""

import asyncio
import json
import csv
import time
import random
import re
import pymysql
import sys
import os
from datetime import datetime, date
from pathlib import Path
from playwright.async_api import async_playwright
from typing import List, Dict, Any, Optional

# 添加项目根路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))
from .scraping_manager import ScrapingManager

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)

class SteamDTScraperFinal:
    def __init__(self):
        self.base_url = "https://steamdt.com/ladders"
        self.api_url = "https://sdt-api.ok-skins.com/user/ranking/v1/page"
        # 使用系统默认的数据库配置（已验证可用）
        self.scraping_manager = ScrapingManager()
        self.current_record_id = None  # 当前爬取记录ID
        self.collected_data = []
        self.filters_ready = False  # 筛选条件是否已完全设置完成

        # 滚动控制标志
        self.target_data_count = 0  # 目标数据数量
        self.current_saved_count = 0  # 当前已保存数据数量
        self.scroll_should_stop = False  # 滚动是否应该停止

        # 数据库连接配置 - 使用系统统一配置，但只取pymysql支持的参数
        from src.cs2_investment.config.settings import get_settings
        settings = get_settings()
        self.db_config = {
            'host': settings.database.host,
            'port': settings.database.port,
            'user': settings.database.user,
            'password': settings.database.password,
            'database': settings.database.name,
            'charset': settings.database.charset
        }
        
        # 榜单配置（基于实际页面观察）
        self.rankings = {
            "price_up": {
                "name": "价格榜-上涨榜",
                "tab_name": "价格榜",
                "sub_options": ["涨跌榜(百分比)"],
                "sort_order": "desc",  # 降序 - 上涨榜
                "time_periods": ["近1天","近3天","近7天", "近15天", "近一个月", "近三个月", "近六个月", "近一年"]
            },
            "price_down": {
                "name": "价格榜-下跌榜",
                "tab_name": "价格榜",
                "sub_options": ["涨跌榜(百分比)"],
                "sort_order": "asc",   # 升序 - 下跌榜
                "time_periods": ["近1天","近3天","近7天", "近15天", "近一个月", "近三个月", "近六个月", "近一年"]
            },
            "inventory": {
                "name": "在售数榜", 
                "tab_name": "在售数榜",
                "sub_options": ["在售数变化(百分比)"],
                "time_periods": ["近1天","近3天","近7天", "近15天", "近一个月", "近三个月", "近六个月", "近一年"]
            },
            "transaction": {
                "name": "成交榜",
                "tab_name": "成交榜", 
                "sub_options": ["成交量", "成交额"],
                "time_periods": ["近1天","近3天","近7天", "近15天", "近一个月", "近三个月"]
            },
            "popularity": {
                "name": "饰品热度榜",
                "tab_name": "饰品热度榜",
                "sub_options": ["热度榜单", "热度上升榜单"],
                "time_periods": ["近1天"]  # 热度榜单只有近1天
            }
        }
        
        self.max_items = 600  # 每个榜单最大抓取30条（测试用）
        self.delay_range = (5, 10)  # 滚动间隔5-10秒（测试时缩短）

        # 排名计数器
        self.current_ranking_position = 0  # 当前排行榜的排名计数器
        
    async def create_browser(self):
        """创建浏览器实例"""
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(headless=False)  # 设置为True可无头运行
        context = await browser.new_context(
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        )
        page = await context.new_page()
        return playwright, browser, page
        
    async def close_popup_if_exists(self, page):
        """关闭可能存在的弹窗"""
        try:
            # 等待并关闭新功能发布弹窗
            close_button = await page.query_selector('button[aria-label="关闭此对话框"]')
            if close_button:
                await close_button.click()
                await asyncio.sleep(2)
                logger.info("✅ 已关闭弹窗")
                return True
        except Exception as e:
            logger.debug(f"关闭弹窗时出现异常（可能不存在弹窗）: {e}")
        return False
        
    def setup_api_monitoring(self, page):
        """设置API监控"""
        def handle_response(response):
            if self.api_url in response.url:
                logger.info(f"🌐 捕获API响应: {response.status} {response.url}")
                # 异步处理响应数据
                asyncio.create_task(self.process_api_response(response))

        page.on('response', handle_response)

    async def process_api_response(self, response):
        """处理API响应数据 - 边收集边保存"""
        try:
            if response.status == 200:
                data = await response.json()

                # 提取数据列表
                if isinstance(data, dict) and 'data' in data:
                    data_field = data['data']
                    if isinstance(data_field, dict) and 'list' in data_field:
                        items = data_field['list']
                        if isinstance(items, list) and items:
                            # 检查筛选条件是否已完全设置完成
                            if hasattr(self, 'current_ranking_info') and self.filters_ready:
                                # 筛选条件已完成，收集并保存正式数据
                                self.collected_data.extend(items)
                                logger.info(f"✅ 已收集{len(items)}条筛选后API数据，总计{len(self.collected_data)}条")

                                # 实时保存数据
                                saved_count = self._save_batch_to_database(items)
                                if saved_count > 0:
                                    if hasattr(self, 'total_saved_count'):
                                        self.total_saved_count += saved_count
                                    # 更新当前已保存数量
                                    self.current_saved_count = getattr(self, 'total_saved_count', saved_count)
                                    logger.info(f"💾 实时保存{saved_count}条数据到数据库，累计保存{self.current_saved_count}条")

                                    # 检查是否达到目标数量，通知滚动程序停止
                                    if self.current_saved_count >= self.target_data_count:
                                        self.scroll_should_stop = True
                                        logger.info(f"🎯 已达到目标数量{self.target_data_count}条，通知滚动程序停止")

                            elif hasattr(self, 'current_ranking_info') and not self.filters_ready:
                                # 筛选条件未完成，记录但不保存非正式数据
                                logger.debug(f"⏳ 检测到{len(items)}条未筛选API数据，等待筛选条件完成后再保存")
                            else:
                                # 没有当前榜单信息，忽略
                                logger.debug(f"🔍 检测到{len(items)}条API数据，但无当前榜单信息")

        except Exception as e:
            logger.error(f"❌ 处理API响应失败: {e}")

    def flatten_api_data(self, raw_data):
        """扁平化API数据结构"""
        flattened_data = []

        for item in raw_data:
            if not isinstance(item, dict):
                continue

            try:
                # 提取基本信息
                flattened_item = {
                    'surviveNum': item.get('surviveNum', ''),
                    'updateTime': item.get('updateTime', ''),
                }

                # 提取饰品信息
                item_info = item.get('itemInfoVO', {})
                flattened_item.update({
                    'itemName': item_info.get('name', ''),
                    'itemId': item_info.get('itemId', ''),
                    'gameId': item_info.get('gameId', ''),
                    'exterior': item_info.get('exterior', ''),
                    'rarity': item_info.get('rarity', ''),
                    'category': item_info.get('category', ''),
                    'imageUrl': item_info.get('imageUrl', ''),
                })

                # 提取价格信息
                price_info = item.get('sellPriceInfoVO', {})
                flattened_item.update({
                    'currentPrice': price_info.get('price', 0),
                    'diff1Days': price_info.get('diff1Days', 0),
                    'diff3Days': price_info.get('diff3Days', 0),
                    'diff7Days': price_info.get('diff7Days', 0),
                    'diff15Days': price_info.get('diff15Days', 0),
                    'diff1Months': price_info.get('diff1Months', 0),
                    'diff3Months': price_info.get('diff3Months', 0),
                    'before7DaysPrice': price_info.get('before7DaysPrice', 0),
                    'priceChangeAmount': price_info.get('priceChangeAmount', 0),  # 价格变化金额
                    'priceChangePercent': price_info.get('priceChangePercent', 0),  # 价格变化百分比
                    'minPrice': price_info.get('minPrice', 0),  # 最低价格
                    'maxPrice': price_info.get('maxPrice', 0),  # 最高价格
                    'avgPrice': price_info.get('avgPrice', 0),  # 平均价格
                })

                # 提取在售数信息
                sell_nums_info = item.get('sellNumsInfoVO', {})
                flattened_item.update({
                    'sellNums': sell_nums_info.get('sellNums', 0),
                    'sellNums7DaysRate': sell_nums_info.get('sellNums7DaysRate', 0),
                    'sellNums1DaysRate': sell_nums_info.get('sellNums1DaysRate', 0),  # 1天在售数变化
                    'sellNums3DaysRate': sell_nums_info.get('sellNums3DaysRate', 0),  # 3天在售数变化
                    'sellNums15DaysRate': sell_nums_info.get('sellNums15DaysRate', 0),  # 15天在售数变化
                    'sellNums1MonthsRate': sell_nums_info.get('sellNums1MonthsRate', 0),  # 1月在售数变化
                })

                # 提取成交信息
                transaction_info = item.get('transactionCountInfoVO', {})
                flattened_item.update({
                    'transactionCount7Days': transaction_info.get('transactionCount7Days', ''),
                    'transactionCountRate48Hours': transaction_info.get('transactionCountRate48Hours', 0),
                    'transactionCount1Days': transaction_info.get('transactionCount1Days', ''),  # 1天成交量
                    'transactionCount3Days': transaction_info.get('transactionCount3Days', ''),  # 3天成交量
                    'transactionCount15Days': transaction_info.get('transactionCount15Days', ''),  # 15天成交量
                    'transactionCount1Months': transaction_info.get('transactionCount1Months', ''),  # 1月成交量
                    'transactionAmount7Days': transaction_info.get('transactionAmount7Days', 0),  # 7天成交额
                    'transactionAmountRate': transaction_info.get('transactionAmountRate', 0),  # 成交额变化率
                })

                # 提取热度信息
                hot_info = item.get('hotVO', {})
                flattened_item.update({
                    'hotCount': hot_info.get('hotCount', 0),
                    'hotRank': hot_info.get('hotRank', 0),
                })

                # 提取更新频率
                update_freq = item.get('updateFrequency', {})
                flattened_item.update({
                    'updateFrequencyType': update_freq.get('type', ''),
                    'updateFrequencyKey': update_freq.get('key', ''),
                })

                # 提取其他可能的字段
                flattened_item.update({
                    'ranking': item.get('ranking', ''),  # 排名
                    'rankingChange': item.get('rankingChange', ''),  # 排名变化
                    'marketCap': item.get('marketCap', 0),  # 市值
                    'volume24h': item.get('volume24h', 0),  # 24小时成交量
                    'priceUSD': item.get('priceUSD', 0),  # 美元价格
                    'lastUpdateTime': item.get('lastUpdateTime', ''),  # 最后更新时间
                    'dataSource': item.get('dataSource', ''),  # 数据来源
                    'quality': item.get('quality', ''),  # 品质
                    'wear': item.get('wear', ''),  # 磨损值
                    'float': item.get('float', ''),  # 浮动值
                    'stickers': item.get('stickers', ''),  # 贴纸信息
                    'tags': item.get('tags', ''),  # 标签
                })

                flattened_data.append(flattened_item)

            except Exception as e:
                logger.warning(f"扁平化数据项失败: {e}")
                continue

        return flattened_data
            
    async def scroll_and_load_data(self, page, target_count=200):
        """滚动页面加载数据 - 通过API监听控制结束"""
        logger.info(f"🔄 开始滚动加载数据，目标数量: {target_count}")

        # 初始化滚动控制变量
        self.target_data_count = target_count
        self.current_saved_count = 0
        self.scroll_should_stop = False
        self.collected_data = []

        scroll_count = 0
        max_scrolls = 50  # 最大滚动次数保护
        no_new_data_count = 0  # 连续无新数据次数

        logger.info(f"📋 滚动参数: 目标{target_count}条，最大滚动{max_scrolls}次")
        logger.info(f"🎯 滚动将通过API监听自动停止，当保存数据达到{target_count}条时")

        while not self.scroll_should_stop and scroll_count < max_scrolls:
            # 检查是否应该停止滚动
            if self.scroll_should_stop:
                logger.info(f"🛑 收到停止信号，已保存{self.current_saved_count}条数据，停止滚动")
                break

            current_count = len(self.collected_data)

            # 随机滚动方式（增加不规律性）
            scroll_type = random.choice(["full", "partial", "smooth"])
            if scroll_type == "full":
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            elif scroll_type == "partial":
                # 随机滚动距离
                scroll_distance = random.randint(800, 1500)
                await page.evaluate(f"window.scrollBy(0, {scroll_distance})")
            else:  # smooth
                await page.evaluate("window.scrollTo({top: document.body.scrollHeight, behavior: 'smooth'})")

            # 10%概率向上滚动一点（模拟用户回看）
            if random.random() < 0.1:
                back_scroll = random.randint(200, 500)
                await page.evaluate(f"window.scrollBy(0, -{back_scroll})")
                await asyncio.sleep(random.uniform(2, 5))

            # 等待数据加载
            base_wait = random.uniform(3, 8)  # 缩短等待时间，因为有API监听控制
            await asyncio.sleep(base_wait)

            new_count = len(self.collected_data)

            if new_count > current_count:
                no_new_data_count = 0
                logger.info(f"📊 第{scroll_count + 1}次滚动，API数据从{current_count}增加到{new_count}，已保存{self.current_saved_count}条")
            else:
                no_new_data_count += 1
                logger.info(f"⚠️ 第{scroll_count + 1}次滚动，API数据未增加 ({no_new_data_count}/5)，已保存{self.current_saved_count}条")

            scroll_count += 1

            # 检查连续无新数据的情况
            if no_new_data_count >= 5:
                logger.info(f"🛑 连续{no_new_data_count}次无新数据，可能已到榜单末尾，结束滚动")
                break

            # 再次检查是否应该停止（在等待前检查）
            if self.scroll_should_stop:
                logger.info(f"🛑 收到停止信号，已保存{self.current_saved_count}条数据，停止滚动")
                break

            # 滚动间隔等待
            delay = random.uniform(*self.delay_range)

            # 降低额外停顿概率，因为有API监听控制
            if random.random() < 0.1:
                extra_delay = random.uniform(10, 30)  # 10-30秒额外停顿
                logger.info(f"😴 触发额外停顿: {extra_delay:.1f}秒")
                await asyncio.sleep(extra_delay)

            logger.info(f"⏳ 等待{delay:.1f}秒后继续...")
            await asyncio.sleep(delay)

        # 滚动结束，显示统计信息
        actual_count = len(self.collected_data)
        saved_count = self.current_saved_count

        if self.scroll_should_stop:
            logger.info(f"✅ 滚动完成（API监听控制）：收集{actual_count}条API数据，保存{saved_count}条到数据库")
        else:
            logger.info(f"✅ 滚动完成（达到限制）：收集{actual_count}条API数据，保存{saved_count}条到数据库")

        # 返回收集的数据（用于统计）
        return self.collected_data
        
    async def select_tab(self, page, tab_name):
        """选择榜单标签"""
        try:
            # 等待标签加载
            await page.wait_for_selector('[role="tab"]', timeout=15000)
            
            # 点击指定标签
            await page.click(f'[role="tab"]:has-text("{tab_name}")')
            await asyncio.sleep(3)
            logger.info(f"✅ 已选择标签: {tab_name}")
            return True
        except Exception as e:
            logger.error(f"❌ 选择标签失败 {tab_name}: {e}")
            # 尝试备用选择器
            try:
                await page.click(f'text="{tab_name}"')
                await asyncio.sleep(3)
                logger.info(f"✅ 使用备用选择器成功选择标签: {tab_name}")
                return True
            except Exception as e2:
                logger.error(f"❌ 备用选择器也失败: {e2}")
                return False
            
    async def select_sub_option(self, page, option_name):
        """选择子选项（如涨跌榜百分比）"""
        try:
            await page.click(f'text="{option_name}"')
            await asyncio.sleep(2)
            logger.info(f"✅ 已选择子选项: {option_name}")
            return True
        except Exception as e:
            logger.error(f"❌ 选择子选项失败 {option_name}: {e}")
            return False

    async def select_sort_order(self, page, sort_order):
        """选择排序方式 - 使用更稳定的选择器"""
        try:
            # 使用更稳定的文本选择器
            if sort_order == "desc":
                # 需要降序（上涨榜）
                current_sort = await page.query_selector('text="升序"')
                if current_sort:
                    logger.info("🔄 当前是升序，切换到降序排序（上涨榜）")
                    await current_sort.click()
                    await asyncio.sleep(random.uniform(3, 8))
                    logger.info("✅ 已切换到降序排序（上涨榜）")
                else:
                    logger.info("✅ 当前已是降序排序（上涨榜）")

            elif sort_order == "asc":
                # 需要升序（下跌榜）
                current_sort = await page.query_selector('text="降序"')
                if current_sort:
                    logger.info("🔄 当前是降序，切换到升序排序（下跌榜）")
                    await current_sort.click()
                    await asyncio.sleep(random.uniform(3, 8))
                    logger.info("✅ 已切换到升序排序（下跌榜）")
                else:
                    logger.info("✅ 当前已是升序排序（下跌榜）")

            return True

        except Exception as e:
            logger.error(f"❌ 选择排序方式失败 {sort_order}: {e}")
            return True  # 继续执行，排序失败不影响数据抓取

    async def select_time_period(self, page, time_period):
        """选择时间周期"""
        try:
            # 查找并点击时间选择下拉框
            time_selector = page.locator('div').filter(has_text=re.compile(r'^近\d+天$|^近\d+个月$|^近一个月$|^近三个月$|^近六个月$|^近一年$')).nth(1)
            await time_selector.click()
            await asyncio.sleep(2)

            # 选择指定时间周期
            await page.get_by_role('option', name=time_period).click()
            await asyncio.sleep(3)
            logger.info(f"✅ 已选择时间周期: {time_period}")
            return True
        except Exception as e:
            logger.error(f"❌ 选择时间周期失败 {time_period}: {e}")
            # 尝试备用选择器
            try:
                # 使用更通用的选择器
                await page.click('[role="combobox"]')
                await asyncio.sleep(2)
                await page.click(f'[role="option"]:has-text("{time_period}")')
                await asyncio.sleep(3)
                logger.info(f"✅ 使用备用选择器成功选择时间周期: {time_period}")
                return True
            except Exception as e2:
                logger.error(f"❌ 备用时间选择器也失败: {e2}")
                return False
            
    def save_to_database(self, data, ranking_key, sub_option, time_period):
        """保存数据到数据库"""
        if not data:
            logger.warning(f"⚠️ 没有数据可保存")
            return {"success_count": 0, "error_count": 0}

        # 扁平化数据
        flattened_data = self.flatten_api_data(data)

        if not flattened_data:
            logger.warning(f"⚠️ 扁平化后没有有效数据")
            return {"success_count": 0, "error_count": 0}

        try:
            connection = pymysql.connect(**self.db_config)
            success_count = 0
            error_count = 0
            snapshot_time = datetime.now()
            data_source = f"{ranking_key}_{sub_option}_{time_period}"

            with connection.cursor() as cursor:
                for item in flattened_data:
                    try:
                        # 转换为数据库记录格式
                        item_record, snapshot_record = self._convert_to_db_format(item, data_source, snapshot_time)

                        if not item_record or not item_record.get("item_id"):
                            error_count += 1
                            continue

                        # 保存饰品信息
                        cursor.execute("""
                            INSERT INTO items (item_id, item_type, name, market_hash_name,
                                             quality, rarity, exterior, image_url)
                            VALUES (%(item_id)s, %(item_type)s, %(name)s, %(market_hash_name)s,
                                    %(quality)s, %(rarity)s, %(exterior)s, %(image_url)s)
                            ON DUPLICATE KEY UPDATE
                            item_type = VALUES(item_type),
                            name = VALUES(name),
                            market_hash_name = VALUES(market_hash_name),
                            quality = VALUES(quality),
                            rarity = VALUES(rarity),
                            exterior = VALUES(exterior),
                            image_url = VALUES(image_url),
                            updated_at = CURRENT_TIMESTAMP
                        """, item_record)

                        # 保存市场快照 - 按饰品ID和日期为主键，只有更新时间更新才保存
                        if snapshot_record:
                            # 检查是否需要更新（基于更新时间）
                            should_update = self._should_update_snapshot(cursor, snapshot_record, item)

                            if should_update:
                                # 使用ON DUPLICATE KEY UPDATE实现智能更新，主键为(item_id, snapshot_date)
                                cursor.execute("""
                                    INSERT INTO market_snapshots (
                                        item_id, snapshot_date, snapshot_time, data_source, current_price,
                                        diff_1d, diff_3d, diff_7d, diff_15d, diff_1m, diff_3m,
                                        trans_count_1d, trans_count_3d, trans_count_7d, trans_count_15d, trans_count_1m,
                                        trans_amount_7d, hot_rank, hot_count, sell_nums,
                                        sell_nums_1d_rate, sell_nums_7d_rate, sell_nums_1m_rate, update_time
                                    ) VALUES (
                                        %(item_id)s, %(snapshot_date)s, %(snapshot_time)s, %(data_source)s, %(current_price)s,
                                        %(diff_1d)s, %(diff_3d)s, %(diff_7d)s, %(diff_15d)s, %(diff_1m)s, %(diff_3m)s,
                                        %(trans_count_1d)s, %(trans_count_3d)s, %(trans_count_7d)s, %(trans_count_15d)s, %(trans_count_1m)s,
                                        %(trans_amount_7d)s, %(hot_rank)s, %(hot_count)s, %(sell_nums)s,
                                        %(sell_nums_1d_rate)s, %(sell_nums_7d_rate)s, %(sell_nums_1m_rate)s, %(update_time)s
                                    ) ON DUPLICATE KEY UPDATE
                                        snapshot_time = VALUES(snapshot_time),
                                        data_source = VALUES(data_source),
                                        current_price = VALUES(current_price),
                                        diff_1d = VALUES(diff_1d),
                                        diff_3d = VALUES(diff_3d),
                                        diff_7d = VALUES(diff_7d),
                                        diff_15d = VALUES(diff_15d),
                                        diff_1m = VALUES(diff_1m),
                                        diff_3m = VALUES(diff_3m),
                                        trans_count_1d = VALUES(trans_count_1d),
                                        trans_count_3d = VALUES(trans_count_3d),
                                        trans_count_7d = VALUES(trans_count_7d),
                                        trans_count_15d = VALUES(trans_count_15d),
                                        trans_count_1m = VALUES(trans_count_1m),
                                        trans_amount_7d = VALUES(trans_amount_7d),
                                        hot_rank = VALUES(hot_rank),
                                        hot_count = VALUES(hot_count),
                                        sell_nums = VALUES(sell_nums),
                                        sell_nums_1d_rate = VALUES(sell_nums_1d_rate),
                                        sell_nums_7d_rate = VALUES(sell_nums_7d_rate),
                                        sell_nums_1m_rate = VALUES(sell_nums_1m_rate),
                                        update_time = VALUES(update_time)
                                """, snapshot_record)
                            else:
                                logger.debug(f"跳过更新 {snapshot_record['item_id']} - 数据未更新")

                        success_count += 1

                    except Exception as e:
                        logger.error(f"❌ 保存单条数据失败: {e}")
                        error_count += 1
                        continue

                connection.commit()

            connection.close()

            logger.info(f"✅ 数据已保存到数据库: 成功{success_count}条, 失败{error_count}条")

            # 显示前3条数据示例
            logger.info("📊 数据示例（前3条）:")
            for i, item in enumerate(flattened_data[:3], 1):
                name = item.get('itemName', 'N/A')
                price = item.get('currentPrice', 'N/A')
                change = item.get('diff7Days', 'N/A')
                logger.info(f"  {i}. {name} - ¥{price} - {change}%")

            return {"success_count": success_count, "error_count": error_count}

        except Exception as e:
            logger.error(f"❌ 保存数据到数据库失败: {e}")
            return {"success_count": 0, "error_count": len(flattened_data)}

    def _save_batch_to_database(self, batch_data):
        """批量保存数据到数据库（实时保存）"""
        if not batch_data or not hasattr(self, 'current_ranking_info'):
            return 0

        try:
            # 直接使用原始JSON数据，不需要扁平化
            if not batch_data:
                return 0

            connection = pymysql.connect(**self.db_config)
            success_count = 0
            snapshot_time = datetime.now()

            # 获取当前榜单信息
            ranking_info = self.current_ranking_info
            data_source = f"{ranking_info['ranking_key']}_{ranking_info['sub_option']}_{ranking_info['time_period']}"

            # 新增：为批次中的每个饰品设置全局排名位置
            self.current_batch_items = batch_data  # 保存当前批次数据供排名计算使用
            batch_start_position = self.current_ranking_position

            for item in batch_data:
                self.current_ranking_position += 1
                item['_ranking_position'] = self.current_ranking_position

            # 添加调试日志
            logger.info(f"🏆 为 {len(batch_data)} 个饰品设置排名信息，排名范围: {batch_start_position + 1}-{self.current_ranking_position}，数据源: {data_source}")

            with connection.cursor() as cursor:
                for item in batch_data:
                    try:
                        # 转换为数据库记录格式
                        item_record, snapshot_record = self._convert_to_db_format(item, data_source, snapshot_time)

                        if not item_record or not item_record.get("item_id"):
                            continue

                        # 保存饰品信息
                        cursor.execute("""
                            INSERT INTO items (item_id, item_type, def_index_name, name, market_hash_name,
                                             quality, rarity, exterior, image_url, update_level)
                            VALUES (%(item_id)s, %(item_type)s, %(def_index_name)s, %(name)s, %(market_hash_name)s,
                                    %(quality)s, %(rarity)s, %(exterior)s, %(image_url)s, %(update_level)s)
                            ON DUPLICATE KEY UPDATE
                            item_type = VALUES(item_type),
                            def_index_name = VALUES(def_index_name),
                            name = VALUES(name),
                            market_hash_name = VALUES(market_hash_name),
                            quality = VALUES(quality),
                            rarity = VALUES(rarity),
                            exterior = VALUES(exterior),
                            image_url = VALUES(image_url),
                            update_level = VALUES(update_level),
                            updated_at = CURRENT_TIMESTAMP
                        """, item_record)

                        # 保存市场快照 - 检查更新策略
                        if snapshot_record:
                            update_strategy = self._should_update_snapshot(cursor, snapshot_record, item)
                            current_ranking_key = snapshot_record.get("_current_ranking_key")

                            if update_strategy in ["full_update", "full_update_with_merge"]:
                                # 处理排名信息合并
                                if update_strategy == "full_update_with_merge":
                                    # 合并排名信息
                                    merged_ranking_info = self._merge_ranking_info(
                                        cursor,
                                        snapshot_record["item_id"],
                                        snapshot_record["snapshot_date"],
                                        snapshot_record["ranking_info"],
                                        current_ranking_key
                                    )
                                    snapshot_record["ranking_info"] = merged_ranking_info
                                    logger.info(f"🔄 全量更新并合并排名: {snapshot_record['item_id']} - {current_ranking_key}")
                                else:
                                    logger.info(f"🆕 全量更新: {snapshot_record['item_id']} - {current_ranking_key}")

                                # 全量更新（新记录或API数据更新）
                                cursor.execute("""
                                    INSERT INTO market_snapshots (
                                        item_id, snapshot_date, snapshot_time, data_source, current_price,
                                        diff_1d, diff_3d, diff_7d, diff_15d, diff_1m, diff_3m, diff_6m, diff_1y,
                                        trans_count_1d, trans_count_3d, trans_count_7d, trans_count_15d, trans_count_1m, trans_count_3m,
                                        trans_amount_1d, trans_amount_3d, trans_amount_7d, trans_amount_15d, trans_amount_1m, trans_amount_3m,
                                        hot_rank, hot_count, hot_keep_days, hot_rank_change,
                                        sell_nums, sell_nums_1d, sell_nums_3d, sell_nums_7d, sell_nums_15d, sell_nums_1m, sell_nums_3m, sell_nums_6m, sell_nums_1y,
                                        sell_nums_1d_rate, sell_nums_3d_rate, sell_nums_7d_rate, sell_nums_15d_rate, sell_nums_1m_rate, sell_nums_3m_rate, sell_nums_6m_rate, sell_nums_1y_rate,
                                        diff_1d_price, diff_3d_price, diff_7d_price, diff_15d_price, diff_1m_price, diff_3m_price, diff_6m_price, diff_1y_price,
                                        before_1d_price, before_3d_price, before_7d_price, before_15d_price, before_1m_price, before_3m_price, before_6m_price, before_1y_price,
                                        trans_count_24h, trans_count_48h, trans_count_diff_48h, trans_count_rate_48h,
                                        trans_amount_24h, trans_amount_48h,
                                        sell_nums_1d_diff, sell_nums_3d_diff, sell_nums_7d_diff, sell_nums_15d_diff, sell_nums_1m_diff, sell_nums_3m_diff, sell_nums_6m_diff, sell_nums_1y_diff,
                                        survive_num, update_time, ranking_info
                                    ) VALUES (
                                        %(item_id)s, %(snapshot_date)s, %(snapshot_time)s, %(data_source)s, %(current_price)s,
                                        %(diff_1d)s, %(diff_3d)s, %(diff_7d)s, %(diff_15d)s, %(diff_1m)s, %(diff_3m)s, %(diff_6m)s, %(diff_1y)s,
                                        %(trans_count_1d)s, %(trans_count_3d)s, %(trans_count_7d)s, %(trans_count_15d)s, %(trans_count_1m)s, %(trans_count_3m)s,
                                        %(trans_amount_1d)s, %(trans_amount_3d)s, %(trans_amount_7d)s, %(trans_amount_15d)s, %(trans_amount_1m)s, %(trans_amount_3m)s,
                                        %(hot_rank)s, %(hot_count)s, %(hot_keep_days)s, %(hot_rank_change)s,
                                        %(sell_nums)s, %(sell_nums_1d)s, %(sell_nums_3d)s, %(sell_nums_7d)s, %(sell_nums_15d)s, %(sell_nums_1m)s, %(sell_nums_3m)s, %(sell_nums_6m)s, %(sell_nums_1y)s,
                                        %(sell_nums_1d_rate)s, %(sell_nums_3d_rate)s, %(sell_nums_7d_rate)s, %(sell_nums_15d_rate)s, %(sell_nums_1m_rate)s, %(sell_nums_3m_rate)s, %(sell_nums_6m_rate)s, %(sell_nums_1y_rate)s,
                                        %(diff_1d_price)s, %(diff_3d_price)s, %(diff_7d_price)s, %(diff_15d_price)s, %(diff_1m_price)s, %(diff_3m_price)s, %(diff_6m_price)s, %(diff_1y_price)s,
                                        %(before_1d_price)s, %(before_3d_price)s, %(before_7d_price)s, %(before_15d_price)s, %(before_1m_price)s, %(before_3m_price)s, %(before_6m_price)s, %(before_1y_price)s,
                                        %(trans_count_24h)s, %(trans_count_48h)s, %(trans_count_diff_48h)s, %(trans_count_rate_48h)s,
                                        %(trans_amount_24h)s, %(trans_amount_48h)s,
                                        %(sell_nums_1d_diff)s, %(sell_nums_3d_diff)s, %(sell_nums_7d_diff)s, %(sell_nums_15d_diff)s, %(sell_nums_1m_diff)s, %(sell_nums_3m_diff)s, %(sell_nums_6m_diff)s, %(sell_nums_1y_diff)s,
                                        %(survive_num)s, %(update_time)s, %(ranking_info)s
                                    ) ON DUPLICATE KEY UPDATE
                                        snapshot_time = VALUES(snapshot_time), data_source = VALUES(data_source), current_price = VALUES(current_price),
                                        diff_1d = VALUES(diff_1d), diff_3d = VALUES(diff_3d), diff_7d = VALUES(diff_7d), diff_15d = VALUES(diff_15d),
                                        diff_1m = VALUES(diff_1m), diff_3m = VALUES(diff_3m), diff_6m = VALUES(diff_6m), diff_1y = VALUES(diff_1y),
                                        trans_count_1d = VALUES(trans_count_1d), trans_count_3d = VALUES(trans_count_3d), trans_count_7d = VALUES(trans_count_7d),
                                        trans_count_15d = VALUES(trans_count_15d), trans_count_1m = VALUES(trans_count_1m), trans_count_3m = VALUES(trans_count_3m),
                                        trans_amount_1d = VALUES(trans_amount_1d), trans_amount_3d = VALUES(trans_amount_3d), trans_amount_7d = VALUES(trans_amount_7d),
                                        trans_amount_15d = VALUES(trans_amount_15d), trans_amount_1m = VALUES(trans_amount_1m), trans_amount_3m = VALUES(trans_amount_3m),
                                        hot_rank = VALUES(hot_rank), hot_count = VALUES(hot_count), hot_keep_days = VALUES(hot_keep_days), hot_rank_change = VALUES(hot_rank_change),
                                        sell_nums = VALUES(sell_nums), sell_nums_1d = VALUES(sell_nums_1d), sell_nums_3d = VALUES(sell_nums_3d), sell_nums_7d = VALUES(sell_nums_7d),
                                        sell_nums_15d = VALUES(sell_nums_15d), sell_nums_1m = VALUES(sell_nums_1m), sell_nums_3m = VALUES(sell_nums_3m), sell_nums_6m = VALUES(sell_nums_6m), sell_nums_1y = VALUES(sell_nums_1y),
                                        sell_nums_1d_rate = VALUES(sell_nums_1d_rate), sell_nums_3d_rate = VALUES(sell_nums_3d_rate), sell_nums_7d_rate = VALUES(sell_nums_7d_rate),
                                        sell_nums_15d_rate = VALUES(sell_nums_15d_rate), sell_nums_1m_rate = VALUES(sell_nums_1m_rate), sell_nums_3m_rate = VALUES(sell_nums_3m_rate), sell_nums_6m_rate = VALUES(sell_nums_6m_rate), sell_nums_1y_rate = VALUES(sell_nums_1y_rate),
                                        diff_1d_price = VALUES(diff_1d_price), diff_3d_price = VALUES(diff_3d_price), diff_7d_price = VALUES(diff_7d_price),
                                        diff_15d_price = VALUES(diff_15d_price), diff_1m_price = VALUES(diff_1m_price), diff_3m_price = VALUES(diff_3m_price),
                                        diff_6m_price = VALUES(diff_6m_price), diff_1y_price = VALUES(diff_1y_price),
                                        before_1d_price = VALUES(before_1d_price), before_3d_price = VALUES(before_3d_price), before_7d_price = VALUES(before_7d_price),
                                        before_15d_price = VALUES(before_15d_price), before_1m_price = VALUES(before_1m_price), before_3m_price = VALUES(before_3m_price),
                                        before_6m_price = VALUES(before_6m_price), before_1y_price = VALUES(before_1y_price),
                                        trans_count_24h = VALUES(trans_count_24h), trans_count_48h = VALUES(trans_count_48h),
                                        trans_count_diff_48h = VALUES(trans_count_diff_48h), trans_count_rate_48h = VALUES(trans_count_rate_48h),
                                        trans_amount_24h = VALUES(trans_amount_24h), trans_amount_48h = VALUES(trans_amount_48h),
                                        sell_nums_1d_diff = VALUES(sell_nums_1d_diff), sell_nums_3d_diff = VALUES(sell_nums_3d_diff),
                                        sell_nums_7d_diff = VALUES(sell_nums_7d_diff), sell_nums_15d_diff = VALUES(sell_nums_15d_diff),
                                        sell_nums_1m_diff = VALUES(sell_nums_1m_diff), sell_nums_3m_diff = VALUES(sell_nums_3m_diff), sell_nums_6m_diff = VALUES(sell_nums_6m_diff), sell_nums_1y_diff = VALUES(sell_nums_1y_diff),
                                        survive_num = VALUES(survive_num), update_time = VALUES(update_time), ranking_info = VALUES(ranking_info)
                                """, snapshot_record)

                            elif update_strategy == "ranking_only":
                                # 只更新排名信息（数据库数据更新，但需要添加新的排行榜排名）
                                merged_ranking_info = self._merge_ranking_info(
                                    cursor,
                                    snapshot_record["item_id"],
                                    snapshot_record["snapshot_date"],
                                    snapshot_record["ranking_info"],
                                    current_ranking_key
                                )

                                # 只更新排名信息和快照时间
                                cursor.execute("""
                                    UPDATE market_snapshots
                                    SET ranking_info = %s, snapshot_time = %s
                                    WHERE item_id = %s AND snapshot_date = %s
                                """, (merged_ranking_info, snapshot_record["snapshot_time"],
                                     snapshot_record["item_id"], snapshot_record["snapshot_date"]))

                                logger.info(f"🏷️ 仅更新排名信息: {snapshot_record['item_id']} - {current_ranking_key}")

                            else:
                                # 跳过更新
                                logger.debug(f"⏭️ 跳过更新: {snapshot_record['item_id']} - 策略: {update_strategy}")

                        # 保存平台价格数据
                        platform_info_list = item.get("platformInfoList", [])
                        for platform_info in platform_info_list:
                            try:
                                # 获取价格并检查是否为0
                                price = self._safe_decimal(platform_info.get("price"))
                                if price is None or price <= 0:
                                    # 价格为0或无效的平台不保存
                                    logger.debug(f"跳过价格为{price}的平台: {platform_info.get('platformName', 'Unknown')}")
                                    continue

                                platform_record = {
                                    "item_id": item_record["item_id"],
                                    "platform_enum": str(platform_info.get("platformEnum", ""))[:50],
                                    "platform_name": str(platform_info.get("platformName", ""))[:100],
                                    "price": price,
                                    "update_time": datetime.fromtimestamp(platform_info.get("updateTime", 0)) if platform_info.get("updateTime") else snapshot_time
                                }

                                cursor.execute("""
                                    INSERT INTO platform_prices (item_id, platform_enum, platform_name, price, update_time)
                                    VALUES (%(item_id)s, %(platform_enum)s, %(platform_name)s, %(price)s, %(update_time)s)
                                    ON DUPLICATE KEY UPDATE
                                    platform_name = VALUES(platform_name),
                                    price = VALUES(price),
                                    update_time = VALUES(update_time)
                                """, platform_record)

                            except Exception as e:
                                logger.error(f"❌ 保存平台价格失败: {e}")
                                continue

                        success_count += 1

                    except Exception as e:
                        logger.error(f"❌ 保存单条数据失败: {e}")
                        continue

                connection.commit()
                logger.info(f"💾 批量保存成功: {success_count}条数据")

                # 更新总保存计数
                if not hasattr(self, 'total_saved_count'):
                    self.total_saved_count = 0
                self.total_saved_count += success_count

                # 更新爬取进度（如果有当前记录ID）
                if hasattr(self, 'current_record_id') and self.current_record_id:
                    total_scraped = getattr(self, 'total_scraped_count', 0)
                    self.scraping_manager.update_progress(
                        self.current_record_id,
                        total_scraped,
                        self.total_saved_count
                    )

            connection.close()
            return success_count

        except Exception as e:
            logger.error(f"❌ 批量保存数据失败: {e}")
            return 0

    def _should_update_snapshot(self, cursor, snapshot_record, item):
        """检查是否需要更新快照数据，返回更新策略"""
        try:
            # 获取API数据中的更新时间
            api_update_time = item.get("updateTime")
            if not api_update_time:
                return "full_update"  # 如果没有更新时间，默认全量更新

            # 查询数据库中的最新记录
            cursor.execute("""
                SELECT update_time, ranking_info FROM market_snapshots
                WHERE item_id = %s AND snapshot_date = %s
                ORDER BY snapshot_time DESC LIMIT 1
            """, (snapshot_record["item_id"], snapshot_record["snapshot_date"]))

            result = cursor.fetchone()
            if not result:
                return "full_update"  # 如果数据库中没有记录，需要插入

            db_update_time, existing_ranking_info = result

            # 比较更新时间（API时间通常是时间戳）
            try:
                if isinstance(api_update_time, (int, float)):
                    # 时间戳格式 - 直接使用
                    api_datetime = datetime.fromtimestamp(api_update_time)
                elif isinstance(api_update_time, str) and api_update_time.isdigit():
                    # 字符串数字格式的时间戳
                    api_datetime = datetime.fromtimestamp(int(api_update_time))
                else:
                    # 其他字符串格式，尝试解析
                    api_datetime = datetime.fromisoformat(str(api_update_time).replace('Z', '+00:00'))

                # 判断更新策略
                if api_datetime > db_update_time:
                    # API数据更新，全量更新并合并排名信息
                    return "full_update_with_merge"
                else:
                    # 数据库数据更新，只更新排名信息
                    return "ranking_only"

            except Exception as e:
                logger.warning(f"⚠️ 解析更新时间失败: {e}, 默认全量更新")
                return "full_update"

        except Exception as e:
            logger.warning(f"⚠️ 检查更新时间失败: {e}, 默认全量更新")
            return "full_update"

    def _convert_to_db_format(self, item, data_source, snapshot_time):
        """将API数据转换为数据库格式"""
        try:
            # 从原始JSON结构中提取饰品基本信息
            item_info = item.get("itemInfoVO", {})
            update_freq = item.get("updateFrequency", {})

            item_record = {
                "item_id": str(item_info.get("itemId", "")),
                "item_type": str(item_info.get("itemType", ""))[:100],  # 使用实际的itemType
                "def_index_name": str(item_info.get("defIndexName", ""))[:100],  # 正确的字段名
                "name": str(item_info.get("name", ""))[:255],
                "market_hash_name": str(item_info.get("marketHashName", ""))[:255],  # 正确的字段名
                "quality": str(item_info.get("quality", ""))[:50],
                "rarity": str(item_info.get("rarity", ""))[:50],
                "exterior": str(item_info.get("exterior", ""))[:50],
                "image_url": str(item_info.get("imageUrl", ""))[:500],
                "update_level": str(update_freq.get("key", ""))[:50]  # 新增update_level字段
            }

            # 获取API数据的更新时间
            api_update_time = item.get("updateTime")
            update_time = None
            if api_update_time:
                try:
                    if isinstance(api_update_time, (int, float)):
                        # 时间戳格式 - 直接使用，不需要除以1000
                        update_time = datetime.fromtimestamp(api_update_time)
                    elif isinstance(api_update_time, str) and api_update_time.isdigit():
                        # 字符串数字格式的时间戳
                        update_time = datetime.fromtimestamp(int(api_update_time))
                    else:
                        # 其他字符串格式
                        update_time = datetime.fromisoformat(str(api_update_time).replace('Z', '+00:00'))
                except Exception as e:
                    logger.warning(f"⚠️ 解析API更新时间失败: {e}, 原始值: {api_update_time}")
                    update_time = snapshot_time

            # 从原始JSON结构中提取市场快照信息
            sell_price_info = item.get("sellPriceInfoVO", {})
            sell_nums_info = item.get("sellNumsInfoVO", {})
            transaction_count_info = item.get("transactionCountInfoVO", {})
            transaction_amount_info = item.get("transactionAmountInfoVO", {})
            hot_info = item.get("hotVO", {})

            snapshot_record = {
                "item_id": item_record["item_id"],
                "snapshot_time": snapshot_time,
                "snapshot_date": snapshot_time.date(),  # 添加日期字段作为主键的一部分
                "data_source": data_source,
                "current_price": self._safe_decimal(sell_price_info.get("price")),
                "diff_1d": self._safe_decimal(sell_price_info.get("diff1Days")),
                "diff_3d": self._safe_decimal(sell_price_info.get("diff3Days")),
                "diff_7d": self._safe_decimal(sell_price_info.get("diff7Days")),
                "diff_15d": self._safe_decimal(sell_price_info.get("diff15Days")),
                "diff_1m": self._safe_decimal(sell_price_info.get("diff1Months")),
                "diff_3m": self._safe_decimal(sell_price_info.get("diff3Months")),
                "diff_6m": self._safe_decimal(sell_price_info.get("diff6Months")),
                "diff_1y": self._safe_decimal(sell_price_info.get("diff1Years")),
                "trans_count_1d": self._safe_int(transaction_count_info.get("transactionCount1Days")),
                "trans_count_3d": self._safe_int(transaction_count_info.get("transactionCount3Days")),
                "trans_count_7d": self._safe_int(transaction_count_info.get("transactionCount7Days")),
                "trans_count_15d": self._safe_int(transaction_count_info.get("transactionCount15Days")),
                "trans_count_1m": self._safe_int(transaction_count_info.get("transactionCount1Months")),
                "trans_count_3m": self._safe_int(transaction_count_info.get("transactionCount3Months")),
                "trans_amount_1d": self._safe_decimal(transaction_amount_info.get("transactionAmount1Days")),
                "trans_amount_3d": self._safe_decimal(transaction_amount_info.get("transactionAmount3Days")),
                "trans_amount_7d": self._safe_decimal(transaction_amount_info.get("transactionAmount7Days")),
                "trans_amount_15d": self._safe_decimal(transaction_amount_info.get("transactionAmount15Days")),
                "trans_amount_1m": self._safe_decimal(transaction_amount_info.get("transactionAmount1Months")),
                "trans_amount_3m": self._safe_decimal(transaction_amount_info.get("transactionAmount3Months")),
                "hot_rank": self._safe_int(hot_info.get("hotRank")),
                "hot_count": self._safe_int(hot_info.get("hotCount")),
                "hot_keep_days": self._safe_int(hot_info.get("hotKeepDays")),
                "hot_rank_change": self._safe_int(hot_info.get("hotRankChange")),
                "sell_nums": self._safe_int(sell_nums_info.get("sellNums")),
                "sell_nums_1d": self._safe_int(sell_nums_info.get("sellNums1Days")),
                "sell_nums_3d": self._safe_int(sell_nums_info.get("sellNums3Days")),
                "sell_nums_7d": self._safe_int(sell_nums_info.get("sellNums7Days")),
                "sell_nums_15d": self._safe_int(sell_nums_info.get("sellNums15Days")),
                "sell_nums_1m": self._safe_int(sell_nums_info.get("sellNums1Months")),
                "sell_nums_3m": self._safe_int(sell_nums_info.get("sellNums3Months")),
                "sell_nums_6m": self._safe_int(sell_nums_info.get("sellNums6Months")),
                "sell_nums_1y": self._safe_int(sell_nums_info.get("sellNums1Years")),
                "sell_nums_1d_rate": self._safe_decimal(sell_nums_info.get("sellNums1DaysRate")),
                "sell_nums_3d_rate": self._safe_decimal(sell_nums_info.get("sellNums3DaysRate")),
                "sell_nums_7d_rate": self._safe_decimal(sell_nums_info.get("sellNums7DaysRate")),
                "sell_nums_15d_rate": self._safe_decimal(sell_nums_info.get("sellNums15DaysRate")),
                "sell_nums_1m_rate": self._safe_decimal(sell_nums_info.get("sellNums1MonthsRate")),
                "sell_nums_3m_rate": self._safe_decimal(sell_nums_info.get("sellNums3MonthsRate")),
                "sell_nums_6m_rate": self._safe_decimal(sell_nums_info.get("sellNums6MonthsRate")),
                "sell_nums_1y_rate": self._safe_decimal(sell_nums_info.get("sellNums1YearsRate")),
                # 添加价格相关字段
                "diff_1d_price": self._safe_decimal(sell_price_info.get("diff1DaysPrice")),
                "diff_3d_price": self._safe_decimal(sell_price_info.get("diff3DaysPrice")),
                "diff_7d_price": self._safe_decimal(sell_price_info.get("diff7DaysPrice")),
                "diff_15d_price": self._safe_decimal(sell_price_info.get("diff15DaysPrice")),
                "diff_1m_price": self._safe_decimal(sell_price_info.get("diff1MonthsPrice")),
                "diff_3m_price": self._safe_decimal(sell_price_info.get("diff3MonthsPrice")),
                "diff_6m_price": self._safe_decimal(sell_price_info.get("diff6MonthsPrice")),
                "diff_1y_price": self._safe_decimal(sell_price_info.get("diff1YearsPrice")),
                # 添加before价格字段
                "before_1d_price": self._safe_decimal(sell_price_info.get("before1DaysPrice")),
                "before_3d_price": self._safe_decimal(sell_price_info.get("before3DaysPrice")),
                "before_7d_price": self._safe_decimal(sell_price_info.get("before7DaysPrice")),
                "before_15d_price": self._safe_decimal(sell_price_info.get("before15DaysPrice")),
                "before_1m_price": self._safe_decimal(sell_price_info.get("before1MonthsPrice")),
                "before_3m_price": self._safe_decimal(sell_price_info.get("before3MonthsPrice")),
                "before_6m_price": self._safe_decimal(sell_price_info.get("before6MonthsPrice")),
                "before_1y_price": self._safe_decimal(sell_price_info.get("before1YearsPrice")),
                # 添加交易数量的24小时和48小时数据
                "trans_count_24h": self._safe_int(transaction_count_info.get("transactionCount24Hours")),
                "trans_count_48h": self._safe_int(transaction_count_info.get("transactionCount48Hours")),
                "trans_count_diff_48h": self._safe_int(transaction_count_info.get("transactionCountDiff48Hours")),
                "trans_count_rate_48h": self._safe_decimal(transaction_count_info.get("transactionCountRate48Hours")),
                # 添加交易金额的24小时和48小时数据
                "trans_amount_24h": self._safe_decimal(transaction_amount_info.get("transactionAmount24Hours")),
                "trans_amount_48h": self._safe_decimal(transaction_amount_info.get("transactionAmount48Hours")),
                # 添加销售数量的差值字段
                "sell_nums_1d_diff": self._safe_int(sell_nums_info.get("sellNums1DaysDiff")),
                "sell_nums_3d_diff": self._safe_int(sell_nums_info.get("sellNums3DaysDiff")),
                "sell_nums_7d_diff": self._safe_int(sell_nums_info.get("sellNums7DaysDiff")),
                "sell_nums_15d_diff": self._safe_int(sell_nums_info.get("sellNums15DaysDiff")),
                "sell_nums_1m_diff": self._safe_int(sell_nums_info.get("sellNums1MonthsDiff")),
                "sell_nums_3m_diff": self._safe_int(sell_nums_info.get("sellNums3MonthsDiff")),
                "sell_nums_6m_diff": self._safe_int(sell_nums_info.get("sellNums6MonthsDiff")),
                "sell_nums_1y_diff": self._safe_int(sell_nums_info.get("sellNums1YearsDiff")),
                "survive_num": self._safe_int(item.get("surviveNum")),
                "update_time": update_time or snapshot_time  # API数据的更新时间
            }

            # 新增：添加排名信息处理
            ranking_key = self._parse_ranking_key(data_source)
            ranking_info = self._generate_ranking_info(data_source, item)
            if ranking_info:
                snapshot_record["ranking_info"] = ranking_info
                snapshot_record["_current_ranking_key"] = ranking_key  # 临时字段，用于后续合并

            return item_record, snapshot_record

        except Exception as e:
            logger.error(f"❌ 转换数据格式失败: {e}")
            return None, None

    def _safe_decimal(self, value):
        """安全转换为decimal"""
        if value is None or value == '':
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None

    def _safe_int(self, value):
        """安全转换为int"""
        if value is None or value == '':
            return None
        try:
            return int(float(str(value)))
        except (ValueError, TypeError):
            return None

    def _generate_ranking_info(self, data_source, item):
        """生成排名信息JSON"""
        try:
            import json

            # 解析数据源获取排行榜类型
            ranking_key = self._parse_ranking_key(data_source)

            # 获取当前饰品在批次中的位置作为排名
            ranking_position = item.get('_ranking_position')
            if ranking_position is None and hasattr(self, 'current_batch_items'):
                # 如果没有预设排名位置，从当前批次中查找位置
                for i, batch_item in enumerate(self.current_batch_items):
                    if batch_item.get('itemInfoVO', {}).get('itemId') == item.get('itemInfoVO', {}).get('itemId'):
                        ranking_position = i + 1
                        break

            if ranking_position is None:
                # 如果还是找不到位置，使用默认值1
                ranking_position = 1

            # 生成排名信息
            ranking_info = {ranking_key: ranking_position}

            # 返回JSON字符串
            return json.dumps(ranking_info, ensure_ascii=False, default=str)

        except Exception as e:
            logger.warning(f"⚠️ 生成排名信息失败: {e}")
            return None

    def _parse_ranking_key(self, data_source):
        """解析排行榜标识（复用DataImportService的逻辑）"""
        # 从data_source解析：price_gain_上涨_1d -> price_gain_1d
        parts = data_source.split('_')
        if len(parts) >= 3:
            # 对于复合类型如price_gain，需要保持完整
            if len(parts) == 4 and parts[0] in ['price']:
                # price_gain_上涨_1d -> price_gain_1d
                ranking_type = f"{parts[0]}_{parts[1]}"
                time_period = parts[-1]
                return f"{ranking_type}_{time_period}"
            else:
                # 其他情况：volume_成交量_1d -> volume_1d
                ranking_type = parts[0]
                time_period = parts[-1]
                return f"{ranking_type}_{time_period}"

        return data_source

    def _merge_ranking_info(self, cursor, item_id, snapshot_date, new_ranking_info, current_ranking_key):
        """合并排名信息，避免重复更新同一排行榜"""
        try:
            import json

            # 获取数据库中现有的排名信息
            cursor.execute("""
                SELECT ranking_info FROM market_snapshots
                WHERE item_id = %s AND snapshot_date = %s
                ORDER BY snapshot_time DESC LIMIT 1
            """, (item_id, snapshot_date))

            result = cursor.fetchone()
            existing_ranking_info = {}

            if result and result[0]:
                try:
                    existing_ranking_info = json.loads(result[0])
                except json.JSONDecodeError:
                    logger.warning(f"⚠️ 解析现有排名信息失败: {result[0]}")
                    existing_ranking_info = {}

            # 解析新的排名信息
            new_ranking_data = {}
            if new_ranking_info:
                try:
                    new_ranking_data = json.loads(new_ranking_info)
                except json.JSONDecodeError:
                    logger.warning(f"⚠️ 解析新排名信息失败: {new_ranking_info}")
                    return new_ranking_info

            # 检查是否已经存在当前排行榜的排名
            if current_ranking_key in existing_ranking_info:
                logger.debug(f"🔄 排行榜 {current_ranking_key} 已存在排名 {existing_ranking_info[current_ranking_key]}，跳过重复更新")
                # 返回现有的排名信息，不做修改
                return json.dumps(existing_ranking_info, ensure_ascii=False, default=str)

            # 合并排名信息
            merged_ranking = existing_ranking_info.copy()
            merged_ranking.update(new_ranking_data)

            logger.info(f"🔗 合并排名信息: {existing_ranking_info} + {new_ranking_data} = {merged_ranking}")

            return json.dumps(merged_ranking, ensure_ascii=False, default=str)

        except Exception as e:
            logger.error(f"❌ 合并排名信息失败: {e}")
            return new_ranking_info

    async def setup_filters(self, page):
        """设置过滤条件：价格大于100，外观选择崭新、略磨、久经"""
        try:
            logger.info("🔧 开始设置过滤条件...")

            # 等待页面加载完成
            await page.wait_for_timeout(3000)

            # 点击筛选按钮 - 使用成功测试的方法
            try:
                filter_button = page.get_by_text('已筛选')
                if await filter_button.is_visible(timeout=5000):
                    await filter_button.click()
                    logger.info("✅ 成功点击筛选按钮")
                    await page.wait_for_timeout(2000)
                else:
                    logger.warning("⚠️ 未找到筛选按钮，跳过过滤设置")
                    return True

            except Exception as e:
                logger.warning(f"⚠️ 点击筛选按钮失败: {e}")
                return True

            # 等待过滤对话框出现
            await page.wait_for_timeout(2000)

            # 设置价格过滤（最小值100）
            try:
                # 查找数字输入框
                number_inputs = page.locator('input[type="number"]')
                count = await number_inputs.count()
                logger.info(f"找到 {count} 个数字输入框")

                if count > 0:
                    # 通常第一个是最小价格输入框
                    min_price_input = number_inputs.first
                    await min_price_input.clear()
                    await min_price_input.fill('100')
                    logger.info("✅ 设置最小价格: 100")
                    await page.wait_for_timeout(1000)
                else:
                    logger.warning("⚠️ 未找到价格输入框")

            except Exception as e:
                logger.warning(f"⚠️ 设置价格过滤失败: {e}")

            # 点击外观选项卡
            try:
                appearance_tab = page.get_by_text('外观', exact=True)
                if await appearance_tab.is_visible(timeout=3000):
                    await appearance_tab.click()
                    logger.info("✅ 点击外观选项卡")
                    await page.wait_for_timeout(1000)
                else:
                    logger.warning("⚠️ 未找到外观选项卡")

            except Exception as e:
                logger.warning(f"⚠️ 点击外观选项卡失败: {e}")

            # 选择外观选项：崭新出厂、略有磨损、久经沙场
            appearance_options = ['崭新出厂', '略有磨损', '久经沙场']

            for option in appearance_options:
                try:
                    option_element = page.get_by_text(option, exact=True)
                    if await option_element.is_visible(timeout=2000):
                        await option_element.click()
                        logger.info(f"✅ 选择外观: {option}")
                        await page.wait_for_timeout(500)
                    else:
                        logger.warning(f"⚠️ 未找到外观选项: {option}")

                except Exception as e:
                    logger.warning(f"⚠️ 选择外观 {option} 失败: {e}")

            # 点击应用按钮 - 使用更精确的选择器
            try:
                # 首先尝试使用对话框内的应用按钮
                dialog_apply_button = page.locator('dialog').get_by_text('应用', exact=True)
                if await dialog_apply_button.is_visible(timeout=3000):
                    await dialog_apply_button.click()
                    logger.info("✅ 点击对话框内的应用按钮")
                    await page.wait_for_timeout(5000)  # 等待过滤结果加载
                else:
                    # 如果对话框方法失败，尝试使用筛选按钮附近的应用按钮
                    filter_apply_button = page.locator('.filter-btn').get_by_text('应用')
                    if await filter_apply_button.is_visible(timeout=3000):
                        await filter_apply_button.click()
                        logger.info("✅ 点击筛选区域的应用按钮")
                        await page.wait_for_timeout(5000)
                    else:
                        logger.warning("⚠️ 未找到应用按钮")

            except Exception as e:
                logger.warning(f"⚠️ 点击应用按钮失败: {e}")
                # 如果上述方法都失败，记录页面上所有"应用"相关的元素
                try:
                    apply_elements = await page.get_by_text('应用').all()
                    logger.info(f"页面上找到 {len(apply_elements)} 个包含'应用'的元素")
                    for i, element in enumerate(apply_elements):
                        is_visible = await element.is_visible()
                        logger.info(f"  元素 {i+1}: 可见={is_visible}")
                except:
                    pass

            logger.info("🎯 过滤条件设置完成")
            return True

        except Exception as e:
            logger.error(f"❌ 设置过滤条件失败: {e}")
            return False

    async def scrape_ranking(self, page, ranking_key, sub_option, time_period):
        """抓取指定榜单数据"""
        ranking_config = self.rankings[ranking_key]
        logger.info(f"🎯 开始抓取: {ranking_config['name']} - {sub_option} - {time_period}")

        # 重置排名计数器 - 每个新排行榜从1开始
        self.current_ranking_position = 0
        logger.info(f"🏆 重置排名计数器，开始新排行榜: {ranking_config['name']}")

        # 重置筛选状态 - 确保每个榜单开始时都是未筛选状态
        self.filters_ready = False

        # 设置当前榜单信息，用于实时保存
        self.current_ranking_info = {
            'ranking_key': ranking_key,
            'sub_option': sub_option,
            'time_period': time_period
        }

        # 清空之前的数据，避免内存累积
        self.collected_data = []

        # 初始化保存统计
        self.total_saved_count = 0

        try:
            # 1. 选择榜单标签
            if not await self.select_tab(page, ranking_config['tab_name']):
                return None

            # 2. 选择子选项（饰品热度榜不需要选择子选项）
            if ranking_config['tab_name'] != "饰品热度榜":
                if not await self.select_sub_option(page, sub_option):
                    return None
            else:
                logger.info("✅ 饰品热度榜无需选择子选项，直接使用默认选项")

            # 3. 选择时间周期（饰品热度榜不需要选择时间周期）
            if ranking_config['tab_name'] != "饰品热度榜":
                if not await self.select_time_period(page, time_period):
                    return None
            else:
                logger.info("✅ 饰品热度榜无需选择时间周期，默认为近1天")

            # 4. 选择排序方式（饰品热度榜不需要排序）
            if 'sort_order' in ranking_config and ranking_config['tab_name'] != "饰品热度榜":
                await self.select_sort_order(page, ranking_config['sort_order'])
            elif ranking_config['tab_name'] == "饰品热度榜":
                logger.info("✅ 饰品热度榜无需选择排序方式，使用默认排序")

            # 4.5 设置过滤条件（价格>100，外观选择崭新、略磨、久经）
            await self.setup_filters(page)

            # 筛选条件设置完成，标记为可以保存正式数据
            self.filters_ready = True
            logger.info("✅ 筛选条件设置完成，开始收集正式数据")

            # 5. 滚动加载数据
            data = await self.scroll_and_load_data(page, self.max_items)

            # 6. 数据已在实时保存过程中保存，这里只做统计
            actual_count = len(data)
            saved_count = getattr(self, 'total_saved_count', 0)

            logger.info(f"🎉 榜单 [{ranking_config['name']} - {sub_option} - {time_period}] 抓取完成!")
            logger.info(f"📊 数据统计: 收集{actual_count}条，保存{saved_count}条")

            if actual_count >= 500:
                logger.info(f"✅ 数据完整: 该榜单包含{actual_count}条饰品数据")
            else:
                logger.info(f"ℹ️ 数据说明: 该榜单实际包含{actual_count}条饰品数据")
            
            return data
            
        except Exception as e:
            logger.error(f"❌ 抓取榜单数据失败: {e}")
            return None
            
    async def run(self, resume_mode: bool = True):
        """运行抓取程序 - 每个排行榜使用独立的浏览器

        Args:
            resume_mode: 是否启用续爬模式，默认True
        """
        logger.info("🚀 开始SteamDT数据抓取")

        # 获取今日汇总
        summary = self.scraping_manager.get_daily_summary()
        logger.info(f"📊 今日爬取状态: 总{summary['total_rankings']}个，已完成{summary['completed']}个，待处理{summary['pending']}个")

        if resume_mode:
            # 续爬模式：只处理未完成的排行榜
            pending_rankings = self.scraping_manager.get_pending_rankings()
            if not pending_rankings:
                logger.info("✅ 所有排行榜都已完成，无需续爬")
                return

            logger.info(f"🔄 续爬模式：发现{len(pending_rankings)}个待处理排行榜")
            rankings_to_process = pending_rankings
        else:
            # 全新爬取模式：创建所有排行榜记录
            logger.info("🆕 全新爬取模式：创建所有排行榜记录")
            rankings_to_process = self._create_all_ranking_records()

        total_scraped = 0
        processed_count = 0

        # 处理排行榜列表 - 每个榜单使用独立的浏览器
        for ranking_info in rankings_to_process:
            processed_count += 1

            if resume_mode:
                # 续爬模式：使用现有记录
                record_id = ranking_info['id']
                ranking_key = ranking_info['ranking_key']
                sub_option = ranking_info['sub_option']
                time_period = ranking_info['time_period']
                ranking_name = ranking_info['ranking_name']

                logger.info(f"🔄 续爬 [{processed_count}/{len(rankings_to_process)}]: {ranking_name} - {sub_option} - {time_period}")
                if ranking_info['retry_count'] > 0:
                    logger.info(f"   重试第{ranking_info['retry_count']}次，上次错误: {ranking_info.get('error_message', 'N/A')}")
            else:
                # 全新模式：从配置创建
                ranking_key = ranking_info['ranking_key']
                sub_option = ranking_info['sub_option']
                time_period = ranking_info['time_period']
                ranking_name = ranking_info['ranking_name']
                record_id = ranking_info['record_id']

                logger.info(f"🆕 新爬 [{processed_count}/{len(rankings_to_process)}]: {ranking_name} - {sub_option} - {time_period}")

            # 设置当前记录ID
            self.current_record_id = record_id

            # 开始爬取
            self.scraping_manager.start_scraping(record_id)

            # 为每个排行榜创建独立的浏览器
            playwright, browser, page = None, None, None
            try:
                logger.info(f"🌐 为榜单 [{ranking_name}] 创建新浏览器...")
                playwright, browser, page = await self.create_browser()

                # 设置API监控
                self.setup_api_monitoring(page)

                # 访问页面
                logger.info("🌐 正在访问SteamDT页面...")
                await page.goto(self.base_url)
                await asyncio.sleep(5)

                # 关闭可能存在的弹窗
                await self.close_popup_if_exists(page)

                # 抓取当前排行榜
                data = await self.scrape_ranking(page, ranking_key, sub_option, time_period)
                if data:
                    total_scraped += len(data)
                    # 完成爬取
                    scraped_count = len(data)
                    saved_count = getattr(self, 'total_saved_count', 0)
                    self.scraping_manager.complete_scraping(record_id, scraped_count, saved_count)
                    logger.info(f"✅ 完成: 爬取{scraped_count}条，保存{saved_count}条")
                else:
                    # 爬取失败
                    self.scraping_manager.fail_scraping(record_id, "数据获取失败")
                    logger.error(f"❌ 失败: 数据获取失败")

            except Exception as e:
                # 爬取异常
                error_msg = str(e)
                self.scraping_manager.fail_scraping(record_id, error_msg)
                logger.error(f"❌ 异常: {error_msg}")

            finally:
                # 关闭当前排行榜的浏览器
                if browser:
                    try:
                        await browser.close()
                        logger.info(f"🔒 已关闭榜单 [{ranking_name}] 的浏览器")
                    except Exception as e:
                        logger.warning(f"⚠️ 关闭浏览器时出错: {e}")

                if playwright:
                    try:
                        await playwright.stop()
                    except Exception as e:
                        logger.warning(f"⚠️ 停止playwright时出错: {e}")

            # 重置保存计数器
            self.total_saved_count = 0

            # 榜单间等待（最后一个不等待）
            if processed_count < len(rankings_to_process):
                delay = random.uniform(60, 180)  # 1-3分钟
                logger.info(f"")
                logger.info(f"{'='*60}")
                logger.info(f"⏳ 榜单间等待{delay/60:.1f}分钟，准备下一个榜单...")
                logger.info(f"{'='*60}")
                await asyncio.sleep(delay)

                # 30%概率额外超长停顿
                if random.random() < 0.3:
                    super_long_delay = random.uniform(300, 600)  # 5-10分钟
                    logger.info(f"😴 触发超长停顿: {super_long_delay/60:.1f}分钟")
                    await asyncio.sleep(super_long_delay)

        # 显示最终汇总
        final_summary = self.scraping_manager.get_daily_summary()
        logger.info(f"🎉 抓取完成！")
        logger.info(f"📊 最终统计: 总{final_summary['total_rankings']}个排行榜")
        logger.info(f"   ✅ 已完成: {final_summary['completed']}个")
        logger.info(f"   ❌ 失败: {final_summary['failed']}个")
        logger.info(f"   🔄 部分完成: {final_summary['partial']}个")
        logger.info(f"   📈 总爬取数据: {final_summary['total_scraped']}条")
        logger.info(f"   💾 总保存数据: {final_summary['total_saved']}条")

    def _create_all_ranking_records(self) -> List[Dict]:
        """创建所有排行榜记录（全新爬取模式）"""
        rankings_to_process = []

        for ranking_key, config in self.rankings.items():
            for sub_option in config['sub_options']:
                for time_period in config['time_periods']:
                    # 创建或获取记录
                    record_id = self.scraping_manager.create_or_update_record(
                        ranking_key=ranking_key,
                        sub_option=sub_option,
                        time_period=time_period,
                        ranking_name=config['name'],
                        expected_count=500  # 预期数量
                    )

                    rankings_to_process.append({
                        'record_id': record_id,
                        'ranking_key': ranking_key,
                        'sub_option': sub_option,
                        'time_period': time_period,
                        'ranking_name': config['name']
                    })

        logger.info(f"📝 创建了{len(rankings_to_process)}个排行榜记录")
        return rankings_to_process

async def main():
    """主函数"""
    import sys

    # 检查命令行参数
    resume_mode = True  # 默认续爬模式
    if len(sys.argv) > 1:
        if sys.argv[1].lower() in ['new', 'fresh', 'full']:
            resume_mode = False
            logger.info("🆕 启用全新爬取模式")
        elif sys.argv[1].lower() in ['resume', 'continue']:
            resume_mode = True
            logger.info("🔄 启用续爬模式")
        elif sys.argv[1].lower() in ['status', 'summary']:
            # 只显示状态，不执行爬取
            scraper = SteamDTScraperFinal()
            summary = scraper.scraping_manager.get_daily_summary()
            print(f"\n📊 今日爬取状态 ({summary['date']}):")
            print(f"  总排行榜数: {summary['total_rankings']}")
            print(f"  已完成: {summary['completed']}")
            print(f"  运行中: {summary['running']}")
            print(f"  失败: {summary['failed']}")
            print(f"  部分完成: {summary['partial']}")
            print(f"  待处理: {summary['pending']}")
            print(f"  完成率: {summary['completion_rate']:.1f}%")
            print(f"  总爬取: {summary['total_scraped']}条")
            print(f"  总保存: {summary['total_saved']}条")

            # 显示待处理列表
            pending = scraper.scraping_manager.get_pending_rankings()
            if pending:
                print(f"\n📋 待处理排行榜 ({len(pending)}个):")
                for item in pending:
                    print(f"  {item['ranking_name']} - {item['sub_option']} - {item['time_period']}")
                    print(f"    状态: {item['status']}, 重试: {item['retry_count']}次, 完成率: {item['completion_rate']:.1f}%")
            return

    scraper = SteamDTScraperFinal()
    await scraper.run(resume_mode=resume_mode)

if __name__ == "__main__":
    print("🚀 SteamDT数据抓取器 - 续爬版本")
    print("=" * 50)
    print("使用方法:")
    print("  python steamdt_scraper_final.py          # 续爬模式（默认）")
    print("  python steamdt_scraper_final.py resume   # 续爬模式")
    print("  python steamdt_scraper_final.py new      # 全新爬取模式")
    print("  python steamdt_scraper_final.py status   # 查看状态")
    print("=" * 50)
    print("📋 支持榜单: 价格榜、在售数榜、成交榜、饰品热度榜")
    print("⏰ 时间范围: 7天、15天、一个月、三个月")
    print("📊 数据量: 每个榜单前500条")
    print("💾 实时保存到数据库")
    print("� 支持断点续爬")
    print("=" * 50)

    asyncio.run(main())
