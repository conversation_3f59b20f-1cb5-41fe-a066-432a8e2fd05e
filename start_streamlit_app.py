#!/usr/bin/env python3
"""
Streamlit应用启动脚本

启动CS2饰品投资分析系统的Streamlit前端应用
"""

import sys
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """启动Streamlit应用"""
    print("=" * 60)
    print("🎮 CS2饰品投资分析系统 - Streamlit前端")
    print("=" * 60)
    print("")
    print("🚀 正在启动Streamlit应用...")
    print("")
    print("📍 应用地址: http://localhost:8504")
    print("")

    try:
        # 启动Streamlit应用
        subprocess.run([
            "streamlit", "run",
            "src/cs2_investment/app/main.py",
            "--server.port", "8504",
            "--server.headless", "true",
            "--browser.gatherUsageStats", "false"
        ], check=True)

    except subprocess.CalledProcessError as e:
        print(f"❌ Streamlit启动失败: {e}")
        print("💡 请确保已安装streamlit: pip install streamlit")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Streamlit应用已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
