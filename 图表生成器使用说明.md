# CS2饰品分析图表生成器使用说明

## 📊 概述

本图表生成器基于现有的CS2饰品投资分析系统，能够生成专业的静态分析图表。参考常规分析的方式，支持多种类型的图表生成。

## 🎯 功能特性

### 支持的图表类型

1. **专业投资仪表板** (`professional_dashboard`)
   - 基于syncps分析系统
   - 包含价格走势、技术指标、风险评估等
   - 专业交易软件风格的综合仪表板

2. **技术指标图表** (`technical_chart`)
   - 基于ssync实时监控系统
   - 增强版K线图、RSI、MACD、成交量等
   - 专业技术分析图表

### 核心功能

- ✅ 静态图表生成（PNG格式）
- ✅ 基于现有分析系统
- ✅ 自动保存到指定目录
- ✅ 支持批量生成
- ✅ 命令行和交互式两种使用方式

## 📁 文件结构

```
项目根目录/
├── generate_analysis_charts.py    # 主图表生成器
├── chart_generator_example.py     # 使用示例脚本
├── 图表生成器使用说明.md          # 本说明文档
└── data/
    ├── analysis_results/           # 图表输出目录
    │   └── {饰品ID}/
    │       ├── {饰品ID}_专业投资仪表板_{时间戳}.png
    │       └── {饰品ID}_技术指标图表_{时间戳}.png
    └── scraped_data/              # 饰品数据目录
        └── {饰品ID}/
```

## 🚀 快速开始

### 1. 命令行使用

```bash
# 查看可用的饰品数据
python generate_analysis_charts.py --list

# 生成所有类型的图表
python generate_analysis_charts.py --item "AK-47 | 红线 (久经沙场)"

# 只生成专业投资仪表板
python generate_analysis_charts.py --item "M4A4 | 龙王" --chart-type dashboard

# 只生成技术指标图表
python generate_analysis_charts.py --item "AWP | 龙狙" --chart-type technical

# 使用数据路径生成图表
python generate_analysis_charts.py --item "示例饰品" --data-path "data/scraped_data/某个饰品ID"
```

### 2. 交互式使用

```bash
# 运行示例脚本，包含交互式选择
python chart_generator_example.py
```

### 3. 编程方式使用

```python
from generate_analysis_charts import AnalysisChartGenerator

# 创建图表生成器
generator = AnalysisChartGenerator()

# 生成所有类型的图表
result = generator.generate_all_charts("AK-47 | 红线 (久经沙场)")

# 生成专业投资仪表板
dashboard_result = generator.generate_professional_dashboard("M4A4 | 龙王")

# 生成技术指标图表
technical_result = generator.generate_technical_chart("AWP | 龙狙")

# 列出可用的饰品
items = generator.list_available_items()
```

## 📋 命令行参数

| 参数 | 简写 | 说明 | 示例 |
|------|------|------|------|
| `--item` | `-i` | 饰品名称或ID | `--item "AK-47 红线"` |
| `--data-path` | `-d` | 数据路径（可选） | `--data-path "data/scraped_data/123"` |
| `--chart-type` | `-t` | 图表类型 | `--chart-type dashboard` |
| `--list` | `-l` | 列出可用饰品 | `--list` |

### 图表类型选项

- `all`: 生成所有类型的图表（默认）
- `dashboard`: 只生成专业投资仪表板
- `technical`: 只生成技术指标图表

## 📈 图表说明

### 专业投资仪表板

基于syncps分析系统生成，包含：

- **价格走势图**: K线图 + 移动平均线 + 买卖信号
- **技术指标**: RSI、MACD、布林线等
- **风险评估**: 多维度风险分析
- **投资建议**: 基于算法的投资建议
- **市场情绪**: 雷达图显示市场情绪
- **综合评分**: 投资价值综合评分

### 技术指标图表

基于ssync实时监控系统生成，包含：

- **增强版K线图**: 专业交易软件风格
- **RSI指标**: 超买超卖分析
- **MACD指标**: 趋势分析
- **成交量分析**: 异常检测
- **KDJ指标**: 随机指标
- **信号标注**: 买卖信号点

## 🔧 配置说明

### 输出目录

图表默认保存到 `data/analysis_results/{饰品ID}/` 目录下，文件命名格式：

- 专业投资仪表板: `{饰品ID}_专业投资仪表板_{时间戳}.png`
- 技术指标图表: `{饰品ID}_技术指标图表_{时间戳}.png`

### 依赖系统

图表生成器依赖以下现有系统：

1. **syncps分析系统** (`src/cs2_investment/fx/syncps/`)
   - `main_analysis_system.py`: 主分析系统
   - `professional_chart_system.py`: 专业图表系统

2. **ssync实时监控系统** (`src/cs2_investment/fx/ssync/`)
   - `real_time_monitor.py`: 实时监控
   - `enhanced_chart_generator.py`: 增强图表生成器

## ⚠️ 注意事项

1. **数据依赖**: 确保目标饰品有相应的数据文件
2. **系统路径**: 图表生成器会自动切换到相应的系统目录
3. **模块导入**: 使用动态导入避免模块冲突
4. **错误处理**: 包含完整的错误处理和回滚机制

## 🐛 故障排除

### 常见问题

1. **"数据加载失败"**
   - 检查饰品名称是否正确
   - 确认 `data/scraped_data/` 目录下有对应数据

2. **"模块未找到"**
   - 确认分析系统文件完整
   - 检查Python路径设置

3. **"图表生成失败"**
   - 查看详细错误信息
   - 确认matplotlib等依赖库已安装

### 调试模式

在代码中添加调试信息：

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📞 技术支持

如遇问题，请检查：

1. 项目依赖是否完整安装
2. 数据文件是否存在
3. 系统路径是否正确
4. Python版本兼容性

## 🔄 更新日志

- **v1.0.0**: 初始版本，支持基础图表生成
- 基于现有分析系统的图表生成功能
- 支持命令行和编程两种使用方式
