#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品智能分析专业图表生成系统

为intelligent_analysis分析系统设计的专业图表生成器
参考syncps的professional_chart_system实现完整的技术指标图表
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Any, Optional
import warnings

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False


class IntelligentChartSystem:
    """智能分析专业图表生成系统 - 参考syncps实现"""

    def __init__(self, analysis_results: Dict[str, Any], item_id: str = None):
        """
        初始化智能图表系统

        Args:
            analysis_results: 智能分析结果
            item_id: 饰品ID
        """
        self.analysis_results = analysis_results
        self.item_id = item_id or "Unknown"

        # 专业配色方案 - 参考syncps
        self.colors = {
            'primary': '#1f77b4',        # 主色调蓝色
            'secondary': '#ff7f0e',      # 次色调橙色
            'success': '#2ca02c',        # 成功绿色
            'danger': '#d62728',         # 危险红色
            'warning': '#ff9800',        # 警告橙色
            'info': '#17a2b8',          # 信息青色
            'light': '#f8f9fa',         # 浅色
            'dark': '#343a40',          # 深色
            'background': '#1e1e1e',    # 背景色
            'grid': '#404040',          # 网格色
            'strategic': '#6A4C93',      # 战略紫
            'tactical': '#1982C4',       # 战术蓝
            'execution': '#8AC926',      # 执行绿
            'comprehensive': '#FFCA3A'   # 综合黄
        }

        print(f"📊 智能分析专业图表系统初始化完成 - 饰品: {self.item_id}")

        # 添加调试日志
        self._debug_analysis_data()

    def _debug_analysis_data(self):
        """调试分析数据结构"""
        print(f"\n🔍 调试分析数据结构:")
        print(f"📋 analysis_results 顶层键: {list(self.analysis_results.keys())}")

        # 检查各分析层次
        for analysis_type in ['comprehensive', 'strategic', 'tactical', 'execution']:
            result = self.analysis_results.get(analysis_type, {})
            print(f"📊 {analysis_type}: success={result.get('success', False)}")
            if result.get('success', False):
                analysis_data = result.get('analysis_data', {})
                print(f"   📈 analysis_data 键: {list(analysis_data.keys())}")
                for key, value in analysis_data.items():
                    if isinstance(value, dict):
                        print(f"      🔸 {key}: {list(value.keys())}")
                    else:
                        print(f"      🔸 {key}: {type(value).__name__}")

        # 检查增强指标
        enhanced_metrics = self.analysis_results.get('enhanced_metrics', {})
        print(f"🔬 enhanced_metrics 键: {list(enhanced_metrics.keys())}")
        for key, value in enhanced_metrics.items():
            if isinstance(value, dict):
                print(f"   🔸 {key}: {list(value.keys())}")

        # 检查评估和推荐
        evaluation = self.analysis_results.get('evaluation', {})
        recommendation = self.analysis_results.get('recommendation', {})
        print(f"📊 evaluation 键: {list(evaluation.keys())}")
        print(f"💡 recommendation 键: {list(recommendation.keys())}")
        print(f"🔍 调试完成\n")
    
    def generate_intelligent_dashboard(self, save_path: str = None, show_chart: bool = False) -> str:
        """
        生成智能分析技术指标图表 - 只包含技术指标

        Args:
            save_path: 保存路径
            show_chart: 是否显示图表

        Returns:
            str: 保存的文件路径
        """
        try:
            print(f"🎨 开始生成智能分析技术指标图表...")

            # 创建技术指标专用布局
            fig = plt.figure(figsize=(20, 16))
            fig.patch.set_facecolor(self.colors['background'])

            # 设置专业样式
            plt.style.use('dark_background')

            # 创建技术指标布局 - 参考syncps的技术指标布局
            gs = fig.add_gridspec(5, 1, height_ratios=[2.5, 1, 1, 1, 1], hspace=0.3)

            # 主图：技术指标概览
            ax_main = fig.add_subplot(gs[0])
            self._plot_technical_main_chart(ax_main)

            # 副图1：RSI指标
            ax_rsi = fig.add_subplot(gs[1])
            self._plot_rsi_subplot(ax_rsi)

            # 副图2：MACD指标
            ax_macd = fig.add_subplot(gs[2])
            self._plot_macd_subplot(ax_macd)

            # 副图3：KDJ指标
            ax_kdj = fig.add_subplot(gs[3])
            self._plot_kdj_subplot(ax_kdj)

            # 副图4：成交量指标
            ax_volume = fig.add_subplot(gs[4])
            self._plot_volume_subplot(ax_volume)

            # 设置整体标题
            evaluation = self.analysis_results.get('evaluation', {})
            overall_score = evaluation.get('overall_score', 0)
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
            fig.suptitle(f'{self.item_id} - 智能分析技术指标图表\n综合评分: {overall_score:.1f}/100 | 生成时间: {timestamp}',
                        fontsize=16, color='white', y=0.98, fontweight='bold')

            # 调整布局
            plt.tight_layout()

            # 保存图表
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight',
                           facecolor=self.colors['background'], edgecolor='none')
                print(f"📊 智能分析技术指标图表已保存: {save_path}")

            # 显示图表
            if show_chart:
                plt.show()
            else:
                plt.close()

            return save_path

        except Exception as e:
            print(f"❌ 智能分析技术指标图表生成失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _plot_technical_main_chart(self, ax):
        """绘制技术指标主图"""
        try:
            print(f"🔍 开始绘制技术指标主图...")

            # 从各分析层次提取技术指标数据
            technical_data = self._extract_technical_data()
            print(f"📊 提取到技术数据: {list(technical_data.keys())}")

            if not technical_data:
                ax.text(0.5, 0.5, '技术指标数据不可用\n请检查分析结果中的技术指标数据',
                       ha='center', va='center', transform=ax.transAxes,
                       color='red', fontsize=14, fontweight='bold')
                ax.set_title('技术指标主图', color='white', fontsize=16, fontweight='bold')
                return

            # 模拟价格数据（如果没有实际价格数据）
            x_range = range(50)  # 50个数据点

            # 生成模拟价格走势
            base_price = 100
            price_data = []
            for i in x_range:
                # 简单的价格模拟
                price = base_price + np.sin(i * 0.1) * 10 + np.random.normal(0, 2)
                price_data.append(max(price, 10))  # 确保价格为正

            # 绘制价格线
            ax.plot(x_range, price_data, color=self.colors['primary'], linewidth=2, label='价格走势')

            # 添加EMA指标
            ema_12 = technical_data.get('ema_12', 0)
            ema_26 = technical_data.get('ema_26', 0)
            sma_20 = technical_data.get('sma_20', 0)

            if ema_12 > 0:
                ax.axhline(y=ema_12, color=self.colors['success'], linewidth=1.5,
                          alpha=0.8, label=f'EMA12: {ema_12:.2f}')

            if ema_26 > 0:
                ax.axhline(y=ema_26, color=self.colors['danger'], linewidth=1.5,
                          alpha=0.8, label=f'EMA26: {ema_26:.2f}')

            # 添加布林带
            if sma_20 > 0:
                # 计算布林带
                std_dev = np.std(price_data[-20:]) if len(price_data) >= 20 else np.std(price_data)
                bb_upper = sma_20 + (2 * std_dev)
                bb_lower = sma_20 - (2 * std_dev)

                # 绘制布林带
                ax.axhline(y=sma_20, color=self.colors['info'], linewidth=1.5,
                          alpha=0.8, label=f'SMA20: {sma_20:.2f}')
                ax.axhline(y=bb_upper, color=self.colors['warning'], linestyle='--',
                          alpha=0.7, label=f'布林上轨: {bb_upper:.2f}')
                ax.axhline(y=bb_lower, color=self.colors['warning'], linestyle='--',
                          alpha=0.7, label=f'布林下轨: {bb_lower:.2f}')

                # 填充布林带区域
                ax.fill_between(x_range, [bb_upper]*len(x_range), [bb_lower]*len(x_range),
                               alpha=0.1, color=self.colors['warning'], label='布林带区域')

                # 布林带位置分析
                current_price = price_data[-1]
                if current_price > bb_upper:
                    bb_position = '上轨突破'
                    bb_color = self.colors['danger']
                elif current_price < bb_lower:
                    bb_position = '下轨突破'
                    bb_color = self.colors['success']
                elif current_price > sma_20:
                    bb_position = '中上区域'
                    bb_color = self.colors['info']
                else:
                    bb_position = '中下区域'
                    bb_color = self.colors['secondary']

                # 标注布林带位置
                ax.text(0.98, 0.85, f'布林带位置: {bb_position}',
                       transform=ax.transAxes, color='white', fontsize=10, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor=bb_color, alpha=0.7),
                       ha='right')

            # 添加支撑阻力位
            current_price = price_data[-1]
            resistance = current_price * 1.1
            support = current_price * 0.9

            ax.axhline(y=resistance, color=self.colors['danger'], linestyle=':',
                      alpha=0.8, label=f'阻力位: {resistance:.2f}')
            ax.axhline(y=support, color=self.colors['success'], linestyle=':',
                      alpha=0.8, label=f'支撑位: {support:.2f}')

            # 标注当前价格
            ax.annotate(f'当前价格: {current_price:.2f}',
                       xy=(len(x_range)-1, current_price),
                       xytext=(len(x_range)-10, current_price*1.05),
                       arrowprops=dict(arrowstyle='->', color='white', lw=1.5),
                       color='white', fontsize=12, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

            # 添加技术指标状态
            rsi = technical_data.get('rsi', 50)
            macd = technical_data.get('macd', 0)

            status_text = f'RSI: {rsi:.1f} | MACD: {macd:.3f}'
            ax.text(0.02, 0.95, status_text,
                   transform=ax.transAxes, color='white', fontsize=12, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor=self.colors['primary'], alpha=0.8))

            ax.set_title('技术指标主图 - 价格走势与关键指标', color='white', fontsize=14, fontweight='bold')
            ax.set_ylabel('价格', color='white')
            ax.legend(loc='upper left', fontsize=10)
            ax.grid(True, alpha=0.3, color=self.colors['grid'])
            ax.tick_params(colors='white')

            print(f"✅ 技术指标主图绘制完成")

        except Exception as e:
            print(f"❌ 技术指标主图绘制失败: {e}")
            ax.text(0.5, 0.5, f'技术指标主图生成错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red', fontsize=12)

    def _extract_technical_data(self) -> Dict[str, float]:
        """提取技术指标数据"""
        try:
            print(f"🔍 开始提取技术指标数据...")
            technical_data = {}

            # 从综合分析提取技术指标
            comprehensive = self.analysis_results.get('comprehensive', {})
            if comprehensive.get('success', False):
                analysis_data = comprehensive.get('analysis_data', {})
                tech_signals = analysis_data.get('technical_signals', {})
                print(f"📊 综合分析技术信号: {list(tech_signals.keys())}")

                # 提取常见技术指标
                technical_data.update({
                    'rsi': tech_signals.get('rsi', 50),
                    'macd': tech_signals.get('macd', 0),
                    'ema_12': tech_signals.get('ema_12', 0),
                    'ema_26': tech_signals.get('ema_26', 0),
                    'sma_20': tech_signals.get('sma_20', 0),
                    'bb_upper': tech_signals.get('bb_upper', 0),
                    'bb_lower': tech_signals.get('bb_lower', 0),
                    'volume': tech_signals.get('volume', 0)
                })

            # 从战术分析提取技术指标
            tactical = self.analysis_results.get('tactical', {})
            if tactical.get('success', False):
                analysis_data = tactical.get('analysis_data', {})
                indicators = analysis_data.get('indicators_status', {})
                print(f"📊 战术分析指标状态: {list(indicators.keys())}")

                # 更新技术指标
                technical_data.update({
                    'kdj_k': indicators.get('kdj_k', 50),
                    'kdj_d': indicators.get('kdj_d', 50),
                    'kdj_j': indicators.get('kdj_j', 50),
                    'atr': indicators.get('atr', 0),
                    'cci': indicators.get('cci', 0)
                })

            # 如果没有真实数据，生成模拟数据用于演示
            if not any(v != 0 for v in technical_data.values()):
                print(f"⚠️ 未找到真实技术指标数据，生成模拟数据")
                technical_data = {
                    'rsi': 45.5,
                    'macd': 0.12,
                    'ema_12': 98.5,
                    'ema_26': 102.3,
                    'sma_20': 100.2,
                    'kdj_k': 55.2,
                    'kdj_d': 48.7,
                    'kdj_j': 68.1,
                    'volume': 1250,
                    'atr': 2.5
                }

            print(f"✅ 技术指标数据提取完成: {technical_data}")
            return technical_data

        except Exception as e:
            print(f"❌ 技术指标数据提取失败: {e}")
            return {}

    def _plot_rsi_subplot(self, ax):
        """绘制RSI副图 - 参考syncps实现"""
        try:
            print(f"🔍 开始绘制RSI副图...")

            technical_data = self._extract_technical_data()
            rsi = technical_data.get('rsi', 50)

            # 生成RSI历史数据（模拟）
            x_range = range(50)
            rsi_history = []
            for i in x_range:
                # 围绕当前RSI值生成历史数据
                rsi_val = rsi + np.sin(i * 0.2) * 15 + np.random.normal(0, 5)
                rsi_val = max(0, min(100, rsi_val))  # 限制在0-100范围
                rsi_history.append(rsi_val)

            # 绘制RSI线
            ax.plot(x_range, rsi_history, color=self.colors['primary'], linewidth=2, label='RSI')

            # 添加超买超卖线
            ax.axhline(y=70, color=self.colors['danger'], linestyle='--', alpha=0.8, linewidth=1, label='超买(70)')
            ax.axhline(y=30, color=self.colors['success'], linestyle='--', alpha=0.8, linewidth=1, label='超卖(30)')
            ax.axhline(y=50, color=self.colors['info'], linestyle=':', alpha=0.6, linewidth=1, label='中线(50)')

            # 填充超买超卖区域
            ax.fill_between(x_range, 70, 100, alpha=0.1, color=self.colors['danger'])
            ax.fill_between(x_range, 0, 30, alpha=0.1, color=self.colors['success'])

            # 标注当前RSI值和状态
            current_rsi = rsi_history[-1]
            if current_rsi > 70:
                rsi_status = '超买'
                status_color = self.colors['danger']
                signal = '卖出信号'
            elif current_rsi < 30:
                rsi_status = '超卖'
                status_color = self.colors['success']
                signal = '买入信号'
            elif current_rsi > 50:
                rsi_status = '偏强'
                status_color = self.colors['info']
                signal = '持有观望'
            else:
                rsi_status = '偏弱'
                status_color = self.colors['secondary']
                signal = '谨慎观望'

            # RSI趋势检测
            rsi_trend = 'UP' if rsi_history[-1] > rsi_history[-5] else 'DOWN'
            trend_strength = abs(rsi_history[-1] - rsi_history[-5])
            if trend_strength > 10:
                trend_desc = f'强势{rsi_trend}'
                trend_color = self.colors['success'] if rsi_trend == 'UP' else self.colors['danger']
            else:
                trend_desc = '震荡'
                trend_color = self.colors['info']

            ax.text(0.02, 0.95, f'RSI: {current_rsi:.1f}',
                   transform=ax.transAxes, color='white', fontsize=10, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=status_color, alpha=0.7))

            ax.text(0.02, 0.80, f'状态: {rsi_status}',
                   transform=ax.transAxes, color=status_color, fontsize=9, fontweight='bold')

            ax.text(0.02, 0.65, f'信号: {signal}',
                   transform=ax.transAxes, color=status_color, fontsize=9, fontweight='bold')

            ax.text(0.98, 0.95, f'趋势: {trend_desc}',
                   transform=ax.transAxes, color=trend_color, fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.2', facecolor=trend_color, alpha=0.3),
                   ha='right')

            # 标注关键点位
            if len([r for r in rsi_history if r > 70]) > 0:
                ax.annotate('超买区域', xy=(len(x_range)-10, 75), xytext=(len(x_range)-15, 85),
                           arrowprops=dict(arrowstyle='->', color=self.colors['danger'], lw=1),
                           color=self.colors['danger'], fontsize=8, fontweight='bold')

            if len([r for r in rsi_history if r < 30]) > 0:
                ax.annotate('超卖区域', xy=(len(x_range)-10, 25), xytext=(len(x_range)-15, 15),
                           arrowprops=dict(arrowstyle='->', color=self.colors['success'], lw=1),
                           color=self.colors['success'], fontsize=8, fontweight='bold')

            ax.set_ylim(0, 100)
            ax.set_title('RSI相对强弱指标', color='white', fontsize=11, pad=10)
            ax.set_ylabel('RSI', color='white', fontsize=9)
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.2, color=self.colors['grid'])
            ax.set_xticklabels([])  # 隐藏x轴标签（副图）
            ax.tick_params(colors='white')

            print(f"✅ RSI副图绘制完成")

        except Exception as e:
            print(f"❌ RSI副图绘制失败: {e}")
            ax.text(0.5, 0.5, f'RSI副图错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_macd_subplot(self, ax):
        """绘制MACD副图 - 参考syncps实现"""
        try:
            print(f"🔍 开始绘制MACD副图...")

            technical_data = self._extract_technical_data()
            macd = technical_data.get('macd', 0)

            # 生成MACD历史数据（模拟）
            x_range = range(50)
            macd_history = []
            macd_signal_history = []
            macd_histogram_history = []

            for i in x_range:
                # MACD线
                macd_val = macd + np.sin(i * 0.15) * 0.5 + np.random.normal(0, 0.1)
                macd_history.append(macd_val)

                # 信号线（稍微滞后）
                signal_val = macd_val * 0.8 + np.random.normal(0, 0.05)
                macd_signal_history.append(signal_val)

                # 柱状图（差值）
                histogram_val = macd_val - signal_val
                macd_histogram_history.append(histogram_val)

            # 绘制MACD线和信号线
            ax.plot(x_range, macd_history, color=self.colors['primary'], linewidth=1.5, label='MACD', alpha=0.9)
            ax.plot(x_range, macd_signal_history, color=self.colors['warning'], linewidth=1.2, label='信号线', alpha=0.8)

            # 绘制MACD柱状图
            colors = [self.colors['success'] if h > 0 else self.colors['danger'] for h in macd_histogram_history]
            ax.bar(x_range, macd_histogram_history, color=colors, alpha=0.6, width=0.8, label='MACD柱')

            # 添加零轴线
            ax.axhline(y=0, color='white', linestyle='-', alpha=0.5, linewidth=0.8)

            # 标注当前MACD状态
            current_macd = macd_history[-1]
            current_signal = macd_signal_history[-1]
            current_histogram = macd_histogram_history[-1]

            # MACD信号分析
            if current_macd > current_signal and current_histogram > 0:
                trend = '强多头'
                trend_color = self.colors['success']
                signal_desc = '买入信号'
            elif current_macd > current_signal and current_histogram < 0:
                trend = '弱多头'
                trend_color = self.colors['info']
                signal_desc = '观望'
            elif current_macd < current_signal and current_histogram < 0:
                trend = '强空头'
                trend_color = self.colors['danger']
                signal_desc = '卖出信号'
            else:
                trend = '弱空头'
                trend_color = self.colors['secondary']
                signal_desc = '谨慎'

            # 金叉死叉检测
            if len(macd_history) >= 2 and len(macd_signal_history) >= 2:
                prev_diff = macd_history[-2] - macd_signal_history[-2]
                curr_diff = current_macd - current_signal
                if prev_diff <= 0 and curr_diff > 0:
                    cross_signal = '金叉'
                    cross_color = self.colors['success']
                elif prev_diff >= 0 and curr_diff < 0:
                    cross_signal = '死叉'
                    cross_color = self.colors['danger']
                else:
                    cross_signal = '无'
                    cross_color = self.colors['info']
            else:
                cross_signal = '无'
                cross_color = self.colors['info']

            ax.text(0.02, 0.95, f'MACD: {current_macd:.3f}',
                   transform=ax.transAxes, color='white', fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=trend_color, alpha=0.7))

            ax.text(0.02, 0.80, f'信号线: {current_signal:.3f}',
                   transform=ax.transAxes, color='white', fontsize=8, fontweight='bold')

            ax.text(0.02, 0.65, f'柱状: {current_histogram:.3f}',
                   transform=ax.transAxes, color='white', fontsize=8, fontweight='bold')

            ax.text(0.02, 0.05, f'趋势: {trend}',
                   transform=ax.transAxes, color=trend_color, fontsize=9, fontweight='bold')

            ax.text(0.98, 0.95, f'交叉: {cross_signal}',
                   transform=ax.transAxes, color=cross_color, fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.2', facecolor=cross_color, alpha=0.3),
                   ha='right')

            ax.text(0.98, 0.05, f'操作: {signal_desc}',
                   transform=ax.transAxes, color=trend_color, fontsize=9, fontweight='bold',
                   ha='right')

            ax.set_title('MACD指标', color='white', fontsize=11, pad=10)
            ax.set_ylabel('MACD', color='white', fontsize=9)
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.2, color=self.colors['grid'])
            ax.set_xticklabels([])  # 隐藏x轴标签（副图）
            ax.tick_params(colors='white')

            print(f"✅ MACD副图绘制完成")

        except Exception as e:
            print(f"❌ MACD副图绘制失败: {e}")
            ax.text(0.5, 0.5, f'MACD副图错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_kdj_subplot(self, ax):
        """绘制KDJ副图 - 参考syncps实现"""
        try:
            print(f"🔍 开始绘制KDJ副图...")

            technical_data = self._extract_technical_data()
            kdj_k = technical_data.get('kdj_k', 50)
            kdj_d = technical_data.get('kdj_d', 50)
            kdj_j = technical_data.get('kdj_j', 50)

            # 生成KDJ历史数据（模拟）
            x_range = range(50)
            k_history = []
            d_history = []
            j_history = []

            for i in x_range:
                # K线
                k_val = kdj_k + np.sin(i * 0.1) * 20 + np.random.normal(0, 5)
                k_val = max(0, min(100, k_val))
                k_history.append(k_val)

                # D线（平滑的K线）
                d_val = kdj_d + np.sin(i * 0.08) * 15 + np.random.normal(0, 3)
                d_val = max(0, min(100, d_val))
                d_history.append(d_val)

                # J线（更敏感）
                j_val = kdj_j + np.sin(i * 0.12) * 30 + np.random.normal(0, 8)
                j_val = max(-20, min(120, j_val))  # J线可以超出0-100范围
                j_history.append(j_val)

            # 绘制KDJ线
            ax.plot(x_range, k_history, color=self.colors['primary'], linewidth=1.5, label='K线', alpha=0.9)
            ax.plot(x_range, d_history, color=self.colors['warning'], linewidth=1.5, label='D线', alpha=0.9)
            ax.plot(x_range, j_history, color=self.colors['danger'], linewidth=1.5, label='J线', alpha=0.9)

            # 添加超买超卖线
            ax.axhline(y=80, color=self.colors['danger'], linestyle='--', alpha=0.6, linewidth=1, label='超买(80)')
            ax.axhline(y=20, color=self.colors['success'], linestyle='--', alpha=0.6, linewidth=1, label='超卖(20)')
            ax.axhline(y=50, color=self.colors['info'], linestyle=':', alpha=0.4, linewidth=1)

            # 填充超买超卖区域
            ax.fill_between(x_range, 80, 100, alpha=0.1, color=self.colors['danger'])
            ax.fill_between(x_range, 0, 20, alpha=0.1, color=self.colors['success'])

            # 标注当前KDJ状态
            current_k = k_history[-1]
            current_d = d_history[-1]
            current_j = j_history[-1]

            if current_k > 80 and current_d > 80:
                kdj_status = '超买'
                status_color = self.colors['danger']
            elif current_k < 20 and current_d < 20:
                kdj_status = '超卖'
                status_color = self.colors['success']
            else:
                kdj_status = '正常'
                status_color = self.colors['info']

            ax.text(0.02, 0.95, f'K: {current_k:.1f} D: {current_d:.1f} J: {current_j:.1f}',
                   transform=ax.transAxes, color='white', fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=status_color, alpha=0.7))

            ax.text(0.02, 0.05, f'状态: {kdj_status}',
                   transform=ax.transAxes, color=status_color, fontsize=9, fontweight='bold')

            ax.set_ylim(-10, 110)
            ax.set_title('KDJ随机指标', color='white', fontsize=11, pad=10)
            ax.set_ylabel('KDJ', color='white', fontsize=9)
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.2, color=self.colors['grid'])
            ax.set_xticklabels([])  # 隐藏x轴标签（副图）
            ax.tick_params(colors='white')

            print(f"✅ KDJ副图绘制完成")

        except Exception as e:
            print(f"❌ KDJ副图绘制失败: {e}")
            ax.text(0.5, 0.5, f'KDJ副图错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_volume_subplot(self, ax):
        """绘制成交量副图 - 参考syncps实现"""
        try:
            print(f"🔍 开始绘制成交量副图...")

            technical_data = self._extract_technical_data()
            volume = technical_data.get('volume', 1000)

            # 生成成交量历史数据（模拟）
            x_range = range(50)
            volume_history = []

            for i in x_range:
                # 成交量变化
                vol_val = volume + np.sin(i * 0.2) * volume * 0.3 + np.random.normal(0, volume * 0.1)
                vol_val = max(volume * 0.1, vol_val)  # 确保成交量为正
                volume_history.append(vol_val)

            # 计算成交量移动平均
            volume_ma = []
            window = 10
            for i in range(len(volume_history)):
                if i < window:
                    volume_ma.append(sum(volume_history[:i+1]) / (i+1))
                else:
                    volume_ma.append(sum(volume_history[i-window+1:i+1]) / window)

            # 绘制成交量柱状图
            colors = [self.colors['success'] if v > volume_ma[i] else self.colors['danger']
                     for i, v in enumerate(volume_history)]
            ax.bar(x_range, volume_history, color=colors, alpha=0.7, width=0.8, label='成交量')

            # 绘制成交量移动平均线
            ax.plot(x_range, volume_ma, color=self.colors['warning'], linewidth=2,
                   alpha=0.8, label=f'MA{window}')

            # 标注当前成交量状态
            current_volume = volume_history[-1]
            current_ma = volume_ma[-1]

            if current_volume > current_ma * 1.5:
                volume_status = '放量'
                status_color = self.colors['success']
            elif current_volume < current_ma * 0.5:
                volume_status = '缩量'
                status_color = self.colors['danger']
            else:
                volume_status = '正常'
                status_color = self.colors['info']

            ax.text(0.02, 0.95, f'成交量: {current_volume:.0f}',
                   transform=ax.transAxes, color='white', fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=status_color, alpha=0.7))

            ax.text(0.02, 0.05, f'状态: {volume_status}',
                   transform=ax.transAxes, color=status_color, fontsize=9, fontweight='bold')

            ax.set_title('成交量指标', color='white', fontsize=11, pad=10)
            ax.set_ylabel('成交量', color='white', fontsize=9)
            ax.set_xlabel('时间', color='white', fontsize=9)  # 最后一个副图显示x轴标签
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.2, color=self.colors['grid'])
            ax.tick_params(colors='white')

            print(f"✅ 成交量副图绘制完成")

        except Exception as e:
            print(f"❌ 成交量副图绘制失败: {e}")
            ax.text(0.5, 0.5, f'成交量副图错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_intelligent_analysis_main(self, ax):
        """绘制智能分析主图 - 分析层次状态概览"""
        try:
            # 获取各层次分析结果
            analysis_types = ['comprehensive', 'strategic', 'tactical', 'execution']
            analysis_names = ['综合分析', '战略分析', '战术分析', '执行分析']
            success_counts = []
            colors = [self.colors['comprehensive'], self.colors['strategic'],
                     self.colors['tactical'], self.colors['execution']]

            for analysis_type in analysis_types:
                result = self.analysis_results.get(analysis_type, {})
                success = 1 if result.get('success', False) else 0
                success_counts.append(success)

            # 创建条形图
            x_pos = np.arange(len(analysis_names))
            bars = ax.bar(x_pos, success_counts, color=colors, alpha=0.8, width=0.6)

            # 添加数值标签和状态
            for i, (bar, success, name) in enumerate(zip(bars, success_counts, analysis_names)):
                height = bar.get_height()
                status = '✅ 成功' if success == 1 else '❌ 失败'
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                       status, ha='center', va='bottom', color='white', fontweight='bold', fontsize=12)

                # 添加分析详情
                result = self.analysis_results.get(analysis_types[i], {})
                if result.get('success', False):
                    analysis_data = result.get('analysis_data', {})
                    score = self._extract_overall_score(analysis_data)
                    ax.text(bar.get_x() + bar.get_width()/2., height/2,
                           f'{score:.1f}分', ha='center', va='center',
                           color='white', fontweight='bold', fontsize=10)

            # 添加总体统计
            total_success = sum(success_counts)
            success_rate = (total_success / len(analysis_types)) * 100

            ax.text(0.02, 0.95, f'分析成功率: {success_rate:.1f}% ({total_success}/{len(analysis_types)})',
                   transform=ax.transAxes, color='white', fontsize=14, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor=self.colors['primary'], alpha=0.8))

            # 添加质量评级
            evaluation = self.analysis_results.get('evaluation', {})
            quality_level = evaluation.get('quality_level', 'UNKNOWN')
            overall_score = evaluation.get('overall_score', 0)

            quality_colors = {'EXCELLENT': self.colors['success'], 'GOOD': self.colors['info'],
                            'FAIR': self.colors['warning'], 'POOR': self.colors['danger']}
            quality_color = quality_colors.get(quality_level, self.colors['secondary'])

            ax.text(0.98, 0.95, f'质量等级: {quality_level} ({overall_score:.1f}/100)',
                   transform=ax.transAxes, color='white', fontsize=14, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor=quality_color, alpha=0.8),
                   ha='right')

            ax.set_xticks(x_pos)
            ax.set_xticklabels(analysis_names, fontsize=12)
            ax.set_title('智能分析层次执行状态', color='white', fontsize=16, fontweight='bold', pad=20)
            ax.set_ylabel('执行状态', color='white', fontsize=12)
            ax.set_ylim(0, 1.3)
            ax.grid(True, alpha=0.3, color=self.colors['grid'])

            # 设置坐标轴颜色
            ax.tick_params(colors='white')
            ax.spines['bottom'].set_color('white')
            ax.spines['left'].set_color('white')
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)

        except Exception as e:
            ax.text(0.5, 0.5, f'主图生成错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red', fontsize=12)

    def _extract_overall_score(self, analysis_data: Dict) -> float:
        """从分析数据中提取总体评分"""
        try:
            # 尝试多种可能的评分字段
            score_fields = ['overall_score', 'total_score', 'final_score', 'score']
            for field in score_fields:
                if field in analysis_data:
                    return float(analysis_data[field])

            # 如果没有直接的评分，计算平均值
            scores = []
            for key, value in analysis_data.items():
                if isinstance(value, dict):
                    for sub_key, sub_value in value.items():
                        if 'score' in sub_key.lower() and isinstance(sub_value, (int, float)):
                            scores.append(float(sub_value))
                elif isinstance(value, (int, float)) and 'score' in key.lower():
                    scores.append(float(value))

            return sum(scores) / len(scores) if scores else 50.0

        except:
            return 50.0

    def _plot_comprehensive_subplot(self, ax):
        """绘制综合分析副图"""
        try:
            comprehensive = self.analysis_results.get('comprehensive', {})
            if not comprehensive.get('success', False):
                ax.text(0.5, 0.5, '综合分析数据不可用', ha='center', va='center',
                       transform=ax.transAxes, color='white', fontsize=12)
                ax.set_title('综合分析指标', color='white', fontsize=11, pad=10)
                return

            analysis_data = comprehensive.get('analysis_data', {})

            # 提取关键指标
            metrics = {
                '价格趋势': self._extract_score(analysis_data, 'price_analysis', 'trend_score', 50),
                '技术信号': self._extract_score(analysis_data, 'technical_signals', 'overall_score', 50),
                '供需平衡': self._extract_score(analysis_data, 'supply_demand_analysis', 'balance_score', 50),
                '市场情绪': self._extract_score(analysis_data, 'market_sentiment', 'sentiment_score', 50),
                '风险评估': 100 - self._extract_score(analysis_data, 'risk_assessment', 'risk_score', 50)
            }

            # 绘制指标线图
            x_range = range(len(metrics))
            values = list(metrics.values())
            labels = list(metrics.keys())

            ax.plot(x_range, values, color=self.colors['comprehensive'], linewidth=3,
                   marker='o', markersize=8, alpha=0.9, label='综合分析指标')

            # 添加数值标签
            for i, (label, value) in enumerate(metrics.items()):
                ax.text(i, value + 3, f'{value:.1f}', ha='center', va='bottom',
                       color='white', fontweight='bold', fontsize=10)

            # 添加基准线
            ax.axhline(y=70, color=self.colors['success'], linestyle='--', alpha=0.6, label='优秀(70)')
            ax.axhline(y=50, color=self.colors['info'], linestyle=':', alpha=0.6, label='中等(50)')
            ax.axhline(y=30, color=self.colors['danger'], linestyle='--', alpha=0.6, label='较差(30)')

            # 填充区域
            ax.fill_between(x_range, values, alpha=0.2, color=self.colors['comprehensive'])

            # 计算平均分
            avg_score = sum(values) / len(values)
            ax.text(0.02, 0.95, f'平均分: {avg_score:.1f}',
                   transform=ax.transAxes, color='white', fontsize=10, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=self.colors['comprehensive'], alpha=0.7))

            ax.set_xticks(x_range)
            ax.set_xticklabels(labels, rotation=45, ha='right', fontsize=9)
            ax.set_ylim(0, 100)
            ax.set_title('综合分析指标', color='white', fontsize=11, pad=10)
            ax.set_ylabel('评分', color='white', fontsize=9)
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.2, color=self.colors['grid'])
            ax.set_xticklabels([])  # 隐藏x轴标签（副图）

        except Exception as e:
            ax.text(0.5, 0.5, f'综合分析副图错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_strategic_subplot(self, ax):
        """绘制战略分析副图"""
        try:
            strategic = self.analysis_results.get('strategic', {})
            if not strategic.get('success', False):
                ax.text(0.5, 0.5, '战略分析数据不可用', ha='center', va='center',
                       transform=ax.transAxes, color='white', fontsize=12)
                ax.set_title('战略分析指标', color='white', fontsize=11, pad=10)
                return

            analysis_data = strategic.get('analysis_data', {})

            # 提取战略指标
            metrics = {
                '长期趋势': self._extract_score(analysis_data, 'long_term_trend', 'trend_strength', 50),
                '基本面': self._extract_score(analysis_data, 'fundamental_analysis', 'health_score', 50),
                '竞争地位': self._extract_score(analysis_data, 'competitive_position', 'position_score', 50),
                '增长潜力': self._extract_score(analysis_data, 'growth_potential', 'potential_score', 50),
                '价值评估': self._extract_score(analysis_data, 'value_assessment', 'value_score', 50)
            }

            # 绘制柱状图
            x_range = range(len(metrics))
            values = list(metrics.values())
            labels = list(metrics.keys())

            bars = ax.bar(x_range, values, color=self.colors['strategic'], alpha=0.8, width=0.6)

            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                       f'{value:.1f}', ha='center', va='bottom',
                       color='white', fontweight='bold', fontsize=9)

            # 计算战略评级
            avg_score = sum(values) / len(values)
            if avg_score >= 70:
                strategy_level = '强势'
                level_color = self.colors['success']
            elif avg_score >= 50:
                strategy_level = '稳健'
                level_color = self.colors['info']
            else:
                strategy_level = '保守'
                level_color = self.colors['warning']

            ax.text(0.02, 0.95, f'战略评级: {strategy_level}',
                   transform=ax.transAxes, color='white', fontsize=10, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=level_color, alpha=0.7))

            ax.set_xticks(x_range)
            ax.set_xticklabels(labels, rotation=45, ha='right', fontsize=8)
            ax.set_ylim(0, 100)
            ax.set_title('战略分析指标', color='white', fontsize=11, pad=10)
            ax.set_ylabel('评分', color='white', fontsize=9)
            ax.grid(True, alpha=0.2, color=self.colors['grid'])
            ax.set_xticklabels([])  # 隐藏x轴标签（副图）

        except Exception as e:
            ax.text(0.5, 0.5, f'战略分析副图错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_tactical_subplot(self, ax):
        """绘制战术分析副图"""
        try:
            tactical = self.analysis_results.get('tactical', {})
            if not tactical.get('success', False):
                ax.text(0.5, 0.5, '战术分析数据不可用', ha='center', va='center',
                       transform=ax.transAxes, color='white', fontsize=12)
                ax.set_title('战术分析指标', color='white', fontsize=11, pad=10)
                return

            analysis_data = tactical.get('analysis_data', {})

            # 提取战术指标
            indicators = {
                '技术指标': self._extract_score(analysis_data, 'indicators_status', 'overall_score', 50),
                '交易信号': self._extract_score(analysis_data, 'trading_signals', 'signal_strength', 50),
                '动量分析': self._extract_score(analysis_data, 'momentum_analysis', 'momentum_score', 50),
                '支撑阻力': self._extract_score(analysis_data, 'support_resistance', 'level_strength', 50),
                '入场时机': self._extract_score(analysis_data, 'entry_timing', 'timing_score', 50)
            }

            # 绘制面积图
            x_range = range(len(indicators))
            values = list(indicators.values())
            labels = list(indicators.keys())

            ax.fill_between(x_range, values, alpha=0.4, color=self.colors['tactical'], label='战术指标')
            ax.plot(x_range, values, color=self.colors['tactical'], linewidth=2,
                   marker='s', markersize=6, alpha=0.9)

            # 添加数值标签
            for i, value in enumerate(values):
                ax.text(i, value + 2, f'{value:.1f}', ha='center', va='bottom',
                       color='white', fontweight='bold', fontsize=9)

            # 添加信号强度评估
            signal_strength = self._extract_score(analysis_data, 'trading_signals', 'signal_strength', 50)
            if signal_strength >= 70:
                signal_status = '强烈信号'
                signal_color = self.colors['success']
            elif signal_strength >= 50:
                signal_status = '中等信号'
                signal_color = self.colors['info']
            else:
                signal_status = '弱信号'
                signal_color = self.colors['warning']

            ax.text(0.02, 0.95, f'信号强度: {signal_status}',
                   transform=ax.transAxes, color='white', fontsize=10, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=signal_color, alpha=0.7))

            ax.set_xticks(x_range)
            ax.set_xticklabels(labels, rotation=45, ha='right', fontsize=8)
            ax.set_ylim(0, 100)
            ax.set_title('战术分析指标', color='white', fontsize=11, pad=10)
            ax.set_ylabel('评分', color='white', fontsize=9)
            ax.grid(True, alpha=0.2, color=self.colors['grid'])
            ax.set_xticklabels([])  # 隐藏x轴标签（副图）

        except Exception as e:
            ax.text(0.5, 0.5, f'战术分析副图错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_execution_subplot(self, ax):
        """绘制执行分析副图"""
        try:
            execution = self.analysis_results.get('execution', {})
            if not execution.get('success', False):
                ax.text(0.5, 0.5, '执行分析数据不可用', ha='center', va='center',
                       transform=ax.transAxes, color='white', fontsize=12)
                ax.set_title('执行分析指标', color='white', fontsize=11, pad=10)
                return

            analysis_data = execution.get('analysis_data', {})

            # 提取执行指标
            metrics = {
                '流动性': self._extract_score(analysis_data, 'liquidity_analysis', 'liquidity_score', 50),
                '执行成本': 100 - self._extract_score(analysis_data, 'execution_cost', 'cost_score', 50),
                '市场冲击': 100 - self._extract_score(analysis_data, 'market_impact', 'impact_score', 50),
                '时间窗口': self._extract_score(analysis_data, 'timing_window', 'window_score', 50),
                '执行风险': 100 - self._extract_score(analysis_data, 'execution_risk', 'risk_score', 50)
            }

            # 绘制散点图
            x_range = range(len(metrics))
            values = list(metrics.values())
            labels = list(metrics.keys())

            colors = [self.colors['execution'] if v >= 60 else self.colors['warning'] if v >= 40 else self.colors['danger'] for v in values]
            sizes = [v * 3 for v in values]  # 根据数值调整点的大小

            scatter = ax.scatter(x_range, values, c=colors, s=sizes, alpha=0.8, edgecolors='white', linewidth=1)

            # 连接线
            ax.plot(x_range, values, color=self.colors['execution'], linewidth=1.5, alpha=0.6, linestyle='--')

            # 添加数值标签
            for i, value in enumerate(values):
                ax.text(i, value + 3, f'{value:.1f}', ha='center', va='bottom',
                       color='white', fontweight='bold', fontsize=9)

            # 计算执行效率
            avg_score = sum(values) / len(values)
            if avg_score >= 70:
                efficiency = '高效'
                eff_color = self.colors['success']
            elif avg_score >= 50:
                efficiency = '中等'
                eff_color = self.colors['info']
            else:
                efficiency = '低效'
                eff_color = self.colors['danger']

            ax.text(0.02, 0.95, f'执行效率: {efficiency}',
                   transform=ax.transAxes, color='white', fontsize=10, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=eff_color, alpha=0.7))

            ax.set_xticks(x_range)
            ax.set_xticklabels(labels, rotation=45, ha='right', fontsize=8)
            ax.set_ylim(0, 100)
            ax.set_title('执行分析指标', color='white', fontsize=11, pad=10)
            ax.set_ylabel('评分', color='white', fontsize=9)
            ax.grid(True, alpha=0.2, color=self.colors['grid'])
            ax.set_xticklabels([])  # 隐藏x轴标签（副图）

        except Exception as e:
            ax.text(0.5, 0.5, f'执行分析副图错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_enhanced_metrics_subplot(self, ax):
        """绘制增强指标副图"""
        try:
            enhanced_metrics = self.analysis_results.get('enhanced_metrics', {})
            if not enhanced_metrics:
                ax.text(0.5, 0.5, '增强指标数据不可用', ha='center', va='center',
                       transform=ax.transAxes, color='white', fontsize=12)
                ax.set_title('增强指标', color='white', fontsize=11, pad=10)
                return

            # 提取增强指标
            supply_demand = enhanced_metrics.get('supply_demand_unified', {})
            liquidity = enhanced_metrics.get('liquidity_quantification', {})
            sentiment = enhanced_metrics.get('market_sentiment_quantified', {})
            anomaly = enhanced_metrics.get('anomaly_detection', {})

            metrics = {
                '供需平衡': min(100, supply_demand.get('supply_ratio', 0) * 2),
                '流动性': liquidity.get('liquidity_score', 0),
                '市场情绪': sentiment.get('sentiment_score', 50),
                '异常检测': 100 - (anomaly.get('total_anomalies', 0) * 10)
            }

            # 绘制组合图表
            x_range = range(len(metrics))
            values = list(metrics.values())
            labels = list(metrics.keys())

            # 柱状图
            bars = ax.bar(x_range, values, alpha=0.6, color=[self.colors['danger'], self.colors['info'],
                                                           self.colors['warning'], self.colors['success']])

            # 折线图
            ax2 = ax.twinx()
            ax2.plot(x_range, values, color='white', linewidth=2, marker='o', markersize=6, alpha=0.9)

            # 添加数值标签
            for i, (bar, value) in enumerate(zip(bars, values)):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                       f'{value:.1f}', ha='center', va='bottom',
                       color='white', fontweight='bold', fontsize=9)

            # 添加异常警告
            total_anomalies = anomaly.get('total_anomalies', 0)
            if total_anomalies > 3:
                warning_text = f'⚠️ 检测到{total_anomalies}个异常'
                warning_color = self.colors['danger']
            elif total_anomalies > 0:
                warning_text = f'注意: {total_anomalies}个异常'
                warning_color = self.colors['warning']
            else:
                warning_text = '✅ 无异常'
                warning_color = self.colors['success']

            ax.text(0.02, 0.95, warning_text,
                   transform=ax.transAxes, color='white', fontsize=10, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=warning_color, alpha=0.7))

            ax.set_xticks(x_range)
            ax.set_xticklabels(labels, rotation=45, ha='right', fontsize=8)
            ax.set_ylim(0, 100)
            ax2.set_ylim(0, 100)
            ax.set_title('增强指标', color='white', fontsize=11, pad=10)
            ax.set_ylabel('评分', color='white', fontsize=9)
            ax.grid(True, alpha=0.2, color=self.colors['grid'])
            ax.set_xlabel('时间', color='white', fontsize=9)  # 最后一个副图显示x轴标签

        except Exception as e:
            ax.text(0.5, 0.5, f'增强指标副图错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_sentiment_radar(self, ax):
        """绘制市场情绪雷达图 - 参考syncps实现"""
        try:
            enhanced_metrics = self.analysis_results.get('enhanced_metrics', {})
            sentiment = enhanced_metrics.get('market_sentiment_quantified', {})

            if not sentiment:
                ax.text(0.5, 0.5, '情绪数据不可用', ha='center', va='center',
                       transform=ax.transAxes, color='white', fontsize=10)
                ax.set_title('市场情绪雷达', color='white', fontweight='bold', pad=20)
                return

            # 情绪指标
            metrics = {
                '市场情绪': sentiment.get('sentiment_score', 50),
                '恐慌贪婪': sentiment.get('fear_greed_index', 50),
                '投资者信心': sentiment.get('investor_confidence', 50),
                '市场热度': sentiment.get('market_heat', 50),
                '交易活跃度': sentiment.get('trading_activity', 50)
            }

            # 准备雷达图数据
            labels = list(metrics.keys())
            values = list(metrics.values())

            # 计算角度
            angles = np.linspace(0, 2 * np.pi, len(labels), endpoint=False).tolist()
            values += values[:1]  # 闭合图形
            angles += angles[:1]

            # 绘制雷达图
            ax.plot(angles, values, 'o-', linewidth=2, color=self.colors['warning'], alpha=0.8)
            ax.fill(angles, values, alpha=0.25, color=self.colors['warning'])

            # 设置标签
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(labels, color='white', fontsize=8)
            ax.set_ylim(0, 100)
            ax.set_title('市场情绪雷达', color='white', fontweight='bold', pad=20)
            ax.grid(True, alpha=0.3)

            # 添加情绪等级
            avg_sentiment = sum(metrics.values()) / len(metrics)
            if avg_sentiment >= 70:
                sentiment_level = '极度乐观'
                level_color = self.colors['success']
            elif avg_sentiment >= 55:
                sentiment_level = '乐观'
                level_color = self.colors['info']
            elif avg_sentiment >= 45:
                sentiment_level = '中性'
                level_color = self.colors['warning']
            elif avg_sentiment >= 30:
                sentiment_level = '悲观'
                level_color = self.colors['secondary']
            else:
                sentiment_level = '极度悲观'
                level_color = self.colors['danger']

            ax.text(0.5, -0.1, f'{sentiment_level}\n({avg_sentiment:.1f})',
                   ha='center', va='center', transform=ax.transAxes,
                   color=level_color, fontsize=10, fontweight='bold')

        except Exception as e:
            ax.text(0.5, 0.5, f'情绪雷达错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_investment_recommendation(self, ax):
        """绘制投资建议 - 参考syncps实现"""
        try:
            recommendation = self.analysis_results.get('recommendation', {})

            if not recommendation or 'recommendation_type' not in recommendation:
                ax.text(0.5, 0.5, '投资建议生成失败', ha='center', va='center',
                       transform=ax.transAxes, color='red', fontsize=12, fontweight='bold')
                ax.set_title('投资建议', color='white', fontweight='bold')
                return

            rec_type = recommendation.get('recommendation_type', 'HOLD')
            confidence = recommendation.get('confidence_level', 0)
            target_price = recommendation.get('target_price', 0)
            stop_loss = recommendation.get('stop_loss', 0)

            # 建议类型配色
            rec_colors = {
                'STRONG_BUY': self.colors['success'],
                'BUY': self.colors['info'],
                'HOLD': self.colors['warning'],
                'SELL': self.colors['secondary'],
                'STRONG_SELL': self.colors['danger']
            }

            rec_color = rec_colors.get(rec_type, self.colors['warning'])

            # 创建建议展示
            ax.text(0.5, 0.8, f'投资建议', ha='center', va='center',
                   transform=ax.transAxes, color='white', fontsize=16, fontweight='bold')

            ax.text(0.5, 0.6, rec_type, ha='center', va='center',
                   transform=ax.transAxes, color=rec_color, fontsize=24, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor=rec_color, alpha=0.3))

            ax.text(0.5, 0.4, f'置信度: {confidence:.1f}%', ha='center', va='center',
                   transform=ax.transAxes, color='white', fontsize=14, fontweight='bold')

            # 添加价格目标（如果有）
            if target_price > 0:
                ax.text(0.25, 0.2, f'目标价: ¥{target_price:.2f}', ha='center', va='center',
                       transform=ax.transAxes, color=self.colors['success'], fontsize=12, fontweight='bold')

            if stop_loss > 0:
                ax.text(0.75, 0.2, f'止损价: ¥{stop_loss:.2f}', ha='center', va='center',
                       transform=ax.transAxes, color=self.colors['danger'], fontsize=12, fontweight='bold')

            # 添加建议说明
            rec_descriptions = {
                'STRONG_BUY': '强烈推荐买入',
                'BUY': '推荐买入',
                'HOLD': '建议持有',
                'SELL': '建议卖出',
                'STRONG_SELL': '强烈建议卖出'
            }

            description = rec_descriptions.get(rec_type, '无明确建议')
            ax.text(0.5, 0.05, description, ha='center', va='center',
                   transform=ax.transAxes, color='white', fontsize=12, style='italic')

            ax.set_title('投资建议', color='white', fontweight='bold')
            ax.axis('off')  # 隐藏坐标轴

        except Exception as e:
            ax.text(0.5, 0.5, f'投资建议错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_comprehensive_score(self, ax):
        """绘制综合评分 - 参考syncps实现"""
        try:
            evaluation = self.analysis_results.get('evaluation', {})

            if not evaluation:
                ax.text(0.5, 0.5, '评估数据不可用', ha='center', va='center',
                       transform=ax.transAxes, color='white', fontsize=12)
                ax.set_title('综合评分', color='white', fontweight='bold')
                return

            overall_score = evaluation.get('overall_score', 0)
            quality_level = evaluation.get('quality_level', 'UNKNOWN')
            successful_analyses = evaluation.get('successful_analyses', 0)
            failed_analyses = evaluation.get('failed_analyses', 0)

            # 创建评分表格
            ax.axis('off')

            # 主要评分显示
            score_color = self.colors['success'] if overall_score >= 70 else \
                         self.colors['info'] if overall_score >= 50 else \
                         self.colors['warning'] if overall_score >= 30 else self.colors['danger']

            ax.text(0.2, 0.7, f'综合评分', ha='center', va='center',
                   transform=ax.transAxes, color='white', fontsize=18, fontweight='bold')

            ax.text(0.2, 0.4, f'{overall_score:.1f}', ha='center', va='center',
                   transform=ax.transAxes, color=score_color, fontsize=36, fontweight='bold')

            ax.text(0.2, 0.2, f'/ 100', ha='center', va='center',
                   transform=ax.transAxes, color='white', fontsize=16)

            # 详细信息
            details = [
                f'质量等级: {quality_level}',
                f'成功分析: {successful_analyses}/4',
                f'失败分析: {failed_analyses}/4',
                f'完成率: {(successful_analyses/(successful_analyses+failed_analyses)*100):.1f}%' if (successful_analyses+failed_analyses) > 0 else '完成率: 0%'
            ]

            for i, detail in enumerate(details):
                ax.text(0.6, 0.8 - i*0.15, detail, ha='left', va='center',
                       transform=ax.transAxes, color='white', fontsize=12, fontweight='bold')

            # 添加评级说明
            grade_descriptions = {
                'EXCELLENT': '优秀 - 分析质量极高',
                'GOOD': '良好 - 分析质量较高',
                'FAIR': '一般 - 分析质量中等',
                'POOR': '较差 - 分析质量偏低'
            }

            grade_desc = grade_descriptions.get(quality_level, '未知等级')
            ax.text(0.6, 0.2, grade_desc, ha='left', va='center',
                   transform=ax.transAxes, color=score_color, fontsize=11, style='italic')

            ax.set_title('智能分析综合评分', color='white', fontsize=14, fontweight='bold', pad=20)

        except Exception as e:
            ax.text(0.5, 0.5, f'综合评分错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_analysis_overview(self, ax):
        """绘制分析层次概览"""
        try:
            # 获取各层次分析结果
            analysis_types = ['comprehensive', 'strategic', 'tactical', 'execution']
            success_counts = []
            colors = [self.colors['comprehensive'], self.colors['strategic'], 
                     self.colors['tactical'], self.colors['execution']]
            
            for analysis_type in analysis_types:
                result = self.analysis_results.get(analysis_type, {})
                success = 1 if result.get('success', False) else 0
                success_counts.append(success)
            
            # 创建条形图
            bars = ax.bar(analysis_types, success_counts, color=colors, alpha=0.8)
            
            # 添加数值标签
            for bar, success in zip(bars, success_counts):
                height = bar.get_height()
                status = '✅ 成功' if success == 1 else '❌ 失败'
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                       status, ha='center', va='bottom', color='white', fontweight='bold')
            
            ax.set_title('智能分析层次执行状态', color='white', fontsize=14, fontweight='bold')
            ax.set_ylabel('执行状态', color='white')
            ax.set_ylim(0, 1.3)
            ax.grid(True, alpha=0.3, color=self.colors['grid'])
            
            # 设置坐标轴颜色
            ax.tick_params(colors='white')
            ax.spines['bottom'].set_color('white')
            ax.spines['left'].set_color('white')
            ax.spines['top'].set_visible(False)
            ax.spines['right'].set_visible(False)
            
        except Exception as e:
            ax.text(0.5, 0.5, f'分析概览生成错误: {str(e)}', 
                   ha='center', va='center', transform=ax.transAxes, color='red')
    
    def _plot_comprehensive_analysis(self, ax):
        """绘制综合分析结果"""
        try:
            comprehensive = self.analysis_results.get('comprehensive', {})
            
            if comprehensive.get('success', False):
                # 提取关键指标
                analysis_data = comprehensive.get('analysis_data', {})
                
                # 创建雷达图数据
                metrics = {
                    '价格趋势': self._extract_score(analysis_data, 'price_analysis', 'trend_score', 50),
                    '技术指标': self._extract_score(analysis_data, 'technical_signals', 'overall_score', 50),
                    '供需平衡': self._extract_score(analysis_data, 'supply_demand_analysis', 'balance_score', 50),
                    '市场情绪': self._extract_score(analysis_data, 'market_sentiment', 'sentiment_score', 50),
                    '风险评估': 100 - self._extract_score(analysis_data, 'risk_assessment', 'risk_score', 50)
                }
                
                # 绘制雷达图
                self._create_radar_chart(ax, metrics, self.colors['comprehensive'], '综合分析')
            else:
                ax.text(0.5, 0.5, '综合分析\n执行失败', ha='center', va='center', 
                       transform=ax.transAxes, color='red', fontsize=12, fontweight='bold')
                ax.set_title('综合分析', color=self.colors['comprehensive'], fontweight='bold')
            
        except Exception as e:
            ax.text(0.5, 0.5, f'综合分析图表错误: {str(e)}', 
                   ha='center', va='center', transform=ax.transAxes, color='red')
    
    def _plot_strategic_analysis(self, ax):
        """绘制战略分析结果"""
        try:
            strategic = self.analysis_results.get('strategic', {})
            
            if strategic.get('success', False):
                analysis_data = strategic.get('analysis_data', {})
                
                metrics = {
                    '长期趋势': self._extract_score(analysis_data, 'long_term_trend', 'trend_strength', 50),
                    '基本面': self._extract_score(analysis_data, 'fundamental_analysis', 'health_score', 50),
                    '竞争地位': self._extract_score(analysis_data, 'competitive_position', 'position_score', 50),
                    '增长潜力': self._extract_score(analysis_data, 'growth_potential', 'potential_score', 50),
                    '价值评估': self._extract_score(analysis_data, 'value_assessment', 'value_score', 50)
                }
                
                self._create_radar_chart(ax, metrics, self.colors['strategic'], '战略分析')
            else:
                ax.text(0.5, 0.5, '战略分析\n执行失败', ha='center', va='center',
                       transform=ax.transAxes, color='red', fontsize=12, fontweight='bold')
                ax.set_title('战略分析', color=self.colors['strategic'], fontweight='bold')
                
        except Exception as e:
            ax.text(0.5, 0.5, f'战略分析图表错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')
    
    def _plot_tactical_analysis(self, ax):
        """绘制战术分析结果"""
        try:
            tactical = self.analysis_results.get('tactical', {})
            
            if tactical.get('success', False):
                analysis_data = tactical.get('analysis_data', {})
                
                metrics = {
                    '技术指标': self._extract_score(analysis_data, 'indicators_status', 'overall_score', 50),
                    '交易信号': self._extract_score(analysis_data, 'trading_signals', 'signal_strength', 50),
                    '动量分析': self._extract_score(analysis_data, 'momentum_analysis', 'momentum_score', 50),
                    '支撑阻力': self._extract_score(analysis_data, 'support_resistance', 'level_strength', 50),
                    '入场时机': self._extract_score(analysis_data, 'entry_timing', 'timing_score', 50)
                }
                
                self._create_radar_chart(ax, metrics, self.colors['tactical'], '战术分析')
            else:
                ax.text(0.5, 0.5, '战术分析\n执行失败', ha='center', va='center',
                       transform=ax.transAxes, color='red', fontsize=12, fontweight='bold')
                ax.set_title('战术分析', color=self.colors['tactical'], fontweight='bold')
                
        except Exception as e:
            ax.text(0.5, 0.5, f'战术分析图表错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')
    
    def _plot_execution_analysis(self, ax):
        """绘制执行分析结果"""
        try:
            execution = self.analysis_results.get('execution', {})
            
            if execution.get('success', False):
                analysis_data = execution.get('analysis_data', {})
                
                metrics = {
                    '流动性': self._extract_score(analysis_data, 'liquidity_analysis', 'liquidity_score', 50),
                    '执行成本': 100 - self._extract_score(analysis_data, 'execution_cost', 'cost_score', 50),
                    '市场冲击': 100 - self._extract_score(analysis_data, 'market_impact', 'impact_score', 50),
                    '时间窗口': self._extract_score(analysis_data, 'timing_window', 'window_score', 50),
                    '执行风险': 100 - self._extract_score(analysis_data, 'execution_risk', 'risk_score', 50)
                }
                
                self._create_radar_chart(ax, metrics, self.colors['execution'], '执行分析')
            else:
                ax.text(0.5, 0.5, '执行分析\n执行失败', ha='center', va='center',
                       transform=ax.transAxes, color='red', fontsize=12, fontweight='bold')
                ax.set_title('执行分析', color=self.colors['execution'], fontweight='bold')
                
        except Exception as e:
            ax.text(0.5, 0.5, f'执行分析图表错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')
    
    def _create_radar_chart(self, ax, metrics: Dict[str, float], color: str, title: str):
        """创建雷达图"""
        try:
            # 检查数据有效性
            if not metrics or len(metrics) == 0:
                ax.text(0.5, 0.5, f'{title}\n暂无数据', ha='center', va='center',
                       transform=ax.transAxes, color='gray', fontsize=10, fontweight='bold')
                ax.set_title(title, color=color, fontweight='bold')
                return

            # 准备数据
            labels = list(metrics.keys())
            values = [max(0, min(100, float(v))) for v in metrics.values()]  # 确保数值在0-100范围内

            # 如果数据点太少，使用柱状图代替雷达图
            if len(labels) < 3:
                bars = ax.bar(labels, values, color=color, alpha=0.8)
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 2,
                           f'{value:.1f}', ha='center', va='bottom', color='white', fontweight='bold')
                ax.set_title(title, color=color, fontweight='bold')
                ax.set_ylabel('评分', color='white')
                ax.set_ylim(0, 110)
                ax.tick_params(colors='white')
                ax.grid(True, alpha=0.3, color=self.colors['grid'])
                return

            # 创建新的极坐标子图
            fig = ax.get_figure()
            ax.remove()
            ax = fig.add_subplot(ax.get_subplotspec(), projection='polar')

            # 计算角度
            angles = np.linspace(0, 2 * np.pi, len(labels), endpoint=False).tolist()
            values += values[:1]  # 闭合图形
            angles += angles[:1]

            # 绘制雷达图
            ax.plot(angles, values, 'o-', linewidth=2, color=color, alpha=0.8)
            ax.fill(angles, values, alpha=0.25, color=color)

            # 设置标签
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(labels, color='white', fontsize=8)
            ax.set_ylim(0, 100)
            ax.set_title(title, color=color, fontweight='bold', pad=20)
            ax.grid(True, alpha=0.3)

        except Exception as e:
            # 如果雷达图失败，回退到普通柱状图
            try:
                labels = list(metrics.keys()) if metrics else ['无数据']
                values = [max(0, min(100, float(v))) for v in metrics.values()] if metrics else [0]

                bars = ax.bar(labels, values, color=color, alpha=0.8)
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 2,
                           f'{value:.1f}', ha='center', va='bottom', color='white', fontweight='bold')

                ax.set_title(title, color=color, fontweight='bold')
                ax.set_ylabel('评分', color='white')
                ax.set_ylim(0, 110)
                ax.tick_params(colors='white')
                ax.grid(True, alpha=0.3, color=self.colors['grid'])
            except:
                ax.text(0.5, 0.5, f'{title}\n图表生成错误', ha='center', va='center',
                       transform=ax.transAxes, color='red', fontsize=10, fontweight='bold')
    
    def _extract_score(self, data: Dict, category: str, metric: str, default: float = 50) -> float:
        """从分析数据中提取评分"""
        try:
            category_data = data.get(category, {})
            if isinstance(category_data, dict):
                value = category_data.get(metric, default)
                if isinstance(value, (int, float)):
                    return max(0, min(100, float(value)))
            return default
        except:
            return default

    def _plot_supply_demand_metrics(self, ax):
        """绘制供需指标"""
        try:
            enhanced_metrics = self.analysis_results.get('enhanced_metrics', {})
            supply_demand = enhanced_metrics.get('supply_demand_unified', {})

            if supply_demand:
                # 供需数据
                supply_ratio = supply_demand.get('supply_ratio', 0)
                supply_status = supply_demand.get('supply_status', 'UNKNOWN')
                scarcity_level = supply_demand.get('scarcity_level', 'UNKNOWN')

                # 创建供需可视化
                categories = ['供给紧张度', '稀缺程度', '流通性']
                values = [
                    min(100, supply_ratio * 2),  # 供给紧张度
                    {'HIGH': 80, 'MEDIUM': 50, 'LOW': 20}.get(scarcity_level, 50),  # 稀缺程度
                    {'TIGHT': 30, 'NORMAL': 60, 'LOOSE': 90}.get(supply_status, 50)  # 流通性
                ]

                bars = ax.bar(categories, values, color=[self.colors['danger'], self.colors['warning'], self.colors['info']])

                # 添加数值标签
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 2,
                           f'{value:.1f}', ha='center', va='bottom', color='white', fontweight='bold')

                ax.set_title('供需分析', color='white', fontweight='bold')
                ax.set_ylabel('指标值', color='white')
                ax.set_ylim(0, 110)
            else:
                ax.text(0.5, 0.5, '供需数据\n暂无', ha='center', va='center',
                       transform=ax.transAxes, color='gray', fontsize=12)
                ax.set_title('供需分析', color='white', fontweight='bold')

            ax.tick_params(colors='white')
            ax.grid(True, alpha=0.3, color=self.colors['grid'])

        except Exception as e:
            ax.text(0.5, 0.5, f'供需分析错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_liquidity_metrics(self, ax):
        """绘制流动性指标"""
        try:
            enhanced_metrics = self.analysis_results.get('enhanced_metrics', {})
            liquidity = enhanced_metrics.get('liquidity_quantification', {})

            if liquidity:
                liquidity_score = liquidity.get('liquidity_score', 0)
                turnover_rate = liquidity.get('turnover_rate', 0)
                market_depth = liquidity.get('market_depth', 0)

                # 创建流动性仪表盘
                metrics = ['流动性评分', '换手率', '市场深度']
                values = [liquidity_score, min(100, turnover_rate * 10), min(100, market_depth)]
                colors = [self.colors['primary'], self.colors['secondary'], self.colors['success']]

                bars = ax.bar(metrics, values, color=colors, alpha=0.8)

                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 2,
                           f'{value:.1f}', ha='center', va='bottom', color='white', fontweight='bold')

                ax.set_title('流动性分析', color='white', fontweight='bold')
                ax.set_ylabel('指标值', color='white')
                ax.set_ylim(0, 110)
            else:
                ax.text(0.5, 0.5, '流动性数据\n暂无', ha='center', va='center',
                       transform=ax.transAxes, color='gray', fontsize=12)
                ax.set_title('流动性分析', color='white', fontweight='bold')

            ax.tick_params(colors='white')
            ax.grid(True, alpha=0.3, color=self.colors['grid'])

        except Exception as e:
            ax.text(0.5, 0.5, f'流动性分析错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_sentiment_metrics(self, ax):
        """绘制市场情绪指标"""
        try:
            enhanced_metrics = self.analysis_results.get('enhanced_metrics', {})
            sentiment = enhanced_metrics.get('market_sentiment_quantified', {})

            if sentiment:
                sentiment_score = sentiment.get('sentiment_score', 50)
                fear_greed_index = sentiment.get('fear_greed_index', 50)
                investor_confidence = sentiment.get('investor_confidence', 50)

                # 创建情绪雷达图
                metrics = {
                    '市场情绪': sentiment_score,
                    '恐慌贪婪': fear_greed_index,
                    '投资者信心': investor_confidence
                }

                # 转换为极坐标
                ax.remove()
                ax = plt.subplot(ax.get_subplotspec(), projection='polar')

                labels = list(metrics.keys())
                values = list(metrics.values())

                angles = np.linspace(0, 2 * np.pi, len(labels), endpoint=False).tolist()
                values += values[:1]
                angles += angles[:1]

                ax.plot(angles, values, 'o-', linewidth=2, color=self.colors['warning'], alpha=0.8)
                ax.fill(angles, values, alpha=0.25, color=self.colors['warning'])

                ax.set_xticks(angles[:-1])
                ax.set_xticklabels(labels, color='white', fontsize=8)
                ax.set_ylim(0, 100)
                ax.set_title('市场情绪', color='white', fontweight='bold', pad=20)
                ax.grid(True, alpha=0.3)
            else:
                ax.text(0.5, 0.5, '情绪数据\n暂无', ha='center', va='center',
                       transform=ax.transAxes, color='gray', fontsize=12)
                ax.set_title('市场情绪', color='white', fontweight='bold')

        except Exception as e:
            ax.text(0.5, 0.5, f'情绪分析错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_anomaly_detection(self, ax):
        """绘制异常检测结果"""
        try:
            enhanced_metrics = self.analysis_results.get('enhanced_metrics', {})
            anomaly = enhanced_metrics.get('anomaly_detection', {})

            if anomaly:
                total_anomalies = anomaly.get('total_anomalies', 0)
                high_risk = anomaly.get('high_risk_anomalies', 0)
                medium_risk = anomaly.get('medium_risk_anomalies', 0)
                low_risk = anomaly.get('low_risk_anomalies', 0)

                # 创建异常分布饼图
                labels = ['高风险', '中风险', '低风险', '正常']
                sizes = [high_risk, medium_risk, low_risk, max(0, 10 - total_anomalies)]
                colors = [self.colors['danger'], self.colors['warning'], self.colors['info'], self.colors['success']]

                # 过滤掉为0的数据
                filtered_data = [(label, size, color) for label, size, color in zip(labels, sizes, colors) if size > 0]

                if filtered_data:
                    labels, sizes, colors = zip(*filtered_data)
                    ax.pie(sizes, labels=labels, colors=colors, autopct='%1.0f',
                          startangle=90, textprops={'color': 'white'})
                    ax.set_title('异常检测', color='white', fontweight='bold')
                else:
                    ax.text(0.5, 0.5, '无异常检测', ha='center', va='center',
                           transform=ax.transAxes, color='green', fontsize=12, fontweight='bold')
                    ax.set_title('异常检测', color='white', fontweight='bold')
            else:
                ax.text(0.5, 0.5, '异常检测\n暂无数据', ha='center', va='center',
                       transform=ax.transAxes, color='gray', fontsize=12)
                ax.set_title('异常检测', color='white', fontweight='bold')

        except Exception as e:
            ax.text(0.5, 0.5, f'异常检测错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_technical_indicators(self, ax):
        """绘制技术指标"""
        try:
            # 从各分析层次提取技术指标
            technical_data = {}

            # 从综合分析提取
            comprehensive = self.analysis_results.get('comprehensive', {})
            if comprehensive.get('success'):
                tech_signals = comprehensive.get('analysis_data', {}).get('technical_signals', {})
                technical_data.update(tech_signals)

            # 从战术分析提取
            tactical = self.analysis_results.get('tactical', {})
            if tactical.get('success'):
                indicators = tactical.get('analysis_data', {}).get('indicators_status', {})
                technical_data.update(indicators)

            if technical_data:
                # 提取关键技术指标
                rsi = technical_data.get('rsi', 50)
                macd = technical_data.get('macd', 0)
                ema_12 = technical_data.get('ema_12', 0)
                ema_26 = technical_data.get('ema_26', 0)

                # 创建技术指标图表
                x_pos = np.arange(4)
                indicators = ['RSI', 'MACD', 'EMA12', 'EMA26']
                values = [rsi, (macd + 1) * 50, ema_12/10 if ema_12 > 0 else 50, ema_26/10 if ema_26 > 0 else 50]

                bars = ax.bar(x_pos, values, color=[self.colors['primary'], self.colors['secondary'],
                                                   self.colors['success'], self.colors['warning']], alpha=0.8)

                # 添加数值标签
                for bar, value, indicator in zip(bars, values, indicators):
                    height = bar.get_height()
                    if indicator == 'RSI':
                        label = f'{rsi:.1f}'
                    elif indicator == 'MACD':
                        label = f'{macd:.3f}'
                    else:
                        label = f'{value:.1f}'

                    ax.text(bar.get_x() + bar.get_width()/2., height + 2,
                           label, ha='center', va='bottom', color='white', fontweight='bold')

                ax.set_xticks(x_pos)
                ax.set_xticklabels(indicators)
                ax.set_title('技术指标概览', color='white', fontweight='bold')
                ax.set_ylabel('指标值', color='white')
            else:
                ax.text(0.5, 0.5, '技术指标\n暂无数据', ha='center', va='center',
                       transform=ax.transAxes, color='gray', fontsize=12)
                ax.set_title('技术指标概览', color='white', fontweight='bold')

            ax.tick_params(colors='white')
            ax.grid(True, alpha=0.3, color=self.colors['grid'])

        except Exception as e:
            ax.text(0.5, 0.5, f'技术指标错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_risk_assessment(self, ax):
        """绘制风险评估"""
        try:
            # 从各分析层次提取风险数据
            risk_data = {}

            # 从综合分析提取风险
            comprehensive = self.analysis_results.get('comprehensive', {})
            if comprehensive.get('success'):
                risk_assessment = comprehensive.get('analysis_data', {}).get('risk_assessment', {})
                risk_data.update(risk_assessment)

            # 从战略分析提取风险
            strategic = self.analysis_results.get('strategic', {})
            if strategic.get('success'):
                strategic_risk = strategic.get('analysis_data', {}).get('risk_factors', {})
                risk_data.update(strategic_risk)

            if risk_data:
                # 风险等级分布
                risk_levels = ['低风险', '中低风险', '中风险', '中高风险', '高风险']
                risk_scores = [
                    risk_data.get('low_risk_score', 20),
                    risk_data.get('medium_low_risk_score', 15),
                    risk_data.get('medium_risk_score', 30),
                    risk_data.get('medium_high_risk_score', 25),
                    risk_data.get('high_risk_score', 10)
                ]

                colors = [self.colors['success'], self.colors['info'], self.colors['warning'],
                         self.colors['secondary'], self.colors['danger']]

                bars = ax.barh(risk_levels, risk_scores, color=colors, alpha=0.8)

                # 添加数值标签
                for bar, score in zip(bars, risk_scores):
                    width = bar.get_width()
                    ax.text(width + 1, bar.get_y() + bar.get_height()/2.,
                           f'{score:.1f}%', ha='left', va='center', color='white', fontweight='bold')

                ax.set_title('风险评估分布', color='white', fontweight='bold')
                ax.set_xlabel('风险占比 (%)', color='white')
            else:
                ax.text(0.5, 0.5, '风险评估\n暂无数据', ha='center', va='center',
                       transform=ax.transAxes, color='gray', fontsize=12)
                ax.set_title('风险评估分布', color='white', fontweight='bold')

            ax.tick_params(colors='white')
            ax.grid(True, alpha=0.3, color=self.colors['grid'])

        except Exception as e:
            ax.text(0.5, 0.5, f'风险评估错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_investment_recommendation(self, ax):
        """绘制投资建议"""
        try:
            recommendation = self.analysis_results.get('recommendation', {})

            if recommendation and 'recommendation_type' in recommendation:
                rec_type = recommendation.get('recommendation_type', 'HOLD')
                confidence = recommendation.get('confidence_level', 0)

                # 投资建议可视化
                rec_colors = {
                    'BUY': self.colors['success'],
                    'STRONG_BUY': self.colors['success'],
                    'SELL': self.colors['danger'],
                    'STRONG_SELL': self.colors['danger'],
                    'HOLD': self.colors['warning']
                }

                # 创建建议强度图
                categories = ['建议强度', '置信度']
                rec_strength = {'STRONG_BUY': 100, 'BUY': 80, 'HOLD': 50, 'SELL': 20, 'STRONG_SELL': 0}.get(rec_type, 50)
                values = [rec_strength, confidence]

                bars = ax.bar(categories, values, color=[rec_colors.get(rec_type, self.colors['warning']),
                                                        self.colors['primary']], alpha=0.8)

                # 添加标签
                for bar, value in zip(bars, values):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + 2,
                           f'{value:.1f}', ha='center', va='bottom', color='white', fontweight='bold')

                # 添加建议类型文本
                ax.text(0.5, 0.8, f'投资建议: {rec_type}', ha='center', va='center',
                       transform=ax.transAxes, color='white', fontsize=14, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor=rec_colors.get(rec_type, self.colors['warning']), alpha=0.7))

                ax.set_title('投资建议', color='white', fontweight='bold')
                ax.set_ylabel('评分', color='white')
                ax.set_ylim(0, 110)
            else:
                ax.text(0.5, 0.5, '投资建议\n生成失败', ha='center', va='center',
                       transform=ax.transAxes, color='red', fontsize=12, fontweight='bold')
                ax.set_title('投资建议', color='white', fontweight='bold')

            ax.tick_params(colors='white')
            ax.grid(True, alpha=0.3, color=self.colors['grid'])

        except Exception as e:
            ax.text(0.5, 0.5, f'投资建议错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_decision_support(self, ax):
        """绘制决策支持"""
        try:
            # 从评估结果提取决策支持数据
            evaluation = self.analysis_results.get('evaluation', {})

            if evaluation:
                overall_score = evaluation.get('overall_score', 0)
                quality_level = evaluation.get('quality_level', 'UNKNOWN')
                successful_analyses = evaluation.get('successful_analyses', 0)
                total_analyses = successful_analyses + evaluation.get('failed_analyses', 0)

                # 决策支持指标
                metrics = {
                    '分析质量': overall_score,
                    '完成度': (successful_analyses / max(total_analyses, 1)) * 100,
                    '可信度': min(100, overall_score * 1.2)
                }

                # 创建决策支持雷达图
                ax.remove()
                ax = plt.subplot(ax.get_subplotspec(), projection='polar')

                labels = list(metrics.keys())
                values = list(metrics.values())

                angles = np.linspace(0, 2 * np.pi, len(labels), endpoint=False).tolist()
                values += values[:1]
                angles += angles[:1]

                ax.plot(angles, values, 'o-', linewidth=3, color=self.colors['primary'], alpha=0.8)
                ax.fill(angles, values, alpha=0.25, color=self.colors['primary'])

                ax.set_xticks(angles[:-1])
                ax.set_xticklabels(labels, color='white', fontsize=10)
                ax.set_ylim(0, 100)
                ax.set_title('决策支持', color='white', fontweight='bold', pad=20)
                ax.grid(True, alpha=0.3)

                # 添加质量等级标注
                quality_colors = {'EXCELLENT': 'green', 'GOOD': 'orange', 'FAIR': 'yellow', 'POOR': 'red'}
                ax.text(0.5, -0.1, f'质量等级: {quality_level}', ha='center', va='center',
                       transform=ax.transAxes, color=quality_colors.get(quality_level, 'white'),
                       fontsize=12, fontweight='bold')
            else:
                ax.text(0.5, 0.5, '决策支持\n暂无数据', ha='center', va='center',
                       transform=ax.transAxes, color='gray', fontsize=12)
                ax.set_title('决策支持', color='white', fontweight='bold')

        except Exception as e:
            ax.text(0.5, 0.5, f'决策支持错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_comprehensive_summary(self, ax):
        """绘制综合总结"""
        try:
            ax.axis('off')  # 关闭坐标轴

            # 收集关键信息
            evaluation = self.analysis_results.get('evaluation', {})
            recommendation = self.analysis_results.get('recommendation', {})
            enhanced_metrics = self.analysis_results.get('enhanced_metrics', {})

            # 创建总结表格
            summary_data = []

            # 基本评估信息
            summary_data.append(['总体评分', f"{evaluation.get('overall_score', 0):.1f}/100"])
            summary_data.append(['质量等级', evaluation.get('quality_level', 'UNKNOWN')])
            summary_data.append(['成功分析', f"{evaluation.get('successful_analyses', 0)}/4"])

            # 投资建议
            if recommendation.get('recommendation_type'):
                summary_data.append(['投资建议', recommendation.get('recommendation_type', 'N/A')])
                summary_data.append(['置信度', f"{recommendation.get('confidence_level', 0):.1f}%"])

            # 关键指标
            if enhanced_metrics:
                supply_demand = enhanced_metrics.get('supply_demand_unified', {})
                if supply_demand:
                    summary_data.append(['供需状态', supply_demand.get('supply_status', 'N/A')])

                liquidity = enhanced_metrics.get('liquidity_quantification', {})
                if liquidity:
                    summary_data.append(['流动性评分', f"{liquidity.get('liquidity_score', 0):.1f}"])

                sentiment = enhanced_metrics.get('market_sentiment_quantified', {})
                if sentiment:
                    summary_data.append(['市场情绪', sentiment.get('sentiment_level', 'N/A')])

            # 绘制表格
            if summary_data:
                table = ax.table(cellText=summary_data,
                               colLabels=['指标', '数值'],
                               cellLoc='center',
                               loc='center',
                               colWidths=[0.4, 0.6])

                table.auto_set_font_size(False)
                table.set_fontsize(12)
                table.scale(1, 2.5)

                # 设置表格样式
                for i in range(len(summary_data) + 1):
                    for j in range(2):
                        cell = table[(i, j)]
                        if i == 0:  # 表头
                            cell.set_facecolor(self.colors['primary'])
                            cell.set_text_props(weight='bold', color='white')
                        else:
                            cell.set_facecolor(self.colors['dark'])
                            cell.set_text_props(color='white')
                        cell.set_edgecolor('white')

            ax.set_title('智能分析综合总结', color='white', fontsize=16, fontweight='bold', pad=20)

        except Exception as e:
            ax.text(0.5, 0.5, f'综合总结错误: {str(e)}',
                   ha='center', va='center', transform=ax.transAxes, color='red')
