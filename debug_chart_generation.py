#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表生成诊断脚本

用于诊断和修复图表生成问题
"""

import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False


def test_basic_matplotlib():
    """测试基本matplotlib功能"""
    print("🧪 测试基本matplotlib功能...")
    
    try:
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 简单的柱状图
        categories = ['测试1', '测试2', '测试3', '测试4']
        values = [75, 82, 68, 90]
        colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D']
        
        bars = ax.bar(categories, values, color=colors, alpha=0.8)
        
        # 添加数值标签
        for bar, value in zip(bars, values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{value}', ha='center', va='bottom', fontweight='bold')
        
        ax.set_title('基本matplotlib测试', fontsize=14, fontweight='bold')
        ax.set_ylabel('数值')
        ax.set_ylim(0, 100)
        ax.grid(True, alpha=0.3)
        
        # 保存测试图表
        output_dir = project_root / "data" / "analysis_results" / "debug"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        test_path = output_dir / "basic_matplotlib_test.png"
        plt.savefig(test_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 基本matplotlib测试成功: {test_path}")
        return True
        
    except Exception as e:
        print(f"❌ 基本matplotlib测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_radar_chart():
    """测试雷达图功能"""
    print("\n🧪 测试雷达图功能...")
    
    try:
        # 创建雷达图数据
        metrics = {
            '价格趋势': 75,
            '技术指标': 68,
            '供需平衡': 82,
            '市场情绪': 58,
            '风险评估': 65
        }
        
        # 准备数据
        labels = list(metrics.keys())
        values = list(metrics.values())
        
        # 计算角度
        angles = np.linspace(0, 2 * np.pi, len(labels), endpoint=False).tolist()
        values += values[:1]  # 闭合图形
        angles += angles[:1]
        
        # 创建极坐标图
        fig, ax = plt.subplots(figsize=(8, 8), subplot_kw=dict(projection='polar'))
        
        # 绘制雷达图
        ax.plot(angles, values, 'o-', linewidth=2, color='#2E86AB', alpha=0.8)
        ax.fill(angles, values, alpha=0.25, color='#2E86AB')
        
        # 设置标签
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(labels, fontsize=10)
        ax.set_ylim(0, 100)
        ax.set_title('雷达图测试', fontsize=14, fontweight='bold', pad=20)
        ax.grid(True, alpha=0.3)
        
        # 保存测试图表
        output_dir = project_root / "data" / "analysis_results" / "debug"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        test_path = output_dir / "radar_chart_test.png"
        plt.savefig(test_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 雷达图测试成功: {test_path}")
        return True
        
    except Exception as e:
        print(f"❌ 雷达图测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_complex_layout():
    """测试复杂布局"""
    print("\n🧪 测试复杂布局...")
    
    try:
        # 创建复杂布局
        fig = plt.figure(figsize=(16, 12))
        
        # 创建网格布局
        gs = fig.add_gridspec(3, 3, hspace=0.4, wspace=0.3)
        
        # 第一行：概览图 (占用整行)
        ax1 = fig.add_subplot(gs[0, :])
        categories = ['综合分析', '战略分析', '战术分析', '执行分析']
        success_counts = [1, 1, 1, 0]
        colors = ['#FFCA3A', '#6A4C93', '#1982C4', '#8AC926']
        
        bars = ax1.bar(categories, success_counts, color=colors, alpha=0.8)
        ax1.set_title('分析层次执行状态', fontsize=14, fontweight='bold')
        ax1.set_ylabel('执行状态')
        ax1.set_ylim(0, 1.3)
        
        # 第二行：三个子图
        # 供需分析
        ax2 = fig.add_subplot(gs[1, 0])
        supply_categories = ['供给紧张度', '稀缺程度', '流通性']
        supply_values = [75, 80, 60]
        ax2.bar(supply_categories, supply_values, color=['#C73E1D', '#F18F01', '#17a2b8'])
        ax2.set_title('供需分析', fontweight='bold')
        ax2.set_ylim(0, 100)
        
        # 流动性分析
        ax3 = fig.add_subplot(gs[1, 1])
        liquidity_metrics = ['流动性评分', '换手率', '市场深度']
        liquidity_values = [72, 85, 65]
        ax3.bar(liquidity_metrics, liquidity_values, color=['#2E86AB', '#A23B72', '#2ca02c'])
        ax3.set_title('流动性分析', fontweight='bold')
        ax3.set_ylim(0, 100)
        
        # 异常检测饼图
        ax4 = fig.add_subplot(gs[1, 2])
        anomaly_labels = ['高风险', '中风险', '低风险', '正常']
        anomaly_sizes = [0, 1, 1, 8]
        anomaly_colors = ['#C73E1D', '#F18F01', '#17a2b8', '#2ca02c']
        # 过滤掉为0的数据
        filtered_data = [(label, size, color) for label, size, color in zip(anomaly_labels, anomaly_sizes, anomaly_colors) if size > 0]
        if filtered_data:
            labels, sizes, colors = zip(*filtered_data)
            ax4.pie(sizes, labels=labels, colors=colors, autopct='%1.0f', startangle=90)
        ax4.set_title('异常检测', fontweight='bold')
        
        # 第三行：技术指标 (占用整行)
        ax5 = fig.add_subplot(gs[2, :])
        tech_indicators = ['RSI', 'MACD', 'EMA12', 'EMA26']
        tech_values = [45, 60, 75, 70]
        tech_colors = ['#2E86AB', '#A23B72', '#2ca02c', '#F18F01']
        ax5.bar(tech_indicators, tech_values, color=tech_colors, alpha=0.8)
        ax5.set_title('技术指标概览', fontweight='bold')
        ax5.set_ylabel('指标值')
        ax5.set_ylim(0, 100)
        
        # 设置整体标题
        fig.suptitle('复杂布局测试 - 智能分析仪表板', fontsize=16, fontweight='bold')
        
        # 保存测试图表
        output_dir = project_root / "data" / "analysis_results" / "debug"
        output_dir.mkdir(parents=True, exist_ok=True)
        
        test_path = output_dir / "complex_layout_test.png"
        plt.savefig(test_path, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 复杂布局测试成功: {test_path}")
        return True
        
    except Exception as e:
        print(f"❌ 复杂布局测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_extraction():
    """测试数据提取逻辑"""
    print("\n🧪 测试数据提取逻辑...")
    
    try:
        # 模拟分析结果数据
        mock_analysis_results = {
            'status': 'success',
            'item_id': '25698',
            'comprehensive': {
                'success': True,
                'analysis_data': {
                    'price_analysis': {'trend_score': 75},
                    'technical_signals': {'overall_score': 68, 'rsi': 45, 'macd': 0.12},
                    'supply_demand_analysis': {'balance_score': 82},
                    'market_sentiment': {'sentiment_score': 58},
                    'risk_assessment': {'risk_score': 35}
                }
            },
            'evaluation': {
                'overall_score': 78,
                'quality_level': 'GOOD',
                'successful_analyses': 4,
                'failed_analyses': 0
            },
            'enhanced_metrics': {
                'supply_demand_unified': {
                    'supply_ratio': 15.5,
                    'supply_status': 'TIGHT',
                    'scarcity_level': 'HIGH'
                },
                'liquidity_quantification': {
                    'liquidity_score': 72.3,
                    'turnover_rate': 8.5,
                    'market_depth': 65
                }
            }
        }
        
        # 测试数据提取函数
        def extract_score(data, category, metric, default=50):
            try:
                category_data = data.get(category, {})
                if isinstance(category_data, dict):
                    value = category_data.get(metric, default)
                    if isinstance(value, (int, float)):
                        return max(0, min(100, float(value)))
                return default
            except:
                return default
        
        # 测试提取各种数据
        comprehensive = mock_analysis_results.get('comprehensive', {})
        if comprehensive.get('success', False):
            analysis_data = comprehensive.get('analysis_data', {})
            
            price_score = extract_score(analysis_data, 'price_analysis', 'trend_score', 50)
            tech_score = extract_score(analysis_data, 'technical_signals', 'overall_score', 50)
            supply_score = extract_score(analysis_data, 'supply_demand_analysis', 'balance_score', 50)
            
            print(f"   价格趋势评分: {price_score}")
            print(f"   技术指标评分: {tech_score}")
            print(f"   供需平衡评分: {supply_score}")
        
        # 测试增强指标提取
        enhanced_metrics = mock_analysis_results.get('enhanced_metrics', {})
        supply_demand = enhanced_metrics.get('supply_demand_unified', {})
        if supply_demand:
            supply_ratio = supply_demand.get('supply_ratio', 0)
            supply_status = supply_demand.get('supply_status', 'UNKNOWN')
            print(f"   供给比例: {supply_ratio}")
            print(f"   供给状态: {supply_status}")
        
        print(f"✅ 数据提取逻辑测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据提取逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🔧 图表生成诊断工具")
    print("=" * 50)
    
    tests = [
        ("基本matplotlib功能", test_basic_matplotlib),
        ("雷达图功能", test_radar_chart),
        ("复杂布局", test_complex_layout),
        ("数据提取逻辑", test_data_extraction)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        success = test_func()
        results.append((test_name, success))
    
    # 总结结果
    print(f"\n" + "=" * 50)
    print(f"📊 诊断结果总结:")
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"   {test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(tests)} 个测试通过")
    
    if success_count == len(tests):
        print(f"🎉 所有测试通过，图表生成基础功能正常!")
    else:
        print(f"⚠️ 部分测试失败，需要进一步调试")
    
    print(f"\n💡 请查看 data/analysis_results/debug/ 目录下的测试图表")


if __name__ == "__main__":
    main()
