#!/usr/bin/env python3
"""
分析任务API路由

提供常规分析和实时监控的API接口
"""

import asyncio
import logging
import uuid
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.api.models.requests import (
    RegularAnalysisRequest, 
    RealtimeAnalysisRequest, 
    BatchAnalysisRequest
)
from src.cs2_investment.api.models.responses import (
    AnalysisTaskResponse, 
    BatchTaskResponse
)
from src.cs2_investment.dao.analysis_log_dao import analysis_log_dao
from src.cs2_investment.api.services.task_engine import task_engine, TaskInfo, TaskType
from src.cs2_investment.fx.integrated_analysis_system import IntegratedAnalysisSystem
from src.cs2_investment.fx.integrated_ssync_system import IntegratedSSyncSystem

# 使用统一日志组件
from src.cs2_investment.utils.logger import get_logger
logger = get_logger(__name__)

# 创建路由器
router = APIRouter(prefix="/api/analysis", tags=["分析任务"])

# 初始化分析系统
try:
    regular_analysis_system = IntegratedAnalysisSystem()
    realtime_analysis_system = IntegratedSSyncSystem()
    logger.info("✅ 分析系统初始化成功")
except Exception as e:
    logger.error(f"❌ 分析系统初始化失败: {e}")
    regular_analysis_system = None
    realtime_analysis_system = None


def generate_task_id() -> str:
    """生成任务ID"""
    return f"task_{uuid.uuid4()}"


def generate_batch_id() -> str:
    """生成批量任务ID"""
    return f"batch_{uuid.uuid4()}"


# 旧的任务执行函数已被TaskEngine替代


@router.post("/regular",
             response_model=AnalysisTaskResponse,
             summary="启动常规分析",
             description="对指定饰品进行完整的投资分析")
async def start_regular_analysis(
    request: RegularAnalysisRequest
):
    """
    启动常规分析任务
    
    - **item_id**: 饰品ID
    - **item_name**: 饰品名称
    - **item_url**: 饰品URL
    - **analysis_options**: 分析选项（可选）
    """
    try:
        # 生成任务ID
        task_id = generate_task_id()
        
        # 创建分析日志记录
        log = analysis_log_dao.create_analysis_log(
            item_id=request.item_id,
            item_name=request.item_name,
            analysis_type='regular',
            scheduled_date=datetime.now()
        )

        # 使用日志记录的ID作为任务ID
        actual_task_id = str(log['id'])

        # 保存必要的信息，避免会话问题
        log_id = log['id']
        
        # 创建任务信息并提交到任务引擎
        task_info = TaskInfo(
            task_id=log_id,
            item_id=request.item_id,
            item_name=request.item_name,
            item_url=request.item_url,
            analysis_type=TaskType.REGULAR
        )

        # 提交任务到执行引擎
        await task_engine.submit_task(task_info)
        
        logger.info(f"📊 常规分析任务已创建: {actual_task_id} - {request.item_name}")
        
        return AnalysisTaskResponse(
            task_id=actual_task_id,
            status="pending",
            message="常规分析任务已创建，正在排队执行",
            created_at=datetime.now(),
            item_id=request.item_id,
            item_name=request.item_name,
            analysis_type="regular"
        )
        
    except Exception as e:
        logger.error(f"创建常规分析任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建分析任务失败: {str(e)}")


@router.post("/realtime",
             response_model=AnalysisTaskResponse,
             summary="启动实时监控",
             description="对指定饰品进行实时监控和技术分析")
async def start_realtime_analysis(
    request: RealtimeAnalysisRequest
):
    """
    启动实时监控分析任务
    
    - **item_id**: 饰品ID
    - **item_name**: 饰品名称
    - **item_url**: 饰品URL
    - **monitor_options**: 监控选项（可选）
    """
    try:
        # 生成任务ID
        task_id = generate_task_id()
        
        # 创建分析日志记录
        log = analysis_log_dao.create_analysis_log(
            item_id=request.item_id,
            item_name=request.item_name,
            analysis_type='realtime',
            scheduled_date=datetime.now()
        )

        # 使用日志记录的ID作为任务ID
        actual_task_id = str(log['id'])

        # 保存必要的信息，避免会话问题
        log_id = log['id']
        
        # 创建任务信息并提交到任务引擎
        task_info = TaskInfo(
            task_id=log_id,
            item_id=request.item_id,
            item_name=request.item_name,
            item_url=request.item_url,
            analysis_type=TaskType.REALTIME
        )

        # 提交任务到执行引擎
        await task_engine.submit_task(task_info)
        
        logger.info(f"📈 实时监控任务已创建: {actual_task_id} - {request.item_name}")
        
        return AnalysisTaskResponse(
            task_id=actual_task_id,
            status="pending",
            message="实时监控任务已创建，正在排队执行",
            created_at=datetime.now(),
            item_id=request.item_id,
            item_name=request.item_name,
            analysis_type="realtime"
        )
        
    except Exception as e:
        logger.error(f"创建实时监控任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建监控任务失败: {str(e)}")


@router.post("/batch",
             response_model=BatchTaskResponse,
             summary="批量分析",
             description="批量处理多个饰品的分析任务")
async def start_batch_analysis(
    request: BatchAnalysisRequest
):
    """
    启动批量分析任务
    
    - **items**: 饰品列表
    - **analysis_type**: 分析类型（regular/realtime）
    - **priority**: 任务优先级（high/normal/low）
    """
    try:
        batch_id = generate_batch_id()
        tasks = []
        
        logger.info(f"📦 开始创建批量分析任务: {batch_id} - {len(request.items)} 个饰品")
        
        for item in request.items:
            # 创建分析日志记录
            log = analysis_log_dao.create_analysis_log(
                item_id=item.item_id,
                item_name=item.item_name,
                analysis_type=request.analysis_type,
                scheduled_date=datetime.now()
            )
            
            actual_task_id = str(log['id'])
            
            # 根据分析类型创建任务信息
            task_type = TaskType.REGULAR if request.analysis_type == "regular" else TaskType.REALTIME

            task_info = TaskInfo(
                task_id=log['id'],
                item_id=item.item_id,
                item_name=item.item_name,
                item_url=item.item_url,
                analysis_type=task_type
            )

            # 提交任务到执行引擎
            await task_engine.submit_task(task_info)
            
            # 添加到任务列表
            tasks.append(AnalysisTaskResponse(
                task_id=actual_task_id,
                status="pending",
                message=f"{request.analysis_type}分析任务已创建",
                created_at=datetime.now(),
                item_id=item.item_id,
                item_name=item.item_name,
                analysis_type=request.analysis_type
            ))
        
        logger.info(f"✅ 批量分析任务创建完成: {batch_id} - {len(tasks)} 个子任务")
        
        return BatchTaskResponse(
            batch_id=batch_id,
            total_items=len(request.items),
            tasks=tasks,
            message=f"批量{request.analysis_type}分析任务已创建",
            created_at=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"创建批量分析任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"创建批量任务失败: {str(e)}")
