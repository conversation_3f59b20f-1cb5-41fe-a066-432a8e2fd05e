"""
分析数据服务

负责从分析结果中提取关键指标并保存到数据库
"""

import json
import os
import numpy as np
from datetime import datetime
from typing import Dict, Any, Optional, List
from pathlib import Path
from loguru import logger

from ..dao.analysis_result_dao import AnalysisResultDAO, RealtimeAnalysisResultDAO
from ..models.analysis_result import AnalysisResult, RealtimeAnalysisResult


def _json_serializer(obj):
    """自定义JSON序列化函数，处理numpy和pandas数据类型"""
    if isinstance(obj, (np.integer, np.int64, np.int32)):
        return int(obj)
    elif isinstance(obj, (np.floating, np.float64, np.float32)):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif hasattr(obj, 'item'):  # pandas scalar types
        return obj.item()
    elif hasattr(obj, 'tolist'):  # pandas Series/DataFrame
        return obj.tolist()
    raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


class AnalysisDataService:
    """分析数据服务"""
    
    def __init__(self):
        self.analysis_dao = AnalysisResultDAO()
        self.realtime_dao = RealtimeAnalysisResultDAO()
        self.logger = logger.bind(service="AnalysisDataService")
    
    def save_regular_analysis_result(self, analysis_result: Dict[str, Any], 
                                   item_name: str, data_path: str, 
                                   chart_path: str = None, report_path: str = None,
                                   analysis_duration: float = None) -> Optional[int]:
        """保存常规分析结果到数据库"""
        try:
            self.logger.info(f"开始保存常规分析结果: {item_name}")
            
            # 提取基础信息
            item_id = self._extract_item_id_from_path(data_path)
            
            # 提取关键指标
            analysis_data = self._extract_regular_analysis_metrics(
                analysis_result, item_id, item_name, data_path, 
                chart_path, report_path, analysis_duration
            )
            
            # 保存到数据库
            db_result = self.analysis_dao.create_analysis_result(analysis_data)
            if not db_result:
                return None
            
            # 保存技术指标
            if 'technical_signals' in analysis_result:
                self._save_technical_indicators(db_result.id, item_id, analysis_result['technical_signals'])
            
            # 保存市场情绪
            if 'market_sentiment' in analysis_result:
                self._save_market_sentiment(db_result.id, item_id, analysis_result['market_sentiment'])
            
            # 保存投资策略
            if 'trading_advice' in analysis_result:
                self._save_investment_strategies(db_result.id, item_id, analysis_result['trading_advice'])
            
            self.logger.info(f"✅ 常规分析结果保存成功: {db_result.id}")
            return db_result.id
            
        except Exception as e:
            self.logger.error(f"❌ 保存常规分析结果失败: {e}")
            return None
    
    def save_realtime_analysis_result(self, analysis_result: Dict[str, Any],
                                    item_name: str, data_path: str = None,
                                    chart_path: str = None, report_path: str = None,
                                    analysis_duration: float = None) -> Optional[int]:
        """保存实时监控分析结果到数据库"""
        try:
            self.logger.info(f"开始保存实时监控结果: {item_name}")

            # 提取基础信息
            item_id = self._extract_item_id_from_path(data_path) if data_path else self._extract_item_id_from_name(item_name)

            # 提取关键指标
            analysis_data = self._extract_realtime_analysis_metrics_v2(
                analysis_result, item_id, item_name, data_path, chart_path, report_path, analysis_duration
            )

            # 保存实时分析结果
            db_result = self.realtime_dao.create_realtime_result(analysis_data)
            if not db_result:
                return None

            self.logger.info(f"✅ 实时监控结果保存成功: {db_result.id}")
            return db_result.id

        except Exception as e:
            self.logger.error(f"❌ 保存实时监控结果失败: {e}")
            return None
    
    def _extract_item_id_from_path(self, data_path: str) -> str:
        """从数据路径中提取饰品ID"""
        try:
            path = Path(data_path)
            return path.name  # 目录名就是item_id
        except Exception:
            return "unknown"
    
    def _extract_item_id_from_name(self, item_name: str) -> str:
        """从饰品名称生成ID（临时方案）"""
        import hashlib
        return hashlib.md5(item_name.encode()).hexdigest()[:16]
    
    def _extract_regular_analysis_metrics(self, analysis_result: Dict[str, Any],
                                        item_id: str, item_name: str, data_path: str,
                                        chart_path: str, report_path: str,
                                        analysis_duration: float) -> Dict[str, Any]:
        """提取常规分析的关键指标 - 简化版本，只提取核心数据"""
        data = {
            'item_id': item_id,
            'item_name': item_name,
            'analysis_version': 'V2.0',
            'data_path': data_path,
            'chart_path': chart_path,
            'report_path': report_path,
            'analysis_duration': analysis_duration
        }

        # 1. 提取当前价格 (优先级: fundamental_snapshot > technical_signals > support_resistance_levels)
        current_price = None
        if 'fundamental_snapshot' in analysis_result:
            current_price = self._safe_float(analysis_result['fundamental_snapshot'].get('current_price'))
        if not current_price and 'technical_signals' in analysis_result:
            current_price = self._safe_float(analysis_result['technical_signals'].get('price'))
        if not current_price and 'support_resistance_levels' in analysis_result:
            current_price = self._safe_float(analysis_result['support_resistance_levels'].get('current_price'))
        data['current_price'] = current_price
        
        # 2. 提取技术指标
        if 'technical_signals' in analysis_result:
            tech_signals = analysis_result['technical_signals']
            data.update({
                'rsi_value': self._safe_float(tech_signals.get('rsi')),
                'macd_value': self._safe_float(tech_signals.get('macd'))
            })

        # KDJ指标在实际分析中不存在，设为NULL
        data.update({
            'kdj_k': None,
            'kdj_d': None,
            'kdj_j': None
        })
        
        # 提取支撑阻力位 - 适配实际分析结果结构
        if 'support_resistance_levels' in analysis_result:
            sr_levels = analysis_result['support_resistance_levels']

            data.update({
                'support_level_1': self._safe_float(sr_levels.get('recent_support')),
                'support_level_2': self._safe_float(sr_levels.get('long_term_support')),
                'resistance_level_1': self._safe_float(sr_levels.get('recent_resistance')),
                'resistance_level_2': self._safe_float(sr_levels.get('long_term_resistance'))
            })
        
        # 4. 提取风险评估
        if 'risk_assessment' in analysis_result:
            risk_data = analysis_result['risk_assessment']
            data.update({
                'risk_level': self._map_risk_level(risk_data.get('overall_risk')),
                'volatility_score': self._safe_float(risk_data.get('comprehensive_score'))
            })

        # 流动性评分在实际分析中不存在，设为NULL
        data['liquidity_score'] = None
        
        # 5. 提取交易建议
        if 'trading_advice' in analysis_result:
            trading_data = analysis_result['trading_advice']
            data.update({
                'trading_signal': self._map_trading_signal(trading_data.get('action')),
                'confidence_level': self._safe_float(trading_data.get('confidence'))
            })

        # 6. 计算投资评分（基于多个指标的综合评分）
        investment_score = self._calculate_investment_score(analysis_result)
        data['investment_score'] = investment_score

        # 7. 提取市场情绪分析数据
        if 'sentiment_summary' in analysis_result:
            sentiment = analysis_result['sentiment_summary']
            data['sentiment_score'] = self._safe_float(sentiment.get('comprehensive_score'))

        if 'market_sentiment' in analysis_result:
            market_sentiment = analysis_result['market_sentiment']
            # 恐慌贪婪指数
            if 'fear_greed_index' in market_sentiment:
                fear_greed = market_sentiment['fear_greed_index']
                data['fear_greed_index'] = self._safe_float(fear_greed.get('fear_greed_score'))

            # 异常恐慌指数
            if 'anomaly_panic_index' in market_sentiment:
                anomaly_panic = market_sentiment['anomaly_panic_index']
                data['anomaly_panic_score'] = self._safe_float(anomaly_panic.get('panic_score'))

        # 8. 提取策略选择数据
        if 'strategy_selection' in analysis_result:
            strategy_selection = analysis_result['strategy_selection']
            if 'optimal_strategy' in strategy_selection:
                optimal_strategy = strategy_selection['optimal_strategy']
                data['optimal_strategy'] = optimal_strategy.get('strategy_name')
                data['strategy_score'] = self._safe_float(optimal_strategy.get('score'))

        # 9. 提取多时间框架分析
        if 'multi_timeframe_analysis' in analysis_result:
            timeframe = analysis_result['multi_timeframe_analysis']
            data.update({
                'daily_trend': timeframe.get('daily_trend'),
                'weekly_trend': timeframe.get('weekly_trend'),
                'trend_consistency': timeframe.get('trend_consistency')
            })

        # 10. 提取基本面数据
        if 'fundamental_snapshot' in analysis_result:
            fundamental = analysis_result['fundamental_snapshot']
            data.update({
                'supply_quantity': self._safe_int(fundamental.get('supply')),
                'bid_premium': self._safe_float(fundamental.get('bid_premium')),
                'supply_ratio': self._safe_float(fundamental.get('supply_ratio'))
            })
        
        # 7. 提取成交量数据
        volume_24h = None
        if 'market_characteristics' in analysis_result:
            market_chars = analysis_result['market_characteristics']
            avg_daily_volume = self._safe_float(market_chars.get('avg_daily_volume'))
            if avg_daily_volume is not None:
                volume_24h = int(avg_daily_volume)  # 转换为整数
        data['volume_24h'] = volume_24h

        # 8. 24小时价格变化（从历史数据计算，如果失败则设为NULL）
        price_change_24h, price_change_percent_24h = self._calculate_price_changes(data_path, current_price)
        data.update({
            'price_change_24h': price_change_24h,
            'price_change_percent_24h': price_change_percent_24h
        })

        # 9. 用户报告内容（如果有的话）
        if 'user_report_content' in analysis_result:
            data['user_report_content'] = analysis_result['user_report_content']

        return data
    
    def _extract_realtime_analysis_metrics(self, analysis_result: Dict[str, Any],
                                         item_id: str, item_name: str, data_path: str,
                                         analysis_duration: float) -> Dict[str, Any]:
        """提取实时监控分析的关键指标"""
        data = {
            'item_id': item_id,
            'item_name': item_name,
            'analysis_type': 'realtime',
            'analysis_version': 'V2.0',
            'data_path': data_path,
            'analysis_duration': analysis_duration
        }
        
        # 提取当前价格和变化
        if 'current_price' in analysis_result:
            data['current_price'] = self._safe_float(analysis_result['current_price'])
        
        if 'price_change' in analysis_result:
            data['price_change_24h'] = self._safe_float(analysis_result['price_change'])
        
        # 提取交易信号
        if 'trading_signals' in analysis_result:
            signals = analysis_result['trading_signals']
            data.update({
                'trading_signal': self._map_trading_signal(signals.get('signal')),
                'confidence_level': self._safe_float(signals.get('confidence'))
            })
        
        # 提取风险评估
        if 'risk_assessment' in analysis_result:
            risk_data = analysis_result['risk_assessment']
            data.update({
                'risk_level': self._map_risk_level(risk_data.get('risk_level')),
                'volatility_score': self._safe_float(risk_data.get('volatility')),
                'risk_score': self._safe_float(risk_data.get('score')),
                'volatility_level': risk_data.get('volatility'),
                'liquidity_level': risk_data.get('liquidity'),
                'stop_loss_price': self._safe_float(risk_data.get('stop_loss')),
                'take_profit_price': self._safe_float(risk_data.get('take_profit'))
            })

        # 提取异常检测数据
        if 'anomaly_results' in analysis_result:
            anomaly = analysis_result['anomaly_results']
            data.update({
                'anomaly_count': self._safe_int(anomaly.get('total_anomalies')),
                'anomaly_frequency': self._safe_float(anomaly.get('anomaly_frequency')),
                'anomaly_severity': anomaly.get('risk_assessment'),
                'anomaly_types': json.dumps(anomaly.get('by_type', {}), ensure_ascii=False, default=_json_serializer)
            })

        # 提取支撑阻力位数据
        if 'support_resistance' in analysis_result:
            sr = analysis_result['support_resistance']
            data.update({
                'support_levels': json.dumps(sr.get('support_levels', []), ensure_ascii=False, default=_json_serializer),
                'resistance_levels': json.dumps(sr.get('resistance_levels', []), ensure_ascii=False, default=_json_serializer)
            })

        # 提取市场情绪数据
        if 'market_sentiment' in analysis_result:
            sentiment = analysis_result['market_sentiment']
            data.update({
                'market_sentiment_score': self._safe_float(sentiment.get('score')),
                'market_sentiment_level': sentiment.get('level')
            })

        # 提取成交量状态
        data['volume_status'] = analysis_result.get('volume_status', 'UNKNOWN')

        # 提取交易信号详情
        if 'trading_signals' in analysis_result:
            signals = analysis_result['trading_signals']
            data.update({
                'trading_signal_reason': signals.get('reason'),
                'signal_confidence': self._safe_float(signals.get('confidence')),
                'price_trend': signals.get('signal'),
                'trend_strength': self._safe_float(signals.get('confidence'))
            })

        # 提取实时报告内容
        if 'realtime_report' in analysis_result:
            data['realtime_report_content'] = str(analysis_result['realtime_report'])

        return data
    
    def _extract_realtime_metrics(self, analysis_result: Dict[str, Any], 
                                analysis_result_id: int, item_id: str) -> Optional[Dict[str, Any]]:
        """提取实时监控指标"""
        try:
            metrics_data = {
                'analysis_result_id': analysis_result_id,
                'item_id': item_id
            }
            
            # 提取异常检测结果
            if 'anomaly_results' in analysis_result:
                anomaly_data = analysis_result['anomaly_results']
                
                # 处理异常统计
                if 'all_anomalies' in anomaly_data:
                    anomalies = anomaly_data['all_anomalies']
                    metrics_data.update({
                        'total_anomalies': len(anomalies),
                        'high_severity_anomalies': len([a for a in anomalies if a.get('severity') == 'HIGH']),
                        'medium_severity_anomalies': len([a for a in anomalies if a.get('severity') == 'MEDIUM']),
                        'low_severity_anomalies': len([a for a in anomalies if a.get('severity') == 'LOW'])
                    })
                
                # 处理异常频率
                if 'anomaly_frequency' in anomaly_data:
                    metrics_data['anomaly_frequency'] = self._safe_float(anomaly_data['anomaly_frequency'])
                
                # 处理风险评估
                if 'risk_assessment' in anomaly_data:
                    metrics_data['pattern_risk_assessment'] = self._map_risk_level(anomaly_data['risk_assessment'])
            
            # 提取成交量状态
            if 'volume_status' in analysis_result:
                metrics_data['volume_status'] = analysis_result['volume_status']
            
            return metrics_data if any(v is not None for v in metrics_data.values() if v != analysis_result_id and v != item_id) else None
            
        except Exception as e:
            self.logger.error(f"提取实时监控指标失败: {e}")
            return None
    
    def _save_anomaly_details(self, realtime_metric_id: int, item_id: str, anomaly_results: Dict[str, Any]):
        """保存异常检测详情"""
        try:
            if 'all_anomalies' not in anomaly_results:
                return
            
            anomalies = anomaly_results['all_anomalies']
            for anomaly in anomalies:
                anomaly_data = {
                    'realtime_metric_id': realtime_metric_id,
                    'item_id': item_id,
                    'anomaly_type': anomaly.get('type', 'Unknown'),
                    'severity': anomaly.get('severity', 'LOW'),
                    'anomaly_time': self._parse_datetime(anomaly.get('timestamp')),
                    'description': anomaly.get('description', ''),
                    'price_at_anomaly': self._safe_float(anomaly.get('price')),
                    'volume_at_anomaly': self._safe_int(anomaly.get('volume')),
                    'z_score': self._safe_float(anomaly.get('z_score')),
                    'deviation_percent': self._safe_float(anomaly.get('deviation_percent')),
                    'related_data': json.dumps(anomaly.get('related_data', {}), default=_json_serializer)
                }
                
                self.anomaly_dao.create_anomaly_detail(anomaly_data)
                
        except Exception as e:
            self.logger.error(f"保存异常详情失败: {e}")
    
    def _safe_float(self, value) -> Optional[float]:
        """安全转换为浮点数，处理nan值"""
        try:
            if value is None:
                return None

            float_val = float(value)

            # 检查是否为nan或inf
            import math
            if math.isnan(float_val) or math.isinf(float_val):
                return None

            return float_val
        except (ValueError, TypeError):
            return None
    
    def _safe_int(self, value) -> Optional[int]:
        """安全转换为整数，处理nan值"""
        try:
            if value is None:
                return None

            # 先转换为float检查nan
            import math
            float_val = float(value)
            if math.isnan(float_val) or math.isinf(float_val):
                return None

            return int(float_val)
        except (ValueError, TypeError):
            return None
    
    def _map_risk_level(self, risk_level: str) -> Optional[str]:
        """映射风险等级"""
        if not risk_level:
            return None
        
        risk_level = str(risk_level).upper()
        if risk_level in ['LOW', 'MEDIUM', 'HIGH']:
            return risk_level
        elif risk_level in ['低', 'L']:
            return 'LOW'
        elif risk_level in ['中', '中等', 'M']:
            return 'MEDIUM'
        elif risk_level in ['高', 'H']:
            return 'HIGH'
        else:
            return None
    
    def _map_trading_signal(self, signal: str) -> Optional[str]:
        """映射交易信号"""
        if not signal:
            return None
        
        signal = str(signal).upper()
        if signal in ['BUY', 'SELL', 'HOLD', 'WAIT']:
            return signal
        elif signal in ['买入', '买', 'B']:
            return 'BUY'
        elif signal in ['卖出', '卖', 'S']:
            return 'SELL'
        elif signal in ['持有', '持', 'H']:
            return 'HOLD'
        elif signal in ['等待', '观望', 'W']:
            return 'WAIT'
        else:
            return None
    
    def _parse_datetime(self, timestamp) -> Optional[datetime]:
        """解析时间戳"""
        try:
            if isinstance(timestamp, datetime):
                return timestamp
            elif isinstance(timestamp, str):
                return datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            elif isinstance(timestamp, (int, float)):
                return datetime.fromtimestamp(timestamp)
            else:
                return datetime.now()
        except Exception:
            return datetime.now()
    
    def _save_technical_indicators(self, analysis_result_id: int, item_id: str, tech_signals: Dict[str, Any]):
        """保存技术指标（占位符，待实现）"""
        # TODO: 实现技术指标保存逻辑
        pass
    
    def _save_market_sentiment(self, analysis_result_id: int, item_id: str, sentiment_data: Dict[str, Any]):
        """保存市场情绪（占位符，待实现）"""
        # TODO: 实现市场情绪保存逻辑
        pass
    
    def _save_investment_strategies(self, analysis_result_id: int, item_id: str, trading_advice: Dict[str, Any]):
        """保存投资策略（占位符，待实现）"""
        # TODO: 实现投资策略保存逻辑
        pass

    def _extract_realtime_analysis_metrics_v2(self, analysis_result: Dict[str, Any],
                                         item_id: str, item_name: str, data_path: str,
                                         chart_path: str, report_path: str,
                                         analysis_duration: float) -> Dict[str, Any]:
        """提取实时分析的关键指标 - 完整版本，包含所有新字段"""
        data = {
            'item_id': item_id,
            'item_name': item_name,
            'analysis_version': 'V2.0',
            'data_path': data_path,
            'chart_path': chart_path,
            'report_path': report_path,
            'analysis_duration': analysis_duration
        }

        # 1. 提取当前价格（从多个可能的位置）
        current_price = None
        if 'current_price' in analysis_result:
            current_price = self._safe_float(analysis_result.get('current_price'))
        elif 'support_resistance' in analysis_result:
            current_price = self._safe_float(analysis_result['support_resistance'].get('current_price'))
        data['current_price'] = current_price

        # 2. 提取异常检测数据
        if 'anomaly_results' in analysis_result:
            anomaly = analysis_result['anomaly_results']
            data.update({
                'anomaly_count': self._safe_int(anomaly.get('total_anomalies', 0)),
                'anomaly_frequency': self._safe_float(anomaly.get('anomaly_frequency')),
                'anomaly_severity': anomaly.get('risk_assessment', 'LOW'),
                'anomaly_types': json.dumps(anomaly.get('by_type', {}), ensure_ascii=False, default=_json_serializer)
            })

        # 3. 提取交易信号数据
        if 'trading_signals' in analysis_result:
            signals = analysis_result['trading_signals']
            data.update({
                'price_trend': signals.get('signal'),
                'trend_strength': self._safe_float(signals.get('confidence')),
                'trading_signal_reason': signals.get('reason'),
                'signal_confidence': self._safe_float(signals.get('confidence'))
            })

        # 4. 提取风险评估数据
        if 'risk_assessment' in analysis_result:
            risk = analysis_result['risk_assessment']
            data.update({
                'risk_level': risk.get('risk_level'),
                'risk_score': self._safe_float(risk.get('score')),
                'volatility_level': risk.get('volatility'),
                'liquidity_level': risk.get('liquidity'),
                'stop_loss_price': self._safe_float(risk.get('stop_loss')),
                'take_profit_price': self._safe_float(risk.get('take_profit'))
            })

        # 5. 提取支撑阻力位数据
        if 'support_resistance' in analysis_result:
            sr = analysis_result['support_resistance']
            data.update({
                'support_levels': json.dumps(sr.get('support_levels', []), ensure_ascii=False, default=_json_serializer),
                'resistance_levels': json.dumps(sr.get('resistance_levels', []), ensure_ascii=False, default=_json_serializer)
            })

        # 6. 提取供需分析数据（新增统一供给分析）
        if 'supply_demand' in analysis_result:
            supply_demand = analysis_result['supply_demand']
            data.update({
                'supply_quantity': self._safe_int(supply_demand.get('supply_quantity')),
                'demand_quantity': self._safe_int(supply_demand.get('demand_quantity')),
                'supply_demand_ratio': self._safe_float(supply_demand.get('supply_demand_ratio'))
            })

            # 提取统一供给分析数据
            if 'unified_supply_analysis' in supply_demand:
                unified_analysis = supply_demand['unified_supply_analysis']
                data.update({
                    'supply_ratio': self._safe_float(unified_analysis.get('supply_ratio')),
                    'supply_status': unified_analysis.get('supply_status'),
                    'scarcity_level': unified_analysis.get('scarcity_level'),
                    'total_circulation': self._safe_int(unified_analysis.get('total_circulation')),
                    'supply_description': unified_analysis.get('description')
                })

        # 7. 计算和提取求购溢价分析
        current_price = self._safe_float(analysis_result.get('current_price'))
        bid_price = None

        # 从不同可能的位置提取bid_price
        bid_price = None
        if 'supply_demand' in analysis_result and 'bid_price' in analysis_result['supply_demand']:
            bid_price = self._safe_float(analysis_result['supply_demand'].get('bid_price'))
        elif 'bid_price' in analysis_result:
            bid_price = self._safe_float(analysis_result['bid_price'])

        if current_price and bid_price:
            # 获取历史数据（如果可用）
            historical_data = None
            if hasattr(self, '_get_historical_data'):
                try:
                    historical_data = self._get_historical_data(data_path)
                except:
                    historical_data = None

            bid_premium_analysis = self._calculate_bid_premium_analysis(
                bid_price, current_price, historical_data
            )

            data.update({
                'bid_price': bid_premium_analysis.get('bid_price'),
                'bid_premium_percentage': bid_premium_analysis.get('bid_premium'),
                'bid_premium_level': bid_premium_analysis.get('demand_strength'),
                'demand_strength': bid_premium_analysis.get('demand_strength'),
                'premium_trend': bid_premium_analysis.get('premium_trend'),
                'bid_premium_description': bid_premium_analysis.get('description'),
                'strength_description': bid_premium_analysis.get('strength_description')
            })

        # 8. 计算和提取流动性量化指标
        supply_quantity = self._safe_int(analysis_result.get('supply_demand', {}).get('supply_quantity'))
        demand_quantity = self._safe_int(analysis_result.get('supply_demand', {}).get('demand_quantity'))

        if supply_quantity is not None and demand_quantity is not None:
            # 获取总流通量（如果可用）
            total_circulation = None
            if 'supply_demand' in analysis_result and 'unified_supply_analysis' in analysis_result['supply_demand']:
                total_circulation = self._safe_int(
                    analysis_result['supply_demand']['unified_supply_analysis'].get('total_circulation')
                )

            # 获取历史成交量数据（如果可用）
            daily_volume = None
            if hasattr(self, '_get_daily_volume'):
                try:
                    daily_volume = self._get_daily_volume(data_path)
                except:
                    daily_volume = None

            liquidity_metrics = self._calculate_liquidity_metrics(
                supply_quantity, demand_quantity, current_price, bid_price,
                daily_volume, total_circulation
            )

            data.update({
                'daily_volume_avg': liquidity_metrics.get('daily_volume_avg'),
                'turnover_rate': liquidity_metrics.get('turnover_rate'),
                'bid_ask_spread_pct': liquidity_metrics.get('bid_ask_spread_pct'),
                'liquidity_score': liquidity_metrics.get('liquidity_score'),
                'execution_difficulty': liquidity_metrics.get('execution_difficulty'),
                'market_depth': liquidity_metrics.get('market_depth'),
                'total_orders': liquidity_metrics.get('total_orders'),
                'liquidity_description': liquidity_metrics.get('description')
            })

        # 9. 集成市场情绪分析系统
        market_sentiment_result = self._integrate_market_sentiment(analysis_result, data_path)
        data.update({
            'sentiment_score': market_sentiment_result.get('sentiment_score'),
            'sentiment_level': market_sentiment_result.get('sentiment_level'),
            'sentiment_description': market_sentiment_result.get('sentiment_description'),
            'fear_greed_index': market_sentiment_result.get('fear_greed_index'),
            'investor_confidence': market_sentiment_result.get('investor_confidence'),
            'sentiment_factors': market_sentiment_result.get('sentiment_factors'),
            'sentiment_advice': market_sentiment_result.get('sentiment_advice'),
            'sentiment_trend': market_sentiment_result.get('sentiment_trend'),
            'sentiment_component_scores': market_sentiment_result.get('component_scores'),
            'sentiment_data_source': market_sentiment_result.get('data_source'),
            'sentiment_analysis_status': market_sentiment_result.get('analysis_status')
        })

        # 10. 集成异常检测系统
        anomaly_detection_result = self._integrate_anomaly_detection(analysis_result, data_path)
        data.update({
            'total_anomalies': anomaly_detection_result.get('total_anomalies'),
            'high_risk_anomalies': anomaly_detection_result.get('high_risk_anomalies'),
            'medium_risk_anomalies': anomaly_detection_result.get('medium_risk_anomalies'),
            'low_risk_anomalies': anomaly_detection_result.get('low_risk_anomalies'),
            'anomaly_types': anomaly_detection_result.get('anomaly_types'),
            'recent_alerts': anomaly_detection_result.get('recent_alerts'),
            'anomaly_summary': anomaly_detection_result.get('anomaly_summary'),
            'anomaly_risk_level': anomaly_detection_result.get('risk_level'),
            'anomaly_risk_impact': anomaly_detection_result.get('risk_impact'),
            'anomaly_detection_status': anomaly_detection_result.get('detection_status')
        })

        # 11. 提取成交量状态
        data['volume_status'] = analysis_result.get('volume_status', 'UNKNOWN')

        # 12. 提取实时报告内容
        if 'realtime_report' in analysis_result:
            data['realtime_report_content'] = str(analysis_result['realtime_report'])

        # 13. 其他实时数据字段设为默认值（如果ssync系统没有提供）
        data.update({
            'price_24h_high': None,
            'price_24h_low': None,
            'price_24h_avg': None,
            'price_change_24h': None,
            'price_change_percent_24h': None,
            'volume_24h': None,
            'volume_change_percent': None,
            'volume_status': analysis_result.get('volume_status', 'NORMAL')
        })

        # 应用JSON输出格式标准化
        standardized_data = self._validate_and_standardize_json_output(data)

        return standardized_data

    def _calculate_investment_score(self, analysis_result: Dict[str, Any]) -> float:
        """计算投资评分（0-100分）"""
        try:
            score = 50.0  # 基础分数

            # 基于RSI的评分调整
            if 'technical_signals' in analysis_result:
                tech = analysis_result['technical_signals']
                rsi = self._safe_float(tech.get('rsi'))
                if rsi is not None:
                    if 30 <= rsi <= 70:  # RSI在正常范围
                        score += 10
                    elif rsi < 30:  # 超卖，可能是买入机会
                        score += 15
                    elif rsi > 70:  # 超买，风险较高
                        score -= 10

            # 基于风险等级的评分调整
            if 'risk_assessment' in analysis_result:
                risk = analysis_result['risk_assessment']
                risk_level = risk.get('overall_risk')
                if risk_level == 'LOW':
                    score += 15
                elif risk_level == 'MEDIUM':
                    score += 5
                elif risk_level == 'HIGH':
                    score -= 15

                # 基于综合评分的调整
                comprehensive_score = self._safe_float(risk.get('comprehensive_score'))
                if comprehensive_score is not None:
                    # 将综合评分转换为投资评分的一部分
                    score += min(comprehensive_score * 5, 20)  # 最多加20分

            # 基于交易信号的评分调整
            if 'trading_advice' in analysis_result:
                advice = analysis_result['trading_advice']
                action = advice.get('action')
                confidence = self._safe_float(advice.get('confidence'))

                if action == 'BUY':
                    score += 20
                elif action == 'HOLD':
                    score += 5
                elif action == 'SELL':
                    score -= 10

                # 基于置信度的调整
                if confidence is not None:
                    score += (confidence - 50) * 0.2  # 置信度高于50%加分，低于50%减分

            # 确保评分在0-100范围内
            score = max(0, min(100, score))
            return round(score, 2)

        except Exception as e:
            self.logger.error(f"计算投资评分失败: {e}")
            return 50.0  # 返回中性评分

    def _calculate_price_changes(self, data_path: str, current_price: float) -> tuple:
        """从原始数据计算24小时价格变化"""
        try:
            if not data_path or not current_price:
                return None, None

            # 尝试从数据路径中读取原始价格数据
            data_dir = Path(data_path)
            if not data_dir.exists():
                return None, None

            # 查找日K数据文件
            daily_file = data_dir / "日k.json"
            if daily_file.exists():
                import json
                with open(daily_file, 'r', encoding='utf-8') as f:
                    daily_data = json.load(f)

                if daily_data and len(daily_data) >= 2:
                    # 获取最近两天的数据
                    latest = daily_data[-1]
                    previous = daily_data[-2]

                    latest_price = float(latest.get('close', 0))
                    previous_price = float(previous.get('close', 0))

                    if latest_price > 0 and previous_price > 0:
                        price_change = latest_price - previous_price
                        price_change_percent = (price_change / previous_price) * 100

                        return round(price_change, 2), round(price_change_percent, 2)

            # 如果没有找到数据文件，返回默认值
            return None, None

        except Exception as e:
            self.logger.warning(f"计算价格变化失败: {e}")
            return None, None

    def _calculate_bid_premium_analysis(self, bid_price: float, current_price: float,
                                      historical_data: List = None) -> Dict:
        """
        计算求购溢价分析

        Args:
            bid_price: 求购价格
            current_price: 当前市场价格
            historical_data: 历史数据（用于趋势分析，可选）

        Returns:
            Dict: 包含求购溢价、需求强度等信息
        """
        try:
            if bid_price is None or current_price is None or current_price <= 0:
                return {
                    'bid_premium': 0.0,
                    'demand_strength': 'UNKNOWN',
                    'premium_trend': 'UNKNOWN',
                    'description': '价格数据无效',
                    'calculation_method': 'bid_premium_analysis'
                }

            # 计算求购溢价：(求购价 - 市场价) / 市场价 * 100%
            bid_premium = ((bid_price - current_price) / current_price) * 100

            # 评估需求强度
            if bid_premium > 5:
                demand_strength = 'EXTREMELY_STRONG'
                strength_description = '极强需求，买家愿意大幅溢价购买'
            elif bid_premium > 2:
                demand_strength = 'STRONG'
                strength_description = '强烈需求，买家愿意溢价购买'
            elif bid_premium > 0:
                demand_strength = 'MODERATE'
                strength_description = '适度需求，买家愿意小幅溢价'
            elif bid_premium > -2:
                demand_strength = 'NORMAL'
                strength_description = '正常需求，买卖价格接近'
            elif bid_premium > -5:
                demand_strength = 'WEAK'
                strength_description = '需求疲软，买家出价低于市场价'
            else:
                demand_strength = 'EXTREMELY_WEAK'
                strength_description = '需求极弱，买家大幅压价'

            # 分析溢价趋势（如果有历史数据）
            premium_trend = self._analyze_premium_trend(bid_premium, historical_data)

            # 生成描述
            if bid_premium >= 0:
                description = f'求购溢价+{bid_premium:.2f}%，{strength_description}'
            else:
                description = f'求购溢价{bid_premium:.2f}%，{strength_description}'

            return {
                'bid_premium': round(bid_premium, 2),
                'demand_strength': demand_strength,
                'premium_trend': premium_trend,
                'bid_price': bid_price,
                'current_price': current_price,
                'description': description,
                'strength_description': strength_description,
                'calculation_method': 'bid_premium_analysis'
            }

        except Exception as e:
            self.logger.error(f"计算求购溢价分析失败: {e}")
            return {
                'bid_premium': 0.0,
                'demand_strength': 'ERROR',
                'premium_trend': 'UNKNOWN',
                'description': f'计算失败: {str(e)}'
            }

    def _analyze_premium_trend(self, current_premium: float, historical_data: List = None) -> str:
        """
        分析求购溢价趋势

        Args:
            current_premium: 当前求购溢价
            historical_data: 历史数据

        Returns:
            str: 趋势描述 ('RISING', 'FALLING', 'STABLE', 'UNKNOWN')
        """
        try:
            if not historical_data or len(historical_data) < 2:
                return 'UNKNOWN'

            # 计算历史求购溢价
            historical_premiums = []
            for item in historical_data[-5:]:  # 取最近5个数据点
                if isinstance(item, (list, tuple)) and len(item) >= 4:
                    # 假设数据格式：[timestamp, price, supply, bid_price, ...]
                    try:
                        price = float(item[1])
                        bid_price = float(item[3])
                        if price > 0:
                            premium = ((bid_price - price) / price) * 100
                            historical_premiums.append(premium)
                    except (ValueError, IndexError):
                        continue
                elif isinstance(item, dict):
                    try:
                        price = float(item.get('price', 0))
                        bid_price = float(item.get('bid_price', 0))
                        if price > 0:
                            premium = ((bid_price - price) / price) * 100
                            historical_premiums.append(premium)
                    except (ValueError, TypeError):
                        continue

            if len(historical_premiums) < 2:
                return 'UNKNOWN'

            # 计算趋势
            recent_avg = sum(historical_premiums[-2:]) / 2  # 最近2个点的平均值
            earlier_avg = sum(historical_premiums[:-2]) / len(historical_premiums[:-2])  # 更早期的平均值

            trend_change = recent_avg - earlier_avg

            if trend_change > 1:
                return 'RISING'
            elif trend_change < -1:
                return 'FALLING'
            else:
                return 'STABLE'

        except Exception as e:
            self.logger.warning(f"分析溢价趋势失败: {e}")
            return 'UNKNOWN'

    def _calculate_liquidity_metrics(self, supply_quantity: int, demand_quantity: int,
                                   current_price: float, bid_price: float,
                                   daily_volume: float = None, total_circulation: int = None,
                                   historical_data: List = None) -> Dict:
        """
        计算流动性量化指标

        Args:
            supply_quantity: 供给数量（在售数量）
            demand_quantity: 需求数量（求购数量）
            current_price: 当前价格
            bid_price: 求购价格
            daily_volume: 日均成交量（可选）
            total_circulation: 总流通量（可选）
            historical_data: 历史数据（可选）

        Returns:
            Dict: 包含量化流动性指标
        """
        try:
            if supply_quantity is None or demand_quantity is None:
                return {
                    'daily_volume_avg': 0.0,
                    'turnover_rate': 0.0,
                    'bid_ask_spread_pct': 0.0,
                    'liquidity_score': 0,
                    'execution_difficulty': 'UNKNOWN',
                    'market_depth': 'UNKNOWN',
                    'description': '流动性数据不足',
                    'calculation_method': 'liquidity_metrics'
                }

            # 1. 计算日均成交量
            if daily_volume is None:
                # 基于供需数量估算日均成交量
                # 经验公式：日均成交量 ≈ (供给数量 + 需求数量) * 0.1
                daily_volume_avg = (supply_quantity + demand_quantity) * 0.1
            else:
                daily_volume_avg = daily_volume

            # 2. 计算换手率
            if total_circulation and total_circulation > 0:
                turnover_rate = (daily_volume_avg / total_circulation) * 100
            else:
                # 使用估算的总流通量
                estimated_circulation = max((supply_quantity + demand_quantity) * 20, 1000)
                turnover_rate = (daily_volume_avg / estimated_circulation) * 100

            # 3. 计算买卖价差百分比
            if current_price and bid_price and current_price > 0:
                bid_ask_spread_pct = abs((current_price - bid_price) / current_price) * 100
            else:
                bid_ask_spread_pct = 0.0

            # 4. 计算市场深度
            total_orders = supply_quantity + demand_quantity
            if total_orders > 500:
                market_depth = 'DEEP'
                depth_score = 90
            elif total_orders > 200:
                market_depth = 'MODERATE'
                depth_score = 70
            elif total_orders > 50:
                market_depth = 'SHALLOW'
                depth_score = 50
            else:
                market_depth = 'VERY_SHALLOW'
                depth_score = 20

            # 5. 计算综合流动性评分 (0-100)
            liquidity_score = self._calculate_comprehensive_liquidity_score(
                daily_volume_avg, turnover_rate, bid_ask_spread_pct,
                total_orders, depth_score
            )

            # 6. 评估执行难度
            execution_difficulty = self._assess_execution_difficulty(
                liquidity_score, bid_ask_spread_pct, total_orders
            )

            # 7. 生成描述
            description = self._generate_liquidity_description(
                liquidity_score, daily_volume_avg, turnover_rate,
                bid_ask_spread_pct, execution_difficulty
            )

            return {
                'daily_volume_avg': round(daily_volume_avg, 2),
                'turnover_rate': round(turnover_rate, 4),
                'bid_ask_spread_pct': round(bid_ask_spread_pct, 2),
                'liquidity_score': liquidity_score,
                'execution_difficulty': execution_difficulty,
                'market_depth': market_depth,
                'total_orders': total_orders,
                'depth_score': depth_score,
                'description': description,
                'calculation_method': 'quantified_liquidity_metrics'
            }

        except Exception as e:
            self.logger.error(f"计算流动性指标失败: {e}")
            return {
                'daily_volume_avg': 0.0,
                'turnover_rate': 0.0,
                'bid_ask_spread_pct': 0.0,
                'liquidity_score': 0,
                'execution_difficulty': 'ERROR',
                'description': f'计算失败: {str(e)}'
            }

    def _calculate_comprehensive_liquidity_score(self, daily_volume: float, turnover_rate: float,
                                               bid_ask_spread: float, total_orders: int,
                                               depth_score: int) -> int:
        """
        计算综合流动性评分

        Args:
            daily_volume: 日均成交量
            turnover_rate: 换手率
            bid_ask_spread: 买卖价差百分比
            total_orders: 总订单数
            depth_score: 市场深度评分

        Returns:
            int: 综合流动性评分 (0-100)
        """
        try:
            score = 0

            # 1. 成交量评分 (权重: 30%)
            if daily_volume >= 50:
                volume_score = 90
            elif daily_volume >= 20:
                volume_score = 70
            elif daily_volume >= 10:
                volume_score = 50
            elif daily_volume >= 5:
                volume_score = 30
            else:
                volume_score = 10

            score += volume_score * 0.3

            # 2. 换手率评分 (权重: 25%)
            if turnover_rate >= 2.0:
                turnover_score = 90
            elif turnover_rate >= 1.0:
                turnover_score = 70
            elif turnover_rate >= 0.5:
                turnover_score = 50
            elif turnover_rate >= 0.1:
                turnover_score = 30
            else:
                turnover_score = 10

            score += turnover_score * 0.25

            # 3. 价差评分 (权重: 25%) - 价差越小越好
            if bid_ask_spread <= 1.0:
                spread_score = 90
            elif bid_ask_spread <= 2.0:
                spread_score = 70
            elif bid_ask_spread <= 5.0:
                spread_score = 50
            elif bid_ask_spread <= 10.0:
                spread_score = 30
            else:
                spread_score = 10

            score += spread_score * 0.25

            # 4. 市场深度评分 (权重: 20%)
            score += depth_score * 0.2

            return min(100, max(0, int(score)))

        except Exception as e:
            self.logger.warning(f"计算综合流动性评分失败: {e}")
            return 50  # 返回中性评分

    def _assess_execution_difficulty(self, liquidity_score: int, bid_ask_spread: float,
                                   total_orders: int) -> str:
        """
        评估交易执行难度

        Args:
            liquidity_score: 流动性评分
            bid_ask_spread: 买卖价差百分比
            total_orders: 总订单数

        Returns:
            str: 执行难度等级
        """
        try:
            # 基于多个因素综合评估
            difficulty_score = 0

            # 流动性评分影响 (权重: 50%)
            if liquidity_score >= 80:
                difficulty_score += 90 * 0.5
            elif liquidity_score >= 60:
                difficulty_score += 70 * 0.5
            elif liquidity_score >= 40:
                difficulty_score += 50 * 0.5
            elif liquidity_score >= 20:
                difficulty_score += 30 * 0.5
            else:
                difficulty_score += 10 * 0.5

            # 价差影响 (权重: 30%)
            if bid_ask_spread <= 2.0:
                difficulty_score += 90 * 0.3
            elif bid_ask_spread <= 5.0:
                difficulty_score += 70 * 0.3
            elif bid_ask_spread <= 10.0:
                difficulty_score += 50 * 0.3
            else:
                difficulty_score += 20 * 0.3

            # 订单数量影响 (权重: 20%)
            if total_orders >= 300:
                difficulty_score += 90 * 0.2
            elif total_orders >= 100:
                difficulty_score += 70 * 0.2
            elif total_orders >= 50:
                difficulty_score += 50 * 0.2
            else:
                difficulty_score += 30 * 0.2

            # 根据综合评分确定执行难度
            if difficulty_score >= 80:
                return 'VERY_EASY'
            elif difficulty_score >= 65:
                return 'EASY'
            elif difficulty_score >= 50:
                return 'MODERATE'
            elif difficulty_score >= 35:
                return 'DIFFICULT'
            else:
                return 'VERY_DIFFICULT'

        except Exception as e:
            self.logger.warning(f"评估执行难度失败: {e}")
            return 'UNKNOWN'

    def _generate_liquidity_description(self, liquidity_score: int, daily_volume: float,
                                      turnover_rate: float, bid_ask_spread: float,
                                      execution_difficulty: str) -> str:
        """
        生成流动性描述

        Args:
            liquidity_score: 流动性评分
            daily_volume: 日均成交量
            turnover_rate: 换手率
            bid_ask_spread: 买卖价差
            execution_difficulty: 执行难度

        Returns:
            str: 流动性描述
        """
        try:
            # 基于评分确定流动性等级
            if liquidity_score >= 80:
                level = '极高'
                level_desc = '市场非常活跃，交易执行容易'
            elif liquidity_score >= 60:
                level = '高'
                level_desc = '市场较为活跃，交易执行相对容易'
            elif liquidity_score >= 40:
                level = '中等'
                level_desc = '市场活跃度一般，交易执行需要耐心'
            elif liquidity_score >= 20:
                level = '低'
                level_desc = '市场活跃度较低，交易执行可能困难'
            else:
                level = '极低'
                level_desc = '市场活跃度很低，交易执行困难'

            # 执行难度描述映射
            difficulty_desc = {
                'VERY_EASY': '非常容易',
                'EASY': '容易',
                'MODERATE': '一般',
                'DIFFICULT': '困难',
                'VERY_DIFFICULT': '非常困难',
                'UNKNOWN': '未知'
            }.get(execution_difficulty, '未知')

            description = (
                f'流动性{level}(评分{liquidity_score}/100)，{level_desc}。'
                f'日均成交量{daily_volume:.1f}件，换手率{turnover_rate:.2f}%，'
                f'买卖价差{bid_ask_spread:.2f}%，交易执行难度{difficulty_desc}。'
            )

            return description

        except Exception as e:
            self.logger.warning(f"生成流动性描述失败: {e}")
            return f'流动性评分{liquidity_score}/100，具体指标计算异常'

    def _integrate_anomaly_detection(self, analysis_result: Dict, data_path: str = None) -> Dict:
        """
        集成异常检测系统到JSON输出

        Args:
            analysis_result: 分析结果数据
            data_path: 数据路径（用于加载历史数据进行异常检测）

        Returns:
            Dict: 包含完整异常检测信息
        """
        try:
            # 首先检查是否已有异常检测结果
            existing_anomalies = analysis_result.get('anomalies', {})

            # 如果已有详细的异常检测结果，直接使用
            if existing_anomalies.get('anomaly_details') and len(existing_anomalies['anomaly_details']) > 0:
                return self._standardize_anomaly_format(existing_anomalies['anomaly_details'])

            # 尝试运行异常检测系统
            detected_anomalies = self._run_anomaly_detection(analysis_result, data_path)

            if detected_anomalies:
                return self._standardize_anomaly_format(detected_anomalies)
            else:
                # 如果没有检测到异常，返回默认格式
                return {
                    'total_anomalies': 0,
                    'high_risk_anomalies': 0,
                    'medium_risk_anomalies': 0,
                    'low_risk_anomalies': 0,
                    'anomaly_types': {},
                    'recent_alerts': [],
                    'anomaly_summary': '未检测到异常',
                    'risk_level': 'LOW',
                    'detection_status': 'COMPLETED'
                }

        except Exception as e:
            self.logger.error(f"集成异常检测失败: {e}")
            return {
                'total_anomalies': 0,
                'high_risk_anomalies': 0,
                'medium_risk_anomalies': 0,
                'low_risk_anomalies': 0,
                'anomaly_types': {},
                'recent_alerts': [],
                'anomaly_summary': f'异常检测失败: {str(e)}',
                'risk_level': 'UNKNOWN',
                'detection_status': 'ERROR'
            }

    def _run_anomaly_detection(self, analysis_result: Dict, data_path: str = None) -> List:
        """
        运行异常检测系统

        Args:
            analysis_result: 分析结果数据
            data_path: 数据路径

        Returns:
            List: 检测到的异常列表
        """
        try:
            # 尝试导入异常检测系统（修正类名）
            try:
                from cs2_investment.fx.ssync.anomaly_detection_system import AnomalyDetector
                AnomalyDetectionSystem = AnomalyDetector  # 别名兼容
            except ImportError:
                try:
                    from cs2_investment.fx.syncps.anomaly_detection_system import AnomalyDetector
                    AnomalyDetectionSystem = AnomalyDetector  # 别名兼容
                except ImportError:
                    self.logger.warning("无法导入异常检测系统")
                    return []

            # 如果有数据路径，尝试加载数据并运行检测
            if data_path and os.path.exists(data_path):
                try:
                    # 从数据路径提取饰品名称
                    skin_name = os.path.basename(data_path) or "Unknown"

                    # 创建异常检测系统实例（修正参数）
                    detector = AnomalyDetectionSystem(skin_name, data_path)

                    # 加载数据
                    detector.load_data()

                    # 运行完整检测
                    anomalies = detector.run_full_detection()

                    return anomalies if anomalies else []

                except Exception as e:
                    self.logger.warning(f"运行异常检测系统失败: {e}")

            # 如果无法运行完整检测，尝试基于现有数据进行简单异常检测
            return self._simple_anomaly_detection(analysis_result)

        except Exception as e:
            self.logger.error(f"异常检测运行失败: {e}")
            return []

    def _simple_anomaly_detection(self, analysis_result: Dict) -> List:
        """
        基于现有分析结果进行简单异常检测

        Args:
            analysis_result: 分析结果数据

        Returns:
            List: 检测到的异常列表
        """
        anomalies = []
        current_time = datetime.now()

        try:
            # 检查供需异常
            if 'supply_demand' in analysis_result:
                supply_demand = analysis_result['supply_demand']
                supply_qty = supply_demand.get('supply_quantity', 0)
                demand_qty = supply_demand.get('demand_quantity', 0)

                # 供需极度不平衡
                if supply_qty > 0 and demand_qty > 0:
                    ratio = supply_qty / demand_qty
                    if ratio > 10:  # 供给是需求的10倍以上
                        anomalies.append({
                            'type': '供需极度不平衡',
                            'datetime': current_time,
                            'severity': 'HIGH',
                            'description': f'供给数量({supply_qty})是需求数量({demand_qty})的{ratio:.1f}倍，市场严重失衡',
                            'supply_quantity': supply_qty,
                            'demand_quantity': demand_qty,
                            'ratio': ratio
                        })
                    elif ratio > 5:  # 供给是需求的5倍以上
                        anomalies.append({
                            'type': '供需不平衡',
                            'datetime': current_time,
                            'severity': 'MEDIUM',
                            'description': f'供给数量({supply_qty})是需求数量({demand_qty})的{ratio:.1f}倍，存在供给过剩',
                            'supply_quantity': supply_qty,
                            'demand_quantity': demand_qty,
                            'ratio': ratio
                        })

            # 检查价格异常
            current_price = analysis_result.get('current_price')
            if current_price:
                # 检查价格是否异常高或异常低（这里需要更多历史数据来判断）
                if current_price > 5000:  # 价格异常高
                    anomalies.append({
                        'type': '价格异常',
                        'datetime': current_time,
                        'severity': 'MEDIUM',
                        'description': f'当前价格({current_price})异常高，可能存在价格操纵',
                        'current_price': current_price
                    })

            return anomalies

        except Exception as e:
            self.logger.warning(f"简单异常检测失败: {e}")
            return []

    def _standardize_anomaly_format(self, anomalies: List) -> Dict:
        """
        标准化异常检测结果格式

        Args:
            anomalies: 异常列表

        Returns:
            Dict: 标准化的异常检测结果
        """
        try:
            if not anomalies:
                return {
                    'total_anomalies': 0,
                    'high_risk_anomalies': 0,
                    'medium_risk_anomalies': 0,
                    'low_risk_anomalies': 0,
                    'anomaly_types': {},
                    'recent_alerts': [],
                    'anomaly_summary': '未检测到异常',
                    'risk_level': 'LOW',
                    'detection_status': 'COMPLETED'
                }

            # 统计异常数量和类型
            total_anomalies = len(anomalies)
            high_risk_count = 0
            medium_risk_count = 0
            low_risk_count = 0
            anomaly_types = {}
            recent_alerts = []

            for anomaly in anomalies:
                # 统计风险等级
                severity = anomaly.get('severity', 'LOW').upper()
                if severity == 'HIGH':
                    high_risk_count += 1
                elif severity == 'MEDIUM':
                    medium_risk_count += 1
                else:
                    low_risk_count += 1

                # 统计异常类型
                anomaly_type = anomaly.get('type', '未知异常')
                anomaly_types[anomaly_type] = anomaly_types.get(anomaly_type, 0) + 1

                # 收集最近的异常预警（最多5个）
                if len(recent_alerts) < 5:
                    alert = {
                        'type': anomaly_type,
                        'severity': severity,
                        'description': anomaly.get('description', ''),
                        'timestamp': anomaly.get('datetime', datetime.now()).isoformat() if hasattr(anomaly.get('datetime', ''), 'isoformat') else str(anomaly.get('datetime', ''))
                    }
                    recent_alerts.append(alert)

            # 确定整体风险等级
            if high_risk_count > 0:
                risk_level = 'HIGH'
                risk_impact = '建议暂停交易，等待市场稳定'
            elif medium_risk_count > 2:
                risk_level = 'MEDIUM'
                risk_impact = '建议谨慎交易，密切关注市场变化'
            elif medium_risk_count > 0 or low_risk_count > 5:
                risk_level = 'MEDIUM'
                risk_impact = '存在一定风险，建议适度控制仓位'
            else:
                risk_level = 'LOW'
                risk_impact = '风险较低，可正常交易'

            # 生成异常摘要
            anomaly_summary = f'检测到{total_anomalies}个异常'
            if high_risk_count > 0:
                anomaly_summary += f'（高风险{high_risk_count}个'
                if medium_risk_count > 0:
                    anomaly_summary += f'，中风险{medium_risk_count}个'
                if low_risk_count > 0:
                    anomaly_summary += f'，低风险{low_risk_count}个'
                anomaly_summary += '）'
            elif medium_risk_count > 0:
                anomaly_summary += f'（中风险{medium_risk_count}个'
                if low_risk_count > 0:
                    anomaly_summary += f'，低风险{low_risk_count}个'
                anomaly_summary += '）'
            elif low_risk_count > 0:
                anomaly_summary += f'（低风险{low_risk_count}个）'

            return {
                'total_anomalies': total_anomalies,
                'high_risk_anomalies': high_risk_count,
                'medium_risk_anomalies': medium_risk_count,
                'low_risk_anomalies': low_risk_count,
                'anomaly_types': anomaly_types,
                'recent_alerts': recent_alerts,
                'anomaly_summary': anomaly_summary,
                'risk_level': risk_level,
                'risk_impact': risk_impact,
                'detection_status': 'COMPLETED'
            }

        except Exception as e:
            self.logger.error(f"标准化异常格式失败: {e}")
            return {
                'total_anomalies': 0,
                'high_risk_anomalies': 0,
                'medium_risk_anomalies': 0,
                'low_risk_anomalies': 0,
                'anomaly_types': {},
                'recent_alerts': [],
                'anomaly_summary': f'异常格式化失败: {str(e)}',
                'risk_level': 'UNKNOWN',
                'detection_status': 'ERROR'
            }

    def _integrate_market_sentiment(self, analysis_result: Dict, data_path: str = None) -> Dict:
        """
        集成市场情绪分析到JSON输出

        Args:
            analysis_result: 分析结果数据
            data_path: 数据路径（用于运行情绪分析）

        Returns:
            Dict: 包含完整市场情绪信息
        """
        try:
            # 首先检查是否已有市场情绪分析结果
            existing_sentiment = analysis_result.get('market_sentiment', {})

            # 如果已有详细的情绪分析结果，直接使用
            if existing_sentiment.get('comprehensive_score') is not None:
                return self._standardize_sentiment_format(existing_sentiment)

            # 尝试运行市场情绪分析系统
            sentiment_results = self._run_market_sentiment_analysis(analysis_result, data_path)

            if sentiment_results:
                return self._standardize_sentiment_format(sentiment_results)
            else:
                # 如果没有情绪分析结果，返回默认格式
                return {
                    'sentiment_score': 50,  # 中性评分
                    'sentiment_level': 'NEUTRAL',
                    'sentiment_description': '中性',
                    'fear_greed_index': 50,
                    'investor_confidence': 50,
                    'sentiment_factors': {
                        'supply_demand_sentiment': 'NEUTRAL',
                        'momentum_sentiment': 'NEUTRAL',
                        'fear_greed_level': 'NEUTRAL',
                        'anomaly_panic_level': 'NEUTRAL'
                    },
                    'sentiment_advice': '市场情绪中性，建议谨慎观望',
                    'sentiment_trend': 'STABLE',
                    'analysis_status': 'DEFAULT'
                }

        except Exception as e:
            self.logger.error(f"集成市场情绪分析失败: {e}")
            return {
                'sentiment_score': 50,
                'sentiment_level': 'UNKNOWN',
                'sentiment_description': '情绪分析失败',
                'fear_greed_index': 50,
                'investor_confidence': 50,
                'sentiment_factors': {},
                'sentiment_advice': f'情绪分析失败: {str(e)}',
                'sentiment_trend': 'UNKNOWN',
                'analysis_status': 'ERROR'
            }

    def _run_market_sentiment_analysis(self, analysis_result: Dict, data_path: str = None) -> Dict:
        """
        运行市场情绪分析系统

        Args:
            analysis_result: 分析结果数据
            data_path: 数据路径

        Returns:
            Dict: 情绪分析结果
        """
        try:
            # 尝试导入市场情绪分析器
            try:
                from cs2_investment.fx.syncps.market_sentiment_analyzer import MarketSentimentAnalyzer
            except ImportError:
                self.logger.warning("无法导入市场情绪分析器")
                return None

            # 如果有数据路径，尝试运行完整的情绪分析
            if data_path and os.path.exists(data_path):
                try:
                    # 创建市场情绪分析器实例
                    sentiment_analyzer = MarketSentimentAnalyzer(data_path)

                    # 运行情绪分析
                    sentiment_results = sentiment_analyzer.analyze_market_sentiment()

                    # 获取情绪摘要
                    sentiment_summary = sentiment_analyzer.get_sentiment_summary()

                    return sentiment_summary if sentiment_summary and 'error' not in sentiment_summary else None

                except Exception as e:
                    self.logger.warning(f"运行市场情绪分析失败: {e}")

            # 如果无法运行完整分析，尝试基于现有数据进行简单情绪分析
            return self._simple_sentiment_analysis(analysis_result)

        except Exception as e:
            self.logger.error(f"市场情绪分析运行失败: {e}")
            return None

    def _simple_sentiment_analysis(self, analysis_result: Dict) -> Dict:
        """
        基于现有分析结果进行简单情绪分析

        Args:
            analysis_result: 分析结果数据

        Returns:
            Dict: 简单情绪分析结果
        """
        try:
            # 基于供需情况评估情绪
            sentiment_score = 50  # 默认中性

            # 供需情绪评估
            if 'supply_demand' in analysis_result:
                supply_demand = analysis_result['supply_demand']
                supply_qty = supply_demand.get('supply_quantity', 0)
                demand_qty = supply_demand.get('demand_quantity', 0)

                if supply_qty > 0 and demand_qty > 0:
                    ratio = supply_qty / demand_qty
                    if ratio < 2:  # 供需平衡或需求旺盛
                        sentiment_score += 15
                    elif ratio > 5:  # 供给过剩
                        sentiment_score -= 15

            # 价格情绪评估
            current_price = analysis_result.get('current_price')
            if current_price:
                # 基于价格水平的简单情绪评估
                if current_price > 2000:  # 高价位，可能情绪偏谨慎
                    sentiment_score -= 5
                elif current_price < 500:  # 低价位，可能情绪偏积极
                    sentiment_score += 5

            # 求购溢价情绪评估
            if 'supply_demand' in analysis_result:
                bid_price = analysis_result['supply_demand'].get('bid_price')
                if bid_price and current_price:
                    premium = ((bid_price - current_price) / current_price) * 100
                    if premium > 2:  # 正溢价，情绪积极
                        sentiment_score += 10
                    elif premium < -5:  # 负溢价严重，情绪悲观
                        sentiment_score -= 10

            # 异常情况情绪评估
            if 'total_anomalies' in analysis_result:
                total_anomalies = analysis_result.get('total_anomalies', 0)
                high_risk_anomalies = analysis_result.get('high_risk_anomalies', 0)

                if high_risk_anomalies > 0:
                    sentiment_score -= 20  # 高风险异常严重影响情绪
                elif total_anomalies > 10:
                    sentiment_score -= 10  # 异常较多，情绪偏谨慎

            # 确保评分在合理范围内
            sentiment_score = max(0, min(100, sentiment_score))

            # 确定情绪等级
            if sentiment_score >= 80:
                sentiment_level = 'EXTREMELY_BULLISH'
                sentiment_description = '极度乐观'
            elif sentiment_score >= 65:
                sentiment_level = 'BULLISH'
                sentiment_description = '乐观'
            elif sentiment_score >= 55:
                sentiment_level = 'SLIGHTLY_BULLISH'
                sentiment_description = '偏乐观'
            elif sentiment_score >= 45:
                sentiment_level = 'NEUTRAL'
                sentiment_description = '中性'
            elif sentiment_score >= 30:
                sentiment_level = 'BEARISH'
                sentiment_description = '悲观'
            else:
                sentiment_level = 'EXTREMELY_BEARISH'
                sentiment_description = '极度悲观'

            return {
                'overall_sentiment': sentiment_level,
                'sentiment_description': sentiment_description,
                'comprehensive_score': sentiment_score,
                'sentiment_advice': f'基于简单分析，市场情绪{sentiment_description} (评分{sentiment_score:.1f})',
                'key_factors': {
                    '供需情绪': 'NEUTRAL',
                    '动量情绪': 'NEUTRAL',
                    '恐慌贪婪': 'NEUTRAL',
                    '异常恐慌': 'NEUTRAL'
                },
                'data_source': '简单情绪分析 (基于现有数据)'
            }

        except Exception as e:
            self.logger.warning(f"简单情绪分析失败: {e}")
            return None

    def _standardize_sentiment_format(self, sentiment_data: Dict) -> Dict:
        """
        标准化市场情绪分析结果格式

        Args:
            sentiment_data: 情绪分析数据

        Returns:
            Dict: 标准化的情绪分析结果
        """
        try:
            if not sentiment_data:
                return {
                    'sentiment_score': 50,
                    'sentiment_level': 'NEUTRAL',
                    'sentiment_description': '中性',
                    'fear_greed_index': 50,
                    'investor_confidence': 50,
                    'sentiment_factors': {},
                    'sentiment_advice': '情绪数据不可用',
                    'sentiment_trend': 'UNKNOWN',
                    'analysis_status': 'NO_DATA'
                }

            # 提取核心情绪指标
            sentiment_score = sentiment_data.get('comprehensive_score', 50)
            sentiment_level = sentiment_data.get('overall_sentiment', 'NEUTRAL')
            sentiment_description = sentiment_data.get('sentiment_description', '中性')
            sentiment_advice = sentiment_data.get('sentiment_advice', '无建议')

            # 提取情绪因子
            key_factors = sentiment_data.get('key_factors', {})
            sentiment_factors = {
                'supply_demand_sentiment': key_factors.get('供需情绪', 'NEUTRAL'),
                'momentum_sentiment': key_factors.get('动量情绪', 'NEUTRAL'),
                'fear_greed_level': key_factors.get('恐慌贪婪', 'NEUTRAL'),
                'anomaly_panic_level': key_factors.get('异常恐慌', 'NEUTRAL')
            }

            # 计算恐慌贪婪指数（基于情绪评分）
            fear_greed_index = self._calculate_fear_greed_index(sentiment_score, sentiment_factors)

            # 计算投资者信心指数
            investor_confidence = self._calculate_investor_confidence(sentiment_score, sentiment_factors)

            # 分析情绪趋势（如果有历史数据）
            sentiment_trend = self._analyze_sentiment_trend(sentiment_score)

            return {
                'sentiment_score': round(sentiment_score, 1),
                'sentiment_level': sentiment_level,
                'sentiment_description': sentiment_description,
                'fear_greed_index': fear_greed_index,
                'investor_confidence': investor_confidence,
                'sentiment_factors': sentiment_factors,
                'sentiment_advice': sentiment_advice,
                'sentiment_trend': sentiment_trend,
                'component_scores': sentiment_data.get('component_scores', {}),
                'data_source': sentiment_data.get('data_source', '情绪分析'),
                'analysis_status': 'COMPLETED'
            }

        except Exception as e:
            self.logger.error(f"标准化情绪格式失败: {e}")
            return {
                'sentiment_score': 50,
                'sentiment_level': 'UNKNOWN',
                'sentiment_description': '格式化失败',
                'fear_greed_index': 50,
                'investor_confidence': 50,
                'sentiment_factors': {},
                'sentiment_advice': f'情绪格式化失败: {str(e)}',
                'sentiment_trend': 'UNKNOWN',
                'analysis_status': 'ERROR'
            }

    def _calculate_fear_greed_index(self, sentiment_score: float, sentiment_factors: Dict) -> int:
        """
        计算恐慌贪婪指数

        Args:
            sentiment_score: 综合情绪评分
            sentiment_factors: 情绪因子

        Returns:
            int: 恐慌贪婪指数 (0-100)
        """
        try:
            # 基于情绪评分计算基础恐慌贪婪指数
            base_index = sentiment_score

            # 根据情绪因子进行调整
            fear_greed_level = sentiment_factors.get('fear_greed_level', 'NEUTRAL')

            if fear_greed_level == 'EXTREME_GREED':
                base_index = min(100, base_index + 20)
            elif fear_greed_level == 'GREED':
                base_index = min(100, base_index + 10)
            elif fear_greed_level == 'EXTREME_FEAR':
                base_index = max(0, base_index - 20)
            elif fear_greed_level == 'FEAR':
                base_index = max(0, base_index - 10)

            return int(base_index)

        except Exception as e:
            self.logger.warning(f"计算恐慌贪婪指数失败: {e}")
            return 50  # 返回中性值

    def _calculate_investor_confidence(self, sentiment_score: float, sentiment_factors: Dict) -> int:
        """
        计算投资者信心指数

        Args:
            sentiment_score: 综合情绪评分
            sentiment_factors: 情绪因子

        Returns:
            int: 投资者信心指数 (0-100)
        """
        try:
            # 基于情绪评分计算基础信心指数
            confidence_index = sentiment_score

            # 根据供需情绪调整
            supply_demand_sentiment = sentiment_factors.get('supply_demand_sentiment', 'NEUTRAL')
            if supply_demand_sentiment in ['BULLISH', 'EXTREMELY_BULLISH']:
                confidence_index = min(100, confidence_index + 10)
            elif supply_demand_sentiment in ['BEARISH', 'EXTREMELY_BEARISH']:
                confidence_index = max(0, confidence_index - 10)

            # 根据异常恐慌调整
            anomaly_panic_level = sentiment_factors.get('anomaly_panic_level', 'NEUTRAL')
            if anomaly_panic_level == 'HIGH_PANIC':
                confidence_index = max(0, confidence_index - 15)
            elif anomaly_panic_level == 'MEDIUM_PANIC':
                confidence_index = max(0, confidence_index - 8)

            return int(confidence_index)

        except Exception as e:
            self.logger.warning(f"计算投资者信心指数失败: {e}")
            return 50  # 返回中性值

    def _analyze_sentiment_trend(self, current_sentiment_score: float) -> str:
        """
        分析情绪趋势

        Args:
            current_sentiment_score: 当前情绪评分

        Returns:
            str: 情绪趋势 ('RISING', 'FALLING', 'STABLE', 'UNKNOWN')
        """
        try:
            # 由于缺少历史情绪数据，这里只能基于当前评分进行简单判断
            # 在实际应用中，应该基于历史情绪数据进行趋势分析

            if current_sentiment_score >= 70:
                return 'STABLE_HIGH'  # 高位稳定
            elif current_sentiment_score <= 30:
                return 'STABLE_LOW'   # 低位稳定
            else:
                return 'STABLE'       # 中位稳定

        except Exception as e:
            self.logger.warning(f"分析情绪趋势失败: {e}")
            return 'UNKNOWN'

    def _validate_and_standardize_json_output(self, data: Dict) -> Dict:
        """
        验证和标准化JSON输出格式

        Args:
            data: 原始分析数据

        Returns:
            Dict: 标准化后的数据
        """
        try:
            # 创建标准化的输出结构
            standardized_data = {}

            # 1. 基础信息字段标准化
            standardized_data.update(self._standardize_basic_fields(data))

            # 2. 供需分析字段标准化
            standardized_data.update(self._standardize_supply_demand_fields(data))

            # 3. 求购溢价字段标准化
            standardized_data.update(self._standardize_bid_premium_fields(data))

            # 4. 流动性指标字段标准化
            standardized_data.update(self._standardize_liquidity_fields(data))

            # 5. 异常检测字段标准化
            standardized_data.update(self._standardize_anomaly_fields(data))

            # 6. 市场情绪字段标准化
            standardized_data.update(self._standardize_sentiment_fields(data))

            # 7. 其他字段标准化
            standardized_data.update(self._standardize_other_fields(data))

            # 8. 数据质量验证
            validation_result = self._validate_data_quality(standardized_data)
            standardized_data['data_quality'] = validation_result

            # 9. 添加元数据
            standardized_data['metadata'] = self._generate_metadata()

            return standardized_data

        except Exception as e:
            self.logger.error(f"JSON输出标准化失败: {e}")
            # 返回原始数据，但添加错误标记
            data['standardization_error'] = str(e)
            data['standardization_status'] = 'FAILED'
            return data

    def _standardize_basic_fields(self, data: Dict) -> Dict:
        """标准化基础字段"""
        return {
            'item_id': str(data.get('item_id', '')),
            'item_name': str(data.get('item_name', '')),
            'analysis_timestamp': data.get('analysis_timestamp', datetime.now().isoformat()),
            'current_price': self._safe_float(data.get('current_price')),
            'data_path': str(data.get('data_path', '')),
            'chart_path': str(data.get('chart_path', '')),
            'report_path': str(data.get('report_path', '')),
            'test_duration_seconds': self._safe_float(data.get('test_duration_seconds'))
        }

    def _standardize_supply_demand_fields(self, data: Dict) -> Dict:
        """标准化供需分析字段"""
        return {
            'supply_quantity': self._safe_int(data.get('supply_quantity')),
            'demand_quantity': self._safe_int(data.get('demand_quantity')),
            'supply_demand_ratio': self._safe_float(data.get('supply_demand_ratio')),
            'supply_ratio': self._safe_float(data.get('supply_ratio')),
            'supply_status': str(data.get('supply_status', 'UNKNOWN')),
            'scarcity_level': str(data.get('scarcity_level', 'UNKNOWN')),
            'scarcity_description': str(data.get('scarcity_description', '')),
            'total_circulation': self._safe_int(data.get('total_circulation'))
        }

    def _standardize_bid_premium_fields(self, data: Dict) -> Dict:
        """标准化求购溢价字段"""
        return {
            'bid_price': self._safe_float(data.get('bid_price')),
            'bid_premium_percentage': self._safe_float(data.get('bid_premium_percentage')),
            'bid_premium_level': str(data.get('bid_premium_level', 'UNKNOWN')),
            'demand_strength': str(data.get('demand_strength', 'UNKNOWN')),
            'bid_premium_description': str(data.get('bid_premium_description', '')),
            'strength_description': str(data.get('strength_description', ''))
        }

    def _standardize_liquidity_fields(self, data: Dict) -> Dict:
        """标准化流动性指标字段"""
        return {
            'daily_volume_avg': self._safe_float(data.get('daily_volume_avg')),
            'turnover_rate': self._safe_float(data.get('turnover_rate')),
            'bid_ask_spread_pct': self._safe_float(data.get('bid_ask_spread_pct')),
            'liquidity_score': self._safe_int(data.get('liquidity_score')),
            'execution_difficulty': str(data.get('execution_difficulty', 'UNKNOWN')),
            'market_depth': str(data.get('market_depth', 'UNKNOWN')),
            'total_orders': self._safe_int(data.get('total_orders')),
            'liquidity_description': str(data.get('liquidity_description', ''))
        }

    def _standardize_anomaly_fields(self, data: Dict) -> Dict:
        """标准化异常检测字段"""
        return {
            'total_anomalies': self._safe_int(data.get('total_anomalies')),
            'high_risk_anomalies': self._safe_int(data.get('high_risk_anomalies')),
            'medium_risk_anomalies': self._safe_int(data.get('medium_risk_anomalies')),
            'low_risk_anomalies': self._safe_int(data.get('low_risk_anomalies')),
            'anomaly_types': data.get('anomaly_types', {}),
            'recent_alerts': data.get('recent_alerts', []),
            'anomaly_summary': str(data.get('anomaly_summary', '')),
            'anomaly_risk_level': str(data.get('anomaly_risk_level', 'UNKNOWN')),
            'anomaly_risk_impact': str(data.get('anomaly_risk_impact', '')),
            'anomaly_detection_status': str(data.get('anomaly_detection_status', 'UNKNOWN'))
        }

    def _standardize_sentiment_fields(self, data: Dict) -> Dict:
        """标准化市场情绪字段"""
        return {
            'sentiment_score': self._safe_float(data.get('sentiment_score')),
            'sentiment_level': str(data.get('sentiment_level', 'UNKNOWN')),
            'sentiment_description': str(data.get('sentiment_description', '')),
            'fear_greed_index': self._safe_int(data.get('fear_greed_index')),
            'investor_confidence': self._safe_int(data.get('investor_confidence')),
            'sentiment_factors': data.get('sentiment_factors', {}),
            'sentiment_advice': str(data.get('sentiment_advice', '')),
            'sentiment_trend': str(data.get('sentiment_trend', 'UNKNOWN')),
            'sentiment_component_scores': data.get('sentiment_component_scores', {}),
            'sentiment_data_source': str(data.get('sentiment_data_source', '')),
            'sentiment_analysis_status': str(data.get('sentiment_analysis_status', 'UNKNOWN'))
        }

    def _standardize_other_fields(self, data: Dict) -> Dict:
        """标准化其他字段"""
        return {
            'volume_status': str(data.get('volume_status', 'UNKNOWN')),
            'realtime_report_content': str(data.get('realtime_report_content', '')),
            'price_24h_high': self._safe_float(data.get('price_24h_high')),
            'price_24h_low': self._safe_float(data.get('price_24h_low')),
            'price_24h_avg': self._safe_float(data.get('price_24h_avg')),
            'price_change_24h': self._safe_float(data.get('price_change_24h')),
            'price_change_24h_pct': self._safe_float(data.get('price_change_24h_pct')),
            'volume_24h': self._safe_float(data.get('volume_24h')),
            'volume_change_24h_pct': self._safe_float(data.get('volume_change_24h_pct')),
            'market_cap': self._safe_float(data.get('market_cap')),
            'last_updated': data.get('last_updated', datetime.now().isoformat())
        }

    def _validate_data_quality(self, data: Dict) -> Dict:
        """
        验证数据质量

        Args:
            data: 标准化后的数据

        Returns:
            Dict: 数据质量验证结果
        """
        try:
            validation_result = {
                'overall_quality': 'GOOD',
                'completeness_score': 0,
                'accuracy_score': 0,
                'consistency_score': 0,
                'issues': [],
                'warnings': [],
                'validation_timestamp': datetime.now().isoformat()
            }

            # 1. 完整性检查
            completeness_result = self._check_data_completeness(data)
            validation_result['completeness_score'] = completeness_result['score']
            validation_result['issues'].extend(completeness_result['issues'])

            # 2. 准确性检查
            accuracy_result = self._check_data_accuracy(data)
            validation_result['accuracy_score'] = accuracy_result['score']
            validation_result['issues'].extend(accuracy_result['issues'])

            # 3. 一致性检查
            consistency_result = self._check_data_consistency(data)
            validation_result['consistency_score'] = consistency_result['score']
            validation_result['warnings'].extend(consistency_result['warnings'])

            # 4. 计算总体质量评分
            total_score = (
                validation_result['completeness_score'] * 0.4 +
                validation_result['accuracy_score'] * 0.4 +
                validation_result['consistency_score'] * 0.2
            )

            if total_score >= 90:
                validation_result['overall_quality'] = 'EXCELLENT'
            elif total_score >= 80:
                validation_result['overall_quality'] = 'GOOD'
            elif total_score >= 70:
                validation_result['overall_quality'] = 'FAIR'
            else:
                validation_result['overall_quality'] = 'POOR'

            validation_result['total_score'] = round(total_score, 1)

            return validation_result

        except Exception as e:
            self.logger.error(f"数据质量验证失败: {e}")
            return {
                'overall_quality': 'UNKNOWN',
                'completeness_score': 0,
                'accuracy_score': 0,
                'consistency_score': 0,
                'issues': [f'验证失败: {str(e)}'],
                'warnings': [],
                'total_score': 0,
                'validation_timestamp': datetime.now().isoformat()
            }

    def _check_data_completeness(self, data: Dict) -> Dict:
        """检查数据完整性"""
        try:
            required_fields = [
                'item_id', 'current_price', 'supply_quantity', 'demand_quantity',
                'sentiment_score', 'liquidity_score', 'total_anomalies'
            ]

            missing_fields = []
            null_fields = []

            for field in required_fields:
                if field not in data:
                    missing_fields.append(field)
                elif data[field] is None:
                    null_fields.append(field)

            issues = []
            if missing_fields:
                issues.append(f"缺失必需字段: {', '.join(missing_fields)}")
            if null_fields:
                issues.append(f"字段值为空: {', '.join(null_fields)}")

            # 计算完整性评分
            total_required = len(required_fields)
            missing_count = len(missing_fields) + len(null_fields)
            score = max(0, (total_required - missing_count) / total_required * 100)

            return {
                'score': round(score, 1),
                'issues': issues
            }

        except Exception as e:
            return {
                'score': 0,
                'issues': [f'完整性检查失败: {str(e)}']
            }

    def _check_data_accuracy(self, data: Dict) -> Dict:
        """检查数据准确性"""
        try:
            issues = []
            score = 100

            # 检查数值范围
            if data.get('current_price') is not None:
                if data['current_price'] <= 0:
                    issues.append("当前价格必须大于0")
                    score -= 20
                elif data['current_price'] > 100000:
                    issues.append("当前价格异常高，可能有误")
                    score -= 10

            if data.get('sentiment_score') is not None:
                if not (0 <= data['sentiment_score'] <= 100):
                    issues.append("情绪评分必须在0-100范围内")
                    score -= 15

            if data.get('liquidity_score') is not None:
                if not (0 <= data['liquidity_score'] <= 100):
                    issues.append("流动性评分必须在0-100范围内")
                    score -= 15

            if data.get('fear_greed_index') is not None:
                if not (0 <= data['fear_greed_index'] <= 100):
                    issues.append("恐慌贪婪指数必须在0-100范围内")
                    score -= 10

            if data.get('investor_confidence') is not None:
                if not (0 <= data['investor_confidence'] <= 100):
                    issues.append("投资者信心指数必须在0-100范围内")
                    score -= 10

            # 检查供需数量
            supply_qty = data.get('supply_quantity')
            demand_qty = data.get('demand_quantity')
            if supply_qty is not None and supply_qty < 0:
                issues.append("供给数量不能为负数")
                score -= 15
            if demand_qty is not None and demand_qty < 0:
                issues.append("需求数量不能为负数")
                score -= 15

            return {
                'score': max(0, round(score, 1)),
                'issues': issues
            }

        except Exception as e:
            return {
                'score': 0,
                'issues': [f'准确性检查失败: {str(e)}']
            }

    def _check_data_consistency(self, data: Dict) -> Dict:
        """检查数据一致性"""
        try:
            warnings = []
            score = 100

            # 检查供需比例一致性
            supply_qty = data.get('supply_quantity')
            demand_qty = data.get('demand_quantity')
            supply_demand_ratio = data.get('supply_demand_ratio')

            if supply_qty and demand_qty and supply_demand_ratio:
                expected_ratio = supply_qty / demand_qty if demand_qty > 0 else 0
                if abs(expected_ratio - supply_demand_ratio) > 0.1:
                    warnings.append(f"供需比例不一致: 计算值{expected_ratio:.2f}, 记录值{supply_demand_ratio:.2f}")
                    score -= 10

            # 检查求购溢价一致性
            current_price = data.get('current_price')
            bid_price = data.get('bid_price')
            bid_premium_pct = data.get('bid_premium_percentage')

            if current_price and bid_price and bid_premium_pct is not None:
                expected_premium = ((bid_price - current_price) / current_price) * 100
                if abs(expected_premium - bid_premium_pct) > 0.5:
                    warnings.append(f"求购溢价不一致: 计算值{expected_premium:.2f}%, 记录值{bid_premium_pct:.2f}%")
                    score -= 10

            # 检查异常数量一致性
            total_anomalies = data.get('total_anomalies', 0)
            high_risk = data.get('high_risk_anomalies', 0)
            medium_risk = data.get('medium_risk_anomalies', 0)
            low_risk = data.get('low_risk_anomalies', 0)

            if total_anomalies != (high_risk + medium_risk + low_risk):
                warnings.append(f"异常数量不一致: 总数{total_anomalies}, 分类合计{high_risk + medium_risk + low_risk}")
                score -= 15

            # 检查情绪评分与等级一致性
            sentiment_score = data.get('sentiment_score')
            sentiment_level = data.get('sentiment_level')

            if sentiment_score is not None and sentiment_level:
                expected_level = self._get_expected_sentiment_level(sentiment_score)
                if expected_level != sentiment_level:
                    warnings.append(f"情绪等级不一致: 评分{sentiment_score}应对应{expected_level}, 实际为{sentiment_level}")
                    score -= 10

            return {
                'score': max(0, round(score, 1)),
                'warnings': warnings
            }

        except Exception as e:
            return {
                'score': 0,
                'warnings': [f'一致性检查失败: {str(e)}']
            }

    def _get_expected_sentiment_level(self, score: float) -> str:
        """根据评分获取预期的情绪等级"""
        if score >= 80:
            return 'EXTREMELY_BULLISH'
        elif score >= 65:
            return 'BULLISH'
        elif score >= 55:
            return 'SLIGHTLY_BULLISH'
        elif score >= 45:
            return 'NEUTRAL'
        elif score >= 30:
            return 'BEARISH'
        else:
            return 'EXTREMELY_BEARISH'

    def _generate_metadata(self) -> Dict:
        """生成元数据"""
        return {
            'format_version': '2.0',
            'schema_version': '1.0',
            'generated_by': 'AnalysisDataService',
            'generation_timestamp': datetime.now().isoformat(),
            'field_definitions': {
                'supply_ratio': '供给比例 = 在售数量 / 总流通量 * 100%',
                'bid_premium_percentage': '求购溢价 = (求购价 - 市场价) / 市场价 * 100%',
                'turnover_rate': '换手率 = 日均成交量 / 总流通量 * 100%',
                'liquidity_score': '流动性评分 (0-100分)',
                'sentiment_score': '市场情绪评分 (0-100分)',
                'fear_greed_index': '恐慌贪婪指数 (0-100分)',
                'investor_confidence': '投资者信心指数 (0-100分)'
            },
            'data_sources': [
                'SupplyDemandAnalyzer',
                'MarketSentimentAnalyzer',
                'AnomalyDetectionSystem',
                'AnalysisDataService'
            ]
        }
