"""
持仓管理页面 - 修复版

提供持仓概览、交易记录、添加交易等功能。
修复了饰品搜索自动填充问题。
"""

import streamlit as st
import pandas as pd
import time
from typing import List, Dict, Any
from datetime import datetime, date
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.app.services.holding_service import HoldingService
from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.app.components import item_analysis_component


def search_items(search_term: str, limit: int = 50):
    """搜索饰品"""
    if not search_term or len(search_term) < 2:
        return []

    try:
        items = st.session_state.item_dao.search_items_by_name(search_term, limit=limit)
        # items现在是字典列表，不是ORM对象
        return [(item['item_id'], item['name']) for item in items]
    except Exception as e:
        st.error(f"搜索饰品失败: {e}")
        return []


def show_add_transaction_form():
    """显示添加交易表单"""
    st.subheader("添加交易")

    # 初始化session_state
    if 'selected_item_id' not in st.session_state:
        st.session_state.selected_item_id = ""
    if 'selected_item_name' not in st.session_state:
        st.session_state.selected_item_name = ""

    # 第一步：饰品选择
    st.markdown("### 第一步：选择饰品")
    
    # 使用tabs来组织搜索和手动输入
    search_tab, manual_tab = st.tabs(["🔍 搜索饰品", "✏️ 手动输入"])
    
    with search_tab:
        col1, col2 = st.columns([2, 1])
        
        with col1:
            search_term = st.text_input(
                "搜索饰品名称",
                placeholder="输入饰品名称...",
                key="search_input"
            )
        
        with col2:
            st.markdown("<br>", unsafe_allow_html=True)  # 对齐按钮
            search_clicked = st.button("🔍 搜索", key="search_btn")
        
        # 初始化搜索结果状态
        if 'search_results' not in st.session_state:
            st.session_state.search_results = []

        # 搜索结果
        if search_clicked and search_term and len(search_term) >= 2:
            with st.spinner("搜索中..."):
                try:
                    st.session_state.search_results = search_items(search_term)
                    if st.session_state.search_results:
                        st.success(f"找到 {len(st.session_state.search_results)} 个匹配的饰品")
                    else:
                        st.warning("未找到匹配的饰品，请尝试其他关键词或使用手动输入")
                except Exception as e:
                    st.error(f"搜索失败: {e}")

        # 显示搜索结果选择框（如果有结果）
        if st.session_state.search_results:
            options = ["请选择..."] + [f"{name} (ID: {item_id})" for item_id, name in st.session_state.search_results]
            selected = st.selectbox(
                "选择饰品",
                options=options,
                key="item_selector"
            )

            if selected != "请选择...":
                # 解析选择的饰品
                selected_item_id = None
                selected_item_name = None
                for item_id, item_name in st.session_state.search_results:
                    if f"{item_name} (ID: {item_id})" == selected:
                        selected_item_id = item_id
                        selected_item_name = item_name
                        break

                # 显示确认按钮
                if selected_item_id and selected_item_name:
                    if st.button(f"✅ 确认选择: {selected_item_name}", key="confirm_selection"):
                        st.session_state.selected_item_id = selected_item_id
                        st.session_state.selected_item_name = selected_item_name
                        # 清除搜索结果，避免重复显示
                        st.session_state.search_results = []
                        # 清除selectbox的值
                        if 'item_selector' in st.session_state:
                            del st.session_state.item_selector
                        st.success(f"已选择: {selected_item_name}")
                        st.rerun()

        # 如果没有搜索结果但有选择的饰品，显示当前选择
        elif st.session_state.selected_item_id and st.session_state.selected_item_name:
            st.info(f"✅ 当前选择: {st.session_state.selected_item_name}")
            if st.button("🔄 重新搜索", key="research_button"):
                # 清除当前选择，允许重新搜索
                st.session_state.selected_item_id = ""
                st.session_state.selected_item_name = ""
                st.rerun()
    
    with manual_tab:
        st.info("如果搜索不到目标饰品，可以手动输入饰品信息")
        
        manual_id = st.text_input(
            "饰品ID",
            placeholder="输入饰品ID",
            key="manual_id_input"
        )
        manual_name = st.text_input(
            "饰品名称",
            placeholder="输入饰品名称",
            key="manual_name_input"
        )
        
        if manual_id and manual_name:
            if st.button("✅ 确认手动输入", key="confirm_manual"):
                st.session_state.selected_item_id = manual_id
                st.session_state.selected_item_name = manual_name
                st.success(f"已设置: {manual_name}")
                st.rerun()
    
    # 显示当前选择
    st.markdown("### 当前选择")
    if st.session_state.selected_item_id and st.session_state.selected_item_name:
        st.success(f"📦 **{st.session_state.selected_item_name}** (ID: {st.session_state.selected_item_id})")
        
        if st.button("🗑️ 清除选择", key="clear_btn"):
            st.session_state.selected_item_id = ""
            st.session_state.selected_item_name = ""
            st.rerun()
    else:
        st.warning("⚠️ 请先选择或输入饰品信息")
    
    # 第二步：交易信息
    st.markdown("### 第二步：填写交易信息")
    
    # 只有选择了饰品才显示表单
    if st.session_state.selected_item_id and st.session_state.selected_item_name:
        with st.form("transaction_form"):
            col1, col2 = st.columns(2)
            
            with col1:
                transaction_type = st.selectbox(
                    "交易类型 *",
                    options=["买入", "卖出"],
                    key="trans_type"
                )
                
                quantity = st.number_input(
                    "数量 *",
                    min_value=1,
                    value=1,
                    key="trans_quantity"
                )
                
                price = st.number_input(
                    "单价 *",
                    min_value=0.01,
                    value=100.0,
                    step=0.01,
                    key="trans_price"
                )
            
            with col2:
                fee = st.number_input(
                    "手续费",
                    min_value=0.0,
                    value=0.0,
                    step=0.01,
                    key="trans_fee"
                )
                
                transaction_date = st.date_input(
                    "交易日期 *",
                    value=date.today(),
                    key="trans_date"
                )
                
                notes = st.text_area(
                    "备注",
                    placeholder="可选的交易备注信息",
                    key="trans_notes"
                )
            
            # 显示选择的饰品信息
            st.markdown("**交易饰品信息**")
            st.info(f"饰品: {st.session_state.selected_item_name} (ID: {st.session_state.selected_item_id})")
            
            # 提交按钮
            submitted = st.form_submit_button(
                "💾 添加交易",
                use_container_width=True,
                type="primary"
            )
            
            if submitted:
                # 处理表单提交
                try:
                    # 转换交易类型
                    trans_type = 'BUY' if transaction_type == '买入' else 'SELL'
                    
                    # 转换日期
                    trans_datetime = datetime.combine(transaction_date, datetime.now().time())
                    
                    # 添加交易
                    with st.spinner("添加交易中..."):
                        if trans_type == 'BUY':
                            result = st.session_state.holding_service.add_buy_transaction(
                                user_id="default_user",
                                item_id=st.session_state.selected_item_id,
                                item_name=st.session_state.selected_item_name,
                                quantity=quantity,
                                price=price,
                                transaction_date=trans_datetime,
                                fee=fee,
                                notes=notes
                            )
                        else:
                            result = st.session_state.holding_service.add_sell_transaction(
                                user_id="default_user",
                                item_id=st.session_state.selected_item_id,
                                item_name=st.session_state.selected_item_name,
                                quantity=quantity,
                                price=price,
                                transaction_date=trans_datetime,
                                fee=fee,
                                notes=notes
                            )
                    
                    if result.get('success'):
                        st.success(result.get('message', '交易添加成功'))
                        # 清除选择，准备下一次交易
                        st.session_state.selected_item_id = ""
                        st.session_state.selected_item_name = ""
                        st.rerun()
                    else:
                        st.error(result.get('message', '交易添加失败'))
                        
                except Exception as e:
                    st.error(f"添加交易失败: {e}")
    else:
        st.info("💡 请先选择饰品，然后填写交易信息")


def show_holdings_overview():
    """显示持仓概览"""
    st.markdown("### 📈 持仓概览")

    try:
        # 获取持仓汇总信息
        summary = st.session_state.holding_service.get_holding_summary("default_user")

        # 显示汇总指标
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric(
                label="持仓种类",
                value=f"{summary.get('total_holdings', 0)} 种"
            )

        with col2:
            st.metric(
                label="总数量",
                value=f"{summary.get('total_quantity', 0)} 个"
            )

        with col3:
            total_cost = summary.get('total_cost', 0)
            st.metric(
                label="总成本",
                value=f"¥{total_cost:,.2f}"
            )

        with col4:
            current_value = summary.get('current_value', 0)
            pnl_amount = summary.get('pnl_amount', 0)
            pnl_percentage = summary.get('pnl_percentage', 0)

            st.metric(
                label="当前价值",
                value=f"¥{current_value:,.2f}",
                delta=f"¥{pnl_amount:,.2f} ({pnl_percentage:+.2f}%)"
            )

        # 分页设置
        page_size = 10  # 每页显示10条持仓

        # 获取持仓总数
        total_holdings = st.session_state.holding_service.get_holdings_count("default_user")

        # 计算总页数
        total_pages = (total_holdings + page_size - 1) // page_size if total_holdings > 0 else 1

        # 获取当前页码（从session_state中获取，默认为1）
        if 'holdings_current_page' not in st.session_state:
            st.session_state.holdings_current_page = 1

        current_page = st.session_state.holdings_current_page

        # 确保页码在有效范围内
        if current_page > total_pages:
            current_page = total_pages
            st.session_state.holdings_current_page = current_page
        elif current_page < 1:
            current_page = 1
            st.session_state.holdings_current_page = current_page

        # 计算偏移量
        offset = (current_page - 1) * page_size

        # 获取当前页的持仓列表
        holdings = st.session_state.holding_service.get_holdings_with_current_prices(
            "default_user",
            limit=page_size,
            offset=offset
        )

        if holdings:
            # 显示图表分析（移到上面）
            if len(holdings) > 1:
                st.markdown("### 📊 持仓分析")

                # 持仓价值分布饼图
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown("#### 持仓价值分布")
                    chart_data = []
                    for holding in holdings:
                        if holding.get('current_value', 0) > 0:
                            chart_data.append({
                                'name': holding.get('item_name', 'N/A')[:20] + ('...' if len(holding.get('item_name', '')) > 20 else ''),
                                'value': holding.get('current_value', 0)
                            })

                    if chart_data:
                        import pandas as pd
                        df = pd.DataFrame(chart_data)
                        st.bar_chart(df.set_index('name')['value'])

                with col2:
                    st.markdown("#### 盈亏分布")
                    pnl_data = []
                    for holding in holdings:
                        if holding.get('price_available'):
                            pnl_data.append({
                                'name': holding.get('item_name', 'N/A')[:20] + ('...' if len(holding.get('item_name', '')) > 20 else ''),
                                'pnl': holding.get('pnl_amount', 0)
                            })

                    if pnl_data:
                        import pandas as pd
                        df = pd.DataFrame(pnl_data)
                        st.bar_chart(df.set_index('name')['pnl'])

            # 持仓列表（改为卡片样式，放在最下面）
            st.markdown("### 📋 持仓列表")

            # 美化的分页控件
            if total_holdings > 0:
                # 分页容器
                with st.container():
                    # 分页信息和控件
                    if total_pages > 1:
                        # 分页信息栏
                        col_info, col_controls = st.columns([1, 2])

                        with col_info:
                            st.markdown(f"""
                            <div style="
                                background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                                color: white;
                                padding: 8px 16px;
                                border-radius: 20px;
                                text-align: center;
                                font-weight: 500;
                                box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
                                margin-bottom: 10px;
                            ">
                                📊 共 {total_holdings} 个持仓 | 第 {current_page}/{total_pages} 页
                            </div>
                            """, unsafe_allow_html=True)

                        with col_controls:
                            # 分页控件容器
                            control_col1, control_col2, control_col3 = st.columns([1, 1, 2])

                            with control_col1:
                                # 页码输入框
                                st.markdown("**快速跳转**")
                                new_page = st.number_input(
                                    "页码",
                                    min_value=1,
                                    max_value=total_pages,
                                    value=current_page,
                                    key="holdings_page_input",
                                    label_visibility="collapsed"
                                )
                                if new_page != current_page:
                                    st.session_state.holdings_current_page = new_page
                                    st.rerun()

                            with control_col2:
                                # 页码选择器
                                st.markdown("**选择页面**")
                                selected_page = st.selectbox(
                                    "页面",
                                    options=list(range(1, total_pages + 1)),
                                    index=current_page - 1,
                                    key="holdings_page_selector",
                                    label_visibility="collapsed"
                                )
                                if selected_page != current_page:
                                    st.session_state.holdings_current_page = selected_page
                                    st.rerun()

                            with control_col3:
                                # 美化的分页按钮
                                st.markdown("**页面导航**")
                                btn_col1, btn_col2, btn_col3, btn_col4 = st.columns(4)

                                with btn_col1:
                                    if st.button("⏮️ 首页", disabled=(current_page <= 1), key="holdings_first_page",
                                                type="secondary", use_container_width=True):
                                        st.session_state.holdings_current_page = 1
                                        st.rerun()

                                with btn_col2:
                                    if st.button("◀️ 上页", disabled=(current_page <= 1), key="holdings_prev_page",
                                                type="secondary", use_container_width=True):
                                        st.session_state.holdings_current_page = current_page - 1
                                        st.rerun()

                                with btn_col3:
                                    if st.button("下页 ▶️", disabled=(current_page >= total_pages), key="holdings_next_page",
                                                type="secondary", use_container_width=True):
                                        st.session_state.holdings_current_page = current_page + 1
                                        st.rerun()

                                with btn_col4:
                                    if st.button("末页 ⏭️", disabled=(current_page >= total_pages), key="holdings_last_page",
                                                type="secondary", use_container_width=True):
                                        st.session_state.holdings_current_page = total_pages
                                        st.rerun()
                    else:
                        # 单页时的简单信息显示
                        st.markdown(f"""
                        <div style="
                            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
                            color: white;
                            padding: 8px 16px;
                            border-radius: 20px;
                            text-align: center;
                            font-weight: 500;
                            box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
                            margin-bottom: 10px;
                        ">
                            📊 共 {total_holdings} 个持仓
                        </div>
                        """, unsafe_allow_html=True)

                # 分隔线
                st.markdown("""
                <div style="
                    height: 2px;
                    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
                    margin: 15px 0;
                    border-radius: 1px;
                "></div>
                """, unsafe_allow_html=True)

            # 显示当前页的持仓卡片
            if holdings:
                for holding in holdings:
                    show_holding_card(holding)
            else:
                if total_holdings > 0:
                    st.info("📝 当前页面没有持仓记录")
                else:
                    st.info("📝 暂无持仓记录，请先添加交易")


    except Exception as e:
        st.error(f"加载持仓概览失败: {e}")
        st.info("📝 暂无持仓记录，请先添加交易")


def show_holding_card(holding: dict):
    """显示单个持仓卡片"""
    with st.container():
        col1, col2, col3, col4, col5, col6, col7, col8 = st.columns([3, 2, 2, 2, 2, 1, 1, 1])

        with col1:
            # 饰品名称和ID
            item_name = holding.get('item_name', '未知饰品')
            st.markdown(f"**🎮 {item_name}**")
            st.caption(f"ID: {holding.get('item_id', '未知')}")

        with col2:
            # 持仓数量和平均成本
            quantity = holding.get('total_quantity', 0)
            avg_cost = holding.get('average_cost', 0)
            st.metric("持仓数量", f"{quantity} 个")
            st.caption(f"平均成本: ¥{avg_cost:,.2f}")

        with col3:
            # 当前价格
            if holding.get('price_available'):
                current_price = holding.get('current_price', 0)
                st.metric("当前价格", f"¥{current_price:,.2f}")
            else:
                st.write("💰 价格未知")
                st.caption("无市场数据")

        with col4:
            # 总成本和当前价值
            total_cost = holding.get('total_cost', 0)
            st.metric("总成本", f"¥{total_cost:,.2f}")

            if holding.get('price_available'):
                current_value = holding.get('current_value', 0)
                st.caption(f"当前价值: ¥{current_value:,.2f}")
            else:
                st.caption("当前价值: 无数据")

        with col5:
            # 盈亏情况
            if holding.get('price_available'):
                pnl_amount = holding.get('pnl_amount', 0)
                pnl_percentage = holding.get('pnl_percentage', 0)

                # 根据盈亏情况选择颜色
                if pnl_amount > 0:
                    st.success(f"📈 +¥{pnl_amount:,.2f}")
                    st.caption(f"📈 +{pnl_percentage:.2f}%")
                elif pnl_amount < 0:
                    st.error(f"📉 ¥{pnl_amount:,.2f}")
                    st.caption(f"📉 {pnl_percentage:.2f}%")
                else:
                    st.info("📊 ¥0.00")
                    st.caption("📊 0.00%")
            else:
                st.write("📊 盈亏未知")
                st.caption("无价格数据")

        with col6:
            # 快捷卖出按钮
            item_id = holding.get('item_id')
            available_quantity = holding.get('available_quantity', holding.get('total_quantity', 0))

            if item_id and available_quantity > 0:
                if st.button("🔴", key=f"sell_{item_id}", help="快捷卖出"):
                    st.session_state[f'show_sell_form_{item_id}'] = True
                    st.rerun()
            else:
                st.write("🚫")
                st.caption("无可卖")

        with col7:
            # 删除持仓按钮
            if item_id:
                if st.button("🗑️", key=f"delete_{item_id}", help="删除持仓"):
                    st.session_state[f'show_delete_confirm_{item_id}'] = True
                    st.rerun()

        with col8:
            # 分析结果按钮
            if item_id:
                item_analysis_component.render_analysis_button(
                    item_id=item_id,
                    button_key=f"holding_analysis_{item_id}",
                    help_text="查看持仓饰品分析结果"
                )

        # 快捷卖出表单
        item_id = holding.get('item_id')
        if item_id and st.session_state.get(f'show_sell_form_{item_id}', False):
            show_quick_sell_form(holding)

        # 删除确认对话框
        if item_id and st.session_state.get(f'show_delete_confirm_{item_id}', False):
            show_delete_confirm_dialog(holding)

        # 分析结果展示
        if item_id:
            item_analysis_component.render_analysis_dialog(
                item_data={
                    'item_id': item_id,
                    'item_name': holding.get('item_name', '未知饰品')
                },
                dialog_key_suffix="_holdings"
            )

        st.divider()


def show_quick_sell_form(holding: dict):
    """显示快捷卖出表单"""
    item_id = holding.get('item_id')
    item_name = holding.get('item_name', '未知饰品')
    available_quantity = holding.get('available_quantity', holding.get('total_quantity', 0))
    current_price = holding.get('current_price', 0)

    with st.container():
        st.markdown(f"### 🔴 快捷卖出: {item_name}")

        col1, col2 = st.columns(2)

        with col1:
            # 卖出数量
            sell_quantity = st.number_input(
                "卖出数量",
                min_value=1,
                max_value=available_quantity,
                value=1,
                key=f"sell_quantity_{item_id}"
            )

            # 卖出单价
            default_price = current_price if current_price > 0 else holding.get('average_cost', 0)
            sell_price = st.number_input(
                "卖出单价 (¥)",
                min_value=0.01,
                value=float(default_price),
                step=0.01,
                format="%.2f",
                key=f"sell_price_{item_id}"
            )

        with col2:
            # 手续费
            sell_fee = st.number_input(
                "手续费 (¥)",
                min_value=0.0,
                value=0.0,
                step=0.01,
                format="%.2f",
                key=f"sell_fee_{item_id}"
            )

            # 备注
            sell_notes = st.text_area(
                "备注",
                placeholder="可选，记录卖出原因等信息",
                key=f"sell_notes_{item_id}",
                height=80
            )

        # 计算总金额
        total_amount = sell_quantity * sell_price - sell_fee
        st.info(f"💰 卖出总金额: ¥{total_amount:,.2f} (数量: {sell_quantity} × 单价: ¥{sell_price:,.2f} - 手续费: ¥{sell_fee:,.2f})")

        # 操作按钮
        col1, col2, col3 = st.columns([1, 1, 2])

        with col1:
            if st.button("✅ 确认卖出", key=f"confirm_sell_{item_id}", type="primary"):
                # 执行卖出操作
                try:
                    result = st.session_state.holding_service.add_sell_transaction(
                        user_id="default_user",
                        item_id=item_id,
                        item_name=item_name,
                        quantity=sell_quantity,
                        price=sell_price,
                        fee=sell_fee,
                        notes=sell_notes
                    )

                    if result.get('success'):
                        st.success(f"✅ {result.get('message', '卖出成功')}")
                        # 清除表单状态
                        st.session_state[f'show_sell_form_{item_id}'] = False
                        # 清除表单输入
                        for key in [f"sell_quantity_{item_id}", f"sell_price_{item_id}",
                                   f"sell_fee_{item_id}", f"sell_notes_{item_id}"]:
                            if key in st.session_state:
                                del st.session_state[key]
                        st.rerun()
                    else:
                        st.error(f"❌ 卖出失败: {result.get('message', '未知错误')}")

                except Exception as e:
                    st.error(f"❌ 卖出失败: {e}")

        with col2:
            if st.button("❌ 取消", key=f"cancel_sell_{item_id}"):
                # 取消卖出，隐藏表单
                st.session_state[f'show_sell_form_{item_id}'] = False
                st.rerun()

        with col3:
            st.caption(f"可卖数量: {available_quantity} 个")


def show_delete_confirm_dialog(holding: dict):
    """显示删除持仓确认对话框"""
    item_id = holding.get('item_id')
    item_name = holding.get('item_name', '未知饰品')
    total_quantity = holding.get('total_quantity', 0)
    total_cost = holding.get('total_cost', 0)

    with st.container():
        st.markdown(f"### ⚠️ 删除持仓确认")

        # 警告信息
        st.error(f"""
        **您即将删除以下持仓记录：**

        🎮 **饰品名称**: {item_name}
        📦 **持仓数量**: {total_quantity} 个
        💰 **总成本**: ¥{total_cost:,.2f}

        ⚠️ **警告**: 此操作将永久删除该持仓记录及其所有相关交易历史，且无法恢复！
        """)

        # 确认输入
        st.markdown("**请输入饰品名称以确认删除：**")
        confirm_input = st.text_input(
            "确认输入",
            placeholder=f"请输入: {item_name}",
            key=f"delete_confirm_input_{item_id}"
        )

        # 操作按钮
        col1, col2, col3 = st.columns([1, 1, 2])

        with col1:
            # 只有输入正确的饰品名称才能删除
            if confirm_input == item_name:
                if st.button("🗑️ 确认删除", key=f"confirm_delete_{item_id}", type="primary"):
                    # 执行删除操作
                    try:
                        result = st.session_state.holding_service.delete_holding(
                            user_id="default_user",
                            item_id=item_id
                        )

                        if result.get('success'):
                            st.success(f"✅ {result.get('message', '持仓删除成功')}")
                            # 清除对话框状态
                            st.session_state[f'show_delete_confirm_{item_id}'] = False
                            # 清除输入
                            if f"delete_confirm_input_{item_id}" in st.session_state:
                                del st.session_state[f"delete_confirm_input_{item_id}"]
                            st.rerun()
                        else:
                            st.error(f"❌ 删除失败: {result.get('message', '未知错误')}")

                    except Exception as e:
                        st.error(f"❌ 删除失败: {e}")
            else:
                st.button("🗑️ 确认删除", disabled=True, key=f"confirm_delete_disabled_{item_id}")
                if confirm_input:
                    st.caption("⚠️ 输入的饰品名称不匹配")

        with col2:
            if st.button("❌ 取消", key=f"cancel_delete_{item_id}"):
                # 取消删除，隐藏对话框
                st.session_state[f'show_delete_confirm_{item_id}'] = False
                # 清除输入
                if f"delete_confirm_input_{item_id}" in st.session_state:
                    del st.session_state[f"delete_confirm_input_{item_id}"]
                st.rerun()

        with col3:
            st.caption("💡 提示：删除后将无法恢复，请谨慎操作")


def show_transaction_history():
    """显示交易记录"""
    st.markdown("### 📋 交易记录")

    try:
        # 筛选选项
        col1, col2, col3 = st.columns(3)

        with col1:
            transaction_type_filter = st.selectbox(
                "交易类型",
                options=["全部", "买入", "卖出"],
                key="trans_type_filter"
            )

        with col2:
            # 获取用户的所有饰品用于筛选
            holdings = st.session_state.holding_service.get_holdings_with_current_prices("default_user")
            item_options = ["全部"] + [holding.get('item_name', 'N/A') for holding in holdings]

            item_filter = st.selectbox(
                "饰品筛选",
                options=item_options,
                key="item_filter"
            )

        with col3:
            days_filter = st.selectbox(
                "时间范围",
                options=[7, 30, 90, 365, 0],
                format_func=lambda x: f"最近{x}天" if x > 0 else "全部",
                index=1,  # 默认选择30天
                key="days_filter"
            )

        # 转换筛选条件
        trans_type = None
        if transaction_type_filter == "买入":
            trans_type = "BUY"
        elif transaction_type_filter == "卖出":
            trans_type = "SELL"

        # 获取对应的item_id
        item_id = None
        if item_filter != "全部":
            for holding in holdings:
                if holding.get('item_name') == item_filter:
                    item_id = holding.get('item_id')
                    break

        # 计算时间范围
        start_date = None
        if days_filter > 0:
            from datetime import datetime, timedelta
            start_date = datetime.now() - timedelta(days=days_filter)

        # 获取交易记录
        if 'transaction_dao' not in st.session_state:
            from src.cs2_investment.dao.holding_transaction_dao import HoldingTransactionDAO
            st.session_state.transaction_dao = HoldingTransactionDAO()

        transactions = st.session_state.transaction_dao.get_user_transactions(
            user_id="default_user",
            limit=100,
            transaction_type=trans_type,
            item_id=item_id,
            start_date=start_date
        )

        if transactions:
            # 显示交易统计
            st.markdown("#### 📊 交易统计")

            # 计算统计数据
            buy_transactions = [t for t in transactions if t['transaction_type'] == 'BUY']
            sell_transactions = [t for t in transactions if t['transaction_type'] == 'SELL']

            total_buy_amount = sum(t['total_amount'] for t in buy_transactions)
            total_sell_amount = sum(t['total_amount'] for t in sell_transactions)
            total_buy_quantity = sum(t['quantity'] for t in buy_transactions)
            total_sell_quantity = sum(t['quantity'] for t in sell_transactions)

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric(
                    label="买入交易",
                    value=f"{len(buy_transactions)} 笔",
                    delta=f"{total_buy_quantity} 个"
                )

            with col2:
                st.metric(
                    label="卖出交易",
                    value=f"{len(sell_transactions)} 笔",
                    delta=f"{total_sell_quantity} 个"
                )

            with col3:
                st.metric(
                    label="买入金额",
                    value=f"¥{total_buy_amount:,.2f}"
                )

            with col4:
                st.metric(
                    label="卖出金额",
                    value=f"¥{total_sell_amount:,.2f}",
                    delta=f"¥{total_sell_amount - total_buy_amount:+,.2f}"
                )

            # 显示交易记录表格
            st.markdown("#### 📋 交易明细")

            # 创建交易记录表格数据
            transaction_data = []
            for trans in transactions:
                transaction_data.append({
                    "交易时间": trans['transaction_date'].strftime("%Y-%m-%d %H:%M:%S"),
                    "交易类型": "🟢 买入" if trans['transaction_type'] == 'BUY' else "🔴 卖出",
                    "饰品名称": trans['item_name'],
                    "数量": trans['quantity'],
                    "单价": f"¥{trans['price']:,.2f}",
                    "总金额": f"¥{trans['total_amount']:,.2f}",
                    "手续费": f"¥{trans['fee']:,.2f}" if trans['fee'] > 0 else "-",
                    "备注": trans['notes'] if trans['notes'] else "-"
                })

            # 显示表格
            st.dataframe(
                transaction_data,
                use_container_width=True,
                hide_index=True
            )

            # 显示交易趋势图表
            if len(transactions) > 1:
                st.markdown("#### 📈 交易趋势")

                # 按日期聚合交易数据
                import pandas as pd
                from datetime import datetime

                df_data = []
                for trans in transactions:
                    df_data.append({
                        'date': trans['transaction_date'].date(),
                        'type': trans['transaction_type'],
                        'amount': trans['total_amount'],
                        'quantity': trans['quantity']
                    })

                df = pd.DataFrame(df_data)

                # 按日期和类型聚合
                daily_stats = df.groupby(['date', 'type']).agg({
                    'amount': 'sum',
                    'quantity': 'sum'
                }).reset_index()

                # 创建透视表
                pivot_amount = daily_stats.pivot(index='date', columns='type', values='amount').fillna(0)

                if not pivot_amount.empty:
                    st.line_chart(pivot_amount)
        else:
            st.info("📝 暂无交易记录，请先添加交易")

    except Exception as e:
        st.error(f"加载交易记录失败: {e}")
        st.info("📝 暂无交易记录，请先添加交易")


def show_page():
    """显示持仓管理页面"""
    try:
        # 初始化服务
        if 'holding_service' not in st.session_state:
            st.session_state.holding_service = HoldingService()
        if 'item_dao' not in st.session_state:
            st.session_state.item_dao = ItemDAO()

        st.title("📊 持仓管理")

        # 创建标签页
        tab1, tab2, tab3 = st.tabs(["📈 持仓概览", "💰 添加交易", "📋 交易记录"])

        with tab1:
            show_holdings_overview()

        with tab2:
            show_add_transaction_form()

        with tab3:
            show_transaction_history()

    except Exception as e:
        st.error(f"加载持仓管理页面失败: {e}")


if __name__ == "__main__":
    show_page()
