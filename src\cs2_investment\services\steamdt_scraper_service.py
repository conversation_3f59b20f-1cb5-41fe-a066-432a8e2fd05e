#!/usr/bin/env python3
"""
SteamDT 爬虫服务 - 基于steamdt_scraper_final.py
直接保存到数据库的版本
"""

import asyncio
import random
import time
import re
from datetime import datetime
from typing import List, Dict, Any, Optional
from playwright.async_api import async_playwright
from loguru import logger

from ..dao.item_dao import ItemDAO
from ..dao.market_snapshot_dao import MarketSnapshotDAO
from ..utils.data_cleaner import DataCleaner
import pymysql


class SteamDTScraperService:
    """SteamDT爬虫服务 - 基于验证过的steamdt_scraper_final.py实现"""
    
    def __init__(self):
        self.base_url = "https://steamdt.com/ladders"
        self.api_url = "https://sdt-api.ok-skins.com/user/ranking/v1/page"
        self.collected_data = []
        self.delay_range = (15, 45)  # 延迟范围（秒）
        self.max_items = 200  # 每个榜单最大抓取数量
        
        # 数据库DAO
        self.item_dao = ItemDAO()
        self.snapshot_dao = MarketSnapshotDAO()
        self.data_cleaner = DataCleaner()

        # 数据库连接配置
        self.db_config = {
            'host': '**************',
            'port': 3306,
            'user': 'root',
            'password': 'Lwz@86622',
            'database': 'cs2_market',
            'charset': 'utf8mb4'
        }
        
        # 榜单配置（完全基于steamdt_scraper_final.py）
        self.rankings = {
            "price_up": {
                "name": "价格榜-上涨榜",
                "tab_name": "价格榜",
                "sub_options": ["涨跌榜(百分比)"],
                "sort_order": "desc",  # 降序 - 上涨榜
                "time_periods": ["近1天","近3天","近7天", "近15天", "近一个月", "近三个月"]
            },
            "price_down": {
                "name": "价格榜-下跌榜",
                "tab_name": "价格榜",
                "sub_options": ["涨跌榜(百分比)"],
                "sort_order": "asc",   # 升序 - 下跌榜
                "time_periods": ["近1天","近3天","近7天", "近15天", "近一个月", "近三个月"]
            },
            "inventory": {
                "name": "在售数榜",
                "tab_name": "在售数榜",
                "sub_options": ["在售数变化(百分比)"],
                "sort_order": "desc",  # 降序
                "time_periods": ["近1天","近3天","近7天", "近15天", "近一个月", "近三个月"]
            },
            "transaction_amount": {
                "name": "成交榜-成交额",
                "tab_name": "成交榜",
                "sub_options": ["成交额"],
                "sort_order": "desc",  # 降序
                "time_periods": ["近1天","近3天","近7天", "近15天", "近一个月", "近三个月"]
            },
            "transaction_count": {
                "name": "成交榜-成交量",
                "tab_name": "成交榜",
                "sub_options": ["成交量"],
                "sort_order": "desc",  # 降序
                "time_periods": ["近1天","近3天","近7天", "近15天", "近一个月", "近三个月"]
            },
            "popularity": {
                "name": "热度榜",
                "tab_name": "饰品热度榜",
                "sub_options": ["热度榜单"],
                "sort_order": "desc",  # 降序
                "time_periods": ["近1天"]  # 热度榜只有近1天
            },
            "popularity_up": {
                "name": "热度上升榜",
                "tab_name": "饰品热度榜",
                "sub_options": ["热度上升榜单"],
                "sort_order": "desc",  # 降序
                "time_periods": ["近1天"]  # 热度上升榜只有近1天
            }
        }
        
        self.logger = logger.bind(service="SteamDTScraperService")
    
    def get_available_rankings(self) -> Dict[str, str]:
        """获取可用的榜单"""
        return {key: config["name"] for key, config in self.rankings.items()}
    
    def get_ranking_info(self, ranking_key: str) -> Optional[Dict[str, Any]]:
        """获取榜单信息"""
        if ranking_key not in self.rankings:
            return None
        
        config = self.rankings[ranking_key]
        return {
            "name": config["name"],
            "tab_name": config["tab_name"],
            "sub_options": config["sub_options"],
            "time_periods": config["time_periods"]
        }
    
    async def close_popup_if_exists(self, page):
        """关闭可能存在的弹窗 - 完全基于steamdt_scraper_final.py"""
        try:
            # 等待并关闭新功能发布弹窗
            close_button = await page.query_selector('button[aria-label="关闭此对话框"]')
            if close_button:
                await close_button.click()
                await asyncio.sleep(2)
                self.logger.info("✅ 已关闭弹窗")
                return True
        except Exception as e:
            self.logger.debug(f"关闭弹窗时出现异常（可能不存在弹窗）: {e}")
        return False
        
    def setup_api_monitoring(self, page):
        """设置API监控 - 完全基于steamdt_scraper_final.py"""
        def handle_response(response):
            if self.api_url in response.url:
                self.logger.info(f"🌐 捕获API响应: {response.status} {response.url}")
                # 异步处理响应数据
                asyncio.create_task(self.process_api_response(response))

        page.on('response', handle_response)

    async def process_api_response(self, response):
        """处理API响应数据 - 完全基于steamdt_scraper_final.py"""
        try:
            if response.status == 200:
                data = await response.json()

                # 提取数据列表
                if isinstance(data, dict) and 'data' in data:
                    data_field = data['data']
                    if isinstance(data_field, dict) and 'list' in data_field:
                        items = data_field['list']
                        if isinstance(items, list) and items:
                            # 收集数据
                            self.collected_data.extend(items)
                            self.logger.info(f"✅ 已收集{len(items)}条API数据，总计{len(self.collected_data)}条")

        except Exception as e:
            self.logger.error(f"❌ 处理API响应失败: {e}")

    async def scroll_and_load_data(self, page, target_count=200):
        """滚动页面加载数据 - 完全基于steamdt_scraper_final.py"""
        self.logger.info(f"🔄 开始滚动加载数据，目标数量: {target_count}")

        # 清空之前的数据
        self.collected_data = []

        scroll_count = 0
        max_scrolls = 50  # 最大滚动次数
        no_new_data_count = 0  # 连续无新数据次数

        while len(self.collected_data) < target_count and scroll_count < max_scrolls:
            current_count = len(self.collected_data)

            # 随机滚动方式（增加不规律性）
            scroll_type = random.choice(["full", "partial", "smooth"])
            if scroll_type == "full":
                await page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
            elif scroll_type == "partial":
                # 随机滚动距离
                scroll_distance = random.randint(800, 1500)
                await page.evaluate(f"window.scrollBy(0, {scroll_distance})")
            else:  # smooth
                await page.evaluate("window.scrollTo({top: document.body.scrollHeight, behavior: 'smooth'})")

            # 10%概率向上滚动一点（模拟用户回看）
            if random.random() < 0.1:
                back_scroll = random.randint(200, 500)
                await page.evaluate(f"window.scrollBy(0, -{back_scroll})")
                await asyncio.sleep(random.uniform(2, 5))

            # 等待数据加载（增加随机性）
            base_wait = random.uniform(8, 15)
            await asyncio.sleep(base_wait)

            new_count = len(self.collected_data)

            if new_count > current_count:
                no_new_data_count = 0
                self.logger.info(f"📊 第{scroll_count + 1}次滚动，API数据从{current_count}增加到{new_count}")
            else:
                no_new_data_count += 1
                self.logger.info(f"⚠️ 第{scroll_count + 1}次滚动，API数据未增加 ({no_new_data_count}/5)")

            scroll_count += 1

            # 如果连续5次没有新数据，停止滚动
            if no_new_data_count >= 5:
                self.logger.info("🛑 连续多次无新数据，停止滚动")
                break

            # 随机等待15-45秒，并增加额外的随机停顿
            delay = random.uniform(*self.delay_range)

            # 20%概率额外长时间停顿
            if random.random() < 0.2:
                extra_delay = random.uniform(60, 180)  # 1-3分钟额外停顿
                self.logger.info(f"😴 触发额外长停顿: {extra_delay/60:.1f}分钟")
                await asyncio.sleep(extra_delay)

            self.logger.info(f"⏳ 等待{delay:.1f}秒后继续...")
            await asyncio.sleep(delay)

        self.logger.info(f"✅ 滚动完成，共获取{len(self.collected_data)}条API数据")
        return self.collected_data[:target_count]  # 只返回前200条

    async def select_tab(self, page, tab_name):
        """选择榜单标签 - 完全基于steamdt_scraper_final.py"""
        try:
            # 等待标签加载
            await page.wait_for_selector('[role="tab"]', timeout=15000)

            # 点击指定标签
            await page.click(f'[role="tab"]:has-text("{tab_name}")')
            await asyncio.sleep(3)
            self.logger.info(f"✅ 已选择标签: {tab_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ 选择标签失败 {tab_name}: {e}")
            # 尝试备用选择器
            try:
                await page.click(f'text="{tab_name}"')
                await asyncio.sleep(3)
                self.logger.info(f"✅ 使用备用选择器成功选择标签: {tab_name}")
                return True
            except Exception as e2:
                self.logger.error(f"❌ 备用选择器也失败: {e2}")
                return False

    async def select_sub_option(self, page, option_name):
        """选择子选项（如涨跌榜百分比）- 完全基于steamdt_scraper_final.py"""
        try:
            await page.click(f'text="{option_name}"')
            await asyncio.sleep(2)
            self.logger.info(f"✅ 已选择子选项: {option_name}")
            return True
        except Exception as e:
            self.logger.error(f"❌ 选择子选项失败 {option_name}: {e}")
            return False

    async def select_time_period(self, page, time_period):
        """选择时间周期 - 完全基于steamdt_scraper_final.py"""
        try:
            # 查找并点击时间选择下拉框
            time_selector = page.locator('div').filter(has_text=re.compile(r'^近\d+天$|^近\d+个月$|^近一个月$|^近三个月$|^近六个月$|^近一年$')).nth(1)
            await time_selector.click()
            await asyncio.sleep(2)

            # 选择指定时间周期
            await page.get_by_role('option', name=time_period).click()
            await asyncio.sleep(3)
            self.logger.info(f"✅ 已选择时间周期: {time_period}")
            return True
        except Exception as e:
            self.logger.error(f"❌ 选择时间周期失败 {time_period}: {e}")
            # 尝试备用选择器
            try:
                # 使用更通用的选择器
                await page.click('[role="combobox"]')
                await asyncio.sleep(2)
                await page.click(f'[role="option"]:has-text("{time_period}")')
                await asyncio.sleep(3)
                self.logger.info(f"✅ 使用备用选择器成功选择时间周期: {time_period}")
                return True
            except Exception as e2:
                self.logger.error(f"❌ 备用时间选择器也失败: {e2}")
                return False

    async def select_sort_order(self, page, sort_order):
        """选择排序方式 - 使用更稳定的选择器 - 完全基于steamdt_scraper_final.py"""
        try:
            # 使用更稳定的文本选择器
            if sort_order == "desc":
                # 需要降序（上涨榜）
                current_sort = await page.query_selector('text="升序"')
                if current_sort:
                    self.logger.info("🔄 当前是升序，切换到降序排序（上涨榜）")
                    await current_sort.click()
                    await asyncio.sleep(random.uniform(3, 8))
                    self.logger.info("✅ 已切换到降序排序（上涨榜）")
                else:
                    self.logger.info("✅ 当前已是降序排序（上涨榜）")

            elif sort_order == "asc":
                # 需要升序（下跌榜）
                current_sort = await page.query_selector('text="降序"')
                if current_sort:
                    self.logger.info("🔄 当前是降序，切换到升序排序（下跌榜）")
                    await current_sort.click()
                    await asyncio.sleep(random.uniform(3, 8))
                    self.logger.info("✅ 已切换到升序排序（下跌榜）")
                else:
                    self.logger.info("✅ 当前已是升序排序（下跌榜）")

            return True

        except Exception as e:
            self.logger.error(f"❌ 选择排序方式失败 {sort_order}: {e}")
            return True  # 继续执行，排序失败不影响数据抓取

    def save_to_database(self, data: List[Dict], ranking_key: str, sub_option: str, time_period: str) -> Dict[str, Any]:
        """保存数据到数据库 - 替代原来的CSV保存"""
        if not data:
            self.logger.warning("⚠️ 没有数据可保存")
            return {"success_count": 0, "error_count": 0}

        try:
            # 构建数据源名称
            data_source = f"{ranking_key}_{sub_option}_{time_period}".replace("(", "").replace(")", "").replace(" ", "_")
            snapshot_time = datetime.now()

            success_count = 0
            error_count = 0

            self.logger.info(f"💾 开始保存{len(data)}条数据到数据库...")

            for item_data in data:
                try:
                    # 处理单个饰品数据
                    item_record, snapshot_record, platform_prices = self.process_item_data(item_data, data_source, snapshot_time)

                    if not item_record or not item_record.get("item_id"):
                        error_count += 1
                        continue

                    # 使用直接的数据库操作保存数据
                    self._save_item_to_db(item_record)

                    # 保存市场快照
                    if snapshot_record:
                        self._save_snapshot_to_db(snapshot_record)

                    # 保存平台价格
                    for platform_price in platform_prices:
                        if platform_price.get("platform_enum"):
                            self._save_platform_price_to_db(platform_price)

                    success_count += 1

                except Exception as e:
                    self.logger.error(f"❌ 保存单条数据失败: {e}")
                    error_count += 1

            self.logger.info(f"✅ 数据保存完成: 成功{success_count}条, 失败{error_count}条")

            return {
                "success_count": success_count,
                "error_count": error_count,
                "data_source": data_source
            }

        except Exception as e:
            self.logger.error(f"❌ 保存数据到数据库失败: {e}")
            return {"success_count": 0, "error_count": len(data)}

    def process_item_data(self, item_data: Dict, data_source: str, snapshot_time: datetime) -> tuple:
        """处理单个饰品数据 - 转换为数据库格式"""
        try:
            item_info = item_data.get("itemInfoVO", {})

            # 饰品基本信息
            item_record = {
                "item_id": self.data_cleaner.safe_str(item_info.get("itemId"), 50),
                "item_type": self.data_cleaner.safe_str(item_info.get("itemType"), 100),
                "def_index_name": self.data_cleaner.safe_str(item_info.get("defIndexName"), 100),
                "name": self.data_cleaner.safe_str(item_info.get("name"), 255),
                "market_hash_name": self.data_cleaner.safe_str(item_info.get("marketHashName"), 255),
                "image_url": self.data_cleaner.safe_str(item_info.get("imageUrl")),
                "quality": self.data_cleaner.safe_str(item_info.get("quality"), 50),
                "rarity": self.data_cleaner.safe_str(item_info.get("rarity"), 50),
                "exterior": self.data_cleaner.safe_str(item_info.get("exterior"), 50)
            }

            # 市场快照信息
            price_info = item_data.get("sellPriceInfoVO", {})
            sell_nums_info = item_data.get("sellNumsInfoVO", {})
            trans_count_info = item_data.get("transactionCountInfoVO", {})
            trans_amount_info = item_data.get("transactionAmountInfoVO", {})
            hot_info = item_data.get("hotVO", {})

            snapshot_record = {
                "item_id": item_record["item_id"],
                "snapshot_time": snapshot_time,
                "data_source": data_source,
                "current_price": self.data_cleaner.safe_decimal(price_info.get("price")),
                "diff_1d": self.data_cleaner.safe_decimal(price_info.get("diff1Days")),
                "diff_3d": self.data_cleaner.safe_decimal(price_info.get("diff3Days")),
                "diff_7d": self.data_cleaner.safe_decimal(price_info.get("diff7Days")),
                "diff_15d": self.data_cleaner.safe_decimal(price_info.get("diff15Days")),
                "diff_1m": self.data_cleaner.safe_decimal(price_info.get("diff1Months")),
                "diff_3m": self.data_cleaner.safe_decimal(price_info.get("diff3Months")),
                "trans_count_1d": self.data_cleaner.safe_int(trans_count_info.get("transactionCount1Days")),
                "trans_count_3d": self.data_cleaner.safe_int(trans_count_info.get("transactionCount3Days")),
                "trans_count_7d": self.data_cleaner.safe_int(trans_count_info.get("transactionCount7Days")),
                "trans_count_15d": self.data_cleaner.safe_int(trans_count_info.get("transactionCount15Days")),
                "trans_count_1m": self.data_cleaner.safe_int(trans_count_info.get("transactionCount1Months")),
                "trans_count_3m": self.data_cleaner.safe_int(trans_count_info.get("transactionCount3Months")),
                "trans_amount_1d": self.data_cleaner.safe_decimal(trans_amount_info.get("transactionAmount1Days")),
                "trans_amount_3d": self.data_cleaner.safe_decimal(trans_amount_info.get("transactionAmount3Days")),
                "trans_amount_7d": self.data_cleaner.safe_decimal(trans_amount_info.get("transactionAmount7Days")),
                "trans_amount_15d": self.data_cleaner.safe_decimal(trans_amount_info.get("transactionAmount15Days")),
                "trans_amount_1m": self.data_cleaner.safe_decimal(trans_amount_info.get("transactionAmount1Months")),
                "trans_amount_3m": self.data_cleaner.safe_decimal(trans_amount_info.get("transactionAmount3Months")),
                "hot_rank": self.data_cleaner.safe_int(hot_info.get("hotRank")),
                "hot_count": self.data_cleaner.safe_int(hot_info.get("hotCount")),
                "hot_keep_days": self.data_cleaner.safe_int(hot_info.get("hotKeepDays")),
                "sell_nums": self.data_cleaner.safe_int(sell_nums_info.get("sellNums")),
                "sell_nums_1d_rate": self.data_cleaner.safe_decimal(sell_nums_info.get("sellNums1DaysRate")),
                "sell_nums_7d_rate": self.data_cleaner.safe_decimal(sell_nums_info.get("sellNums7DaysRate")),
                "sell_nums_1m_rate": self.data_cleaner.safe_decimal(sell_nums_info.get("sellNums1MonthsRate")),
                "survive_num": self.data_cleaner.safe_int(item_data.get("surviveNum"))
            }

            # 平台价格信息
            platform_prices = []
            platform_list = item_data.get("platformInfoList", [])
            for platform_info in platform_list:
                platform_record = {
                    "item_id": item_record["item_id"],
                    "platform_enum": self.data_cleaner.safe_str(platform_info.get("platformEnum"), 50),
                    "platform_name": self.data_cleaner.safe_str(platform_info.get("platformName"), 100),
                    "price": self.data_cleaner.safe_decimal(platform_info.get("price")),
                    "update_time": datetime.fromtimestamp(platform_info.get("updateTime", 0) / 1000) if platform_info.get("updateTime") else None,
                    "link_url": self.data_cleaner.safe_str(platform_info.get("linkUrl"))
                }
                platform_prices.append(platform_record)

            return item_record, snapshot_record, platform_prices

        except Exception as e:
            self.logger.error(f"❌ 处理饰品数据失败: {e}")
            return None, None, []

    def _save_item_to_db(self, item_record: Dict):
        """保存饰品信息到数据库"""
        try:
            connection = pymysql.connect(**self.db_config)
            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO items (item_id, item_type, def_index_name, name, market_hash_name,
                                     image_url, quality, rarity, exterior)
                    VALUES (%(item_id)s, %(item_type)s, %(def_index_name)s, %(name)s, %(market_hash_name)s,
                            %(image_url)s, %(quality)s, %(rarity)s, %(exterior)s)
                    ON DUPLICATE KEY UPDATE
                    item_type = VALUES(item_type),
                    def_index_name = VALUES(def_index_name),
                    name = VALUES(name),
                    market_hash_name = VALUES(market_hash_name),
                    image_url = VALUES(image_url),
                    quality = VALUES(quality),
                    rarity = VALUES(rarity),
                    exterior = VALUES(exterior),
                    updated_at = CURRENT_TIMESTAMP
                """, item_record)
                connection.commit()
            connection.close()
        except Exception as e:
            self.logger.error(f"❌ 保存饰品信息失败: {e}")

    def _save_snapshot_to_db(self, snapshot_record: Dict):
        """保存市场快照到数据库"""
        try:
            connection = pymysql.connect(**self.db_config)
            with connection.cursor() as cursor:
                # 先删除同一数据源的旧记录
                cursor.execute("""
                    DELETE FROM market_snapshots
                    WHERE item_id = %s AND data_source = %s
                """, (snapshot_record["item_id"], snapshot_record["data_source"]))

                # 插入新记录
                cursor.execute("""
                    INSERT INTO market_snapshots (
                        item_id, snapshot_time, data_source, current_price, diff_1d, diff_3d, diff_7d,
                        diff_15d, diff_1m, diff_3m, trans_count_1d, trans_count_3d, trans_count_7d,
                        trans_count_15d, trans_count_1m, trans_count_3m, trans_amount_1d, trans_amount_3d,
                        trans_amount_7d, trans_amount_15d, trans_amount_1m, trans_amount_3m, hot_rank,
                        hot_count, hot_keep_days, sell_nums, sell_nums_1d_rate, sell_nums_7d_rate,
                        sell_nums_1m_rate, survive_num
                    ) VALUES (
                        %(item_id)s, %(snapshot_time)s, %(data_source)s, %(current_price)s, %(diff_1d)s,
                        %(diff_3d)s, %(diff_7d)s, %(diff_15d)s, %(diff_1m)s, %(diff_3m)s, %(trans_count_1d)s,
                        %(trans_count_3d)s, %(trans_count_7d)s, %(trans_count_15d)s, %(trans_count_1m)s,
                        %(trans_count_3m)s, %(trans_amount_1d)s, %(trans_amount_3d)s, %(trans_amount_7d)s,
                        %(trans_amount_15d)s, %(trans_amount_1m)s, %(trans_amount_3m)s, %(hot_rank)s,
                        %(hot_count)s, %(hot_keep_days)s, %(sell_nums)s, %(sell_nums_1d_rate)s,
                        %(sell_nums_7d_rate)s, %(sell_nums_1m_rate)s, %(survive_num)s
                    )
                """, snapshot_record)
                connection.commit()
            connection.close()
        except Exception as e:
            self.logger.error(f"❌ 保存市场快照失败: {e}")

    def _save_platform_price_to_db(self, platform_record: Dict):
        """保存平台价格到数据库"""
        try:
            connection = pymysql.connect(**self.db_config)
            with connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO platform_prices (item_id, platform_enum, platform_name, price, update_time, link_url)
                    VALUES (%(item_id)s, %(platform_enum)s, %(platform_name)s, %(price)s, %(update_time)s, %(link_url)s)
                    ON DUPLICATE KEY UPDATE
                    platform_name = VALUES(platform_name),
                    price = VALUES(price),
                    update_time = VALUES(update_time),
                    link_url = VALUES(link_url)
                """, platform_record)
                connection.commit()
            connection.close()
        except Exception as e:
            self.logger.error(f"❌ 保存平台价格失败: {e}")

    async def scrape_ranking(self, page, ranking_key: str, sub_option: str, time_period: str, target_count: int = 200) -> Optional[List[Dict]]:
        """抓取指定榜单数据 - 完全基于steamdt_scraper_final.py"""
        ranking_config = self.rankings[ranking_key]
        self.logger.info(f"🎯 开始抓取: {ranking_config['name']} - {sub_option} - {time_period}")

        try:
            # 1. 选择榜单标签
            if not await self.select_tab(page, ranking_config['tab_name']):
                return None

            # 2. 选择子选项（饰品热度榜不需要选择子选项）
            if ranking_config['tab_name'] != "饰品热度榜":
                if not await self.select_sub_option(page, sub_option):
                    return None
            else:
                self.logger.info("✅ 饰品热度榜无需选择子选项，直接使用默认选项")

            # 3. 选择时间周期（饰品热度榜不需要选择时间周期）
            if ranking_config['tab_name'] != "饰品热度榜":
                if not await self.select_time_period(page, time_period):
                    return None
            else:
                self.logger.info("✅ 饰品热度榜无需选择时间周期，默认为近1天")

            # 4. 选择排序方式（饰品热度榜不需要排序）
            if 'sort_order' in ranking_config and ranking_config['tab_name'] != "饰品热度榜":
                await self.select_sort_order(page, ranking_config['sort_order'])
            elif ranking_config['tab_name'] == "饰品热度榜":
                self.logger.info("✅ 饰品热度榜无需选择排序方式，使用默认排序")

            # 5. 滚动加载数据
            data = await self.scroll_and_load_data(page, target_count)

            # 6. 保存数据到数据库
            save_result = self.save_to_database(data, ranking_key, sub_option, time_period)
            self.logger.info(f"✅ 保存结果: 成功{save_result['success_count']}条, 失败{save_result['error_count']}条")

            return data

        except Exception as e:
            self.logger.error(f"❌ 抓取榜单数据失败: {e}")
            return None

    async def create_browser(self):
        """创建浏览器 - 完全基于steamdt_scraper_final.py"""
        playwright = await async_playwright().start()

        browser = await playwright.chromium.launch(
            headless=False,  # 显示浏览器窗口
            args=[
                '--no-sandbox',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )

        context = await browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )

        page = await context.new_page()
        return playwright, browser, page

    async def run_scraping(self, ranking_keys: Optional[List[str]] = None, target_count: int = 200) -> Dict[str, Any]:
        """运行完整的抓取流程 - 完全基于steamdt_scraper_final.py"""
        if ranking_keys is None:
            ranking_keys = list(self.rankings.keys())

        self.logger.info(f"🚀 开始SteamDT数据抓取，榜单数量: {len(ranking_keys)}")

        playwright, browser, page = await self.create_browser()

        try:
            # 设置API监控
            self.setup_api_monitoring(page)

            # 访问页面
            self.logger.info("🌐 正在访问SteamDT页面...")
            await page.goto(self.base_url)
            await asyncio.sleep(5)

            # 关闭可能存在的弹窗
            await self.close_popup_if_exists(page)

            total_scraped = 0
            results = []

            # 遍历所有榜单配置
            for ranking_key in ranking_keys:
                if ranking_key not in self.rankings:
                    self.logger.warning(f"⚠️ 跳过未知榜单: {ranking_key}")
                    continue

                ranking_config = self.rankings[ranking_key]

                for sub_option in ranking_config['sub_options']:
                    for time_period in ranking_config['time_periods']:
                        try:
                            data = await self.scrape_ranking(page, ranking_key, sub_option, time_period, target_count)
                            if data:
                                total_scraped += len(data)
                                results.append({
                                    "ranking_key": ranking_key,
                                    "sub_option": sub_option,
                                    "time_period": time_period,
                                    "data_count": len(data)
                                })

                            # 榜单间长时间等待
                            delay = random.uniform(60, 180)  # 1-3分钟
                            self.logger.info(f"⏳ 榜单间等待{delay/60:.1f}分钟...")
                            await asyncio.sleep(delay)

                            # 30%概率额外超长停顿
                            if random.random() < 0.3:
                                super_long_delay = random.uniform(300, 600)  # 5-10分钟
                                self.logger.info(f"😴 触发超长停顿: {super_long_delay/60:.1f}分钟")
                                await asyncio.sleep(super_long_delay)

                        except Exception as e:
                            self.logger.error(f"❌ 抓取榜单异常: {ranking_key} - {e}")

            self.logger.info(f"🎉 抓取完成！总共获取{total_scraped}条数据")

            return {
                "total_rankings": len(results),
                "total_scraped": total_scraped,
                "results": results
            }

        except Exception as e:
            self.logger.error(f"❌ 抓取过程中发生错误: {e}")
            return {"total_rankings": 0, "total_scraped": 0, "results": []}
        finally:
            await browser.close()
            await playwright.stop()
