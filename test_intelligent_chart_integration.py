#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能分析图表集成功能

验证新的智能分析图表生成功能是否正确集成
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def test_intelligent_chart_integration():
    """测试智能分析图表集成功能"""
    print("🧪 开始测试智能分析图表集成功能")
    print("=" * 60)
    
    # 指定测试饰品ID
    item_id = "25698"  # 使用现有的测试饰品ID
    
    try:
        # 导入测试模块
        from tests.test_single_item_analysis import SingleItemAnalysisTester
        
        # 创建测试器
        tester = SingleItemAnalysisTester(item_id)
        
        print(f"🎯 测试饰品: {item_id}")
        print(f"📁 数据路径: {tester.data_base_path}")
        
        # 检查数据可用性
        print(f"\n📋 检查数据可用性...")
        data_availability = tester.check_data_availability()
        
        available_files = sum(1 for info in data_availability.values() if info.get('available', False))
        total_files = len(data_availability)
        
        print(f"   可用文件: {available_files}/{total_files}")
        
        if available_files == 0:
            print("❌ 没有可用的数据文件，无法进行测试")
            print("💡 请确保 data/scraped_data/25698/ 目录下有数据文件")
            return False
        
        # 运行完整分析
        print(f"\n🚀 运行智能分析...")
        results = await tester.run_complete_analysis()
        
        if results.get('status') != 'success':
            print(f"❌ 智能分析失败: {results.get('error', 'Unknown error')}")
            return False
        
        # 添加增强指标
        print(f"\n🔍 添加增强指标...")
        enhanced_results = tester.add_enhanced_metrics(results)
        
        # 测试图表生成
        print(f"\n📊 测试智能图表生成...")
        chart_result = tester.generate_intelligent_chart(enhanced_results)
        
        # 显示图表生成结果
        print(f"\n📈 图表生成结果:")
        if chart_result.get('success', False):
            print(f"   ✅ 图表生成成功!")
            chart_path = chart_result.get('chart_path')
            if chart_path:
                print(f"   📁 图表路径: {chart_path}")
                
                # 检查文件是否真的存在
                if Path(chart_path).exists():
                    file_size = Path(chart_path).stat().st_size / 1024  # KB
                    print(f"   📏 文件大小: {file_size:.1f} KB")
                    print(f"   🎉 图表文件确认存在!")
                else:
                    print(f"   ⚠️ 图表文件不存在")
                    return False
            
            chart_type = chart_result.get('chart_type', 'unknown')
            timestamp = chart_result.get('timestamp', 'unknown')
            print(f"   🎨 图表类型: {chart_type}")
            print(f"   ⏰ 生成时间: {timestamp}")
            
            return True
        else:
            print(f"   ❌ 图表生成失败")
            error = chart_result.get('error', 'Unknown error')
            print(f"   💥 失败原因: {error}")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_chart_system_directly():
    """直接测试图表系统"""
    print("\n" + "=" * 60)
    print("🧪 直接测试智能图表系统")
    print("=" * 60)
    
    try:
        # 导入智能图表系统
        from src.cs2_investment.fx.intelligent_analysis.visualization.intelligent_chart_system import IntelligentChartSystem
        
        # 创建模拟分析结果
        mock_analysis_results = {
            'status': 'success',
            'item_id': '25698',
            'comprehensive': {
                'success': True,
                'analysis_data': {
                    'price_analysis': {'trend_score': 75},
                    'technical_signals': {'overall_score': 68, 'rsi': 45, 'macd': 0.12},
                    'supply_demand_analysis': {'balance_score': 82},
                    'market_sentiment': {'sentiment_score': 58},
                    'risk_assessment': {'risk_score': 35}
                }
            },
            'strategic': {
                'success': True,
                'analysis_data': {
                    'long_term_trend': {'trend_strength': 70},
                    'fundamental_analysis': {'health_score': 85},
                    'competitive_position': {'position_score': 60},
                    'growth_potential': {'potential_score': 75},
                    'value_assessment': {'value_score': 80}
                }
            },
            'tactical': {
                'success': True,
                'analysis_data': {
                    'indicators_status': {'overall_score': 65},
                    'trading_signals': {'signal_strength': 70},
                    'momentum_analysis': {'momentum_score': 55},
                    'support_resistance': {'level_strength': 80},
                    'entry_timing': {'timing_score': 60}
                }
            },
            'execution': {
                'success': True,
                'analysis_data': {
                    'liquidity_analysis': {'liquidity_score': 72},
                    'execution_cost': {'cost_score': 25},
                    'market_impact': {'impact_score': 30},
                    'timing_window': {'window_score': 85},
                    'execution_risk': {'risk_score': 20}
                }
            },
            'evaluation': {
                'overall_score': 78,
                'quality_level': 'GOOD',
                'successful_analyses': 4,
                'failed_analyses': 0
            },
            'recommendation': {
                'recommendation_type': 'BUY',
                'confidence_level': 75
            },
            'enhanced_metrics': {
                'supply_demand_unified': {
                    'supply_ratio': 15.5,
                    'supply_status': 'TIGHT',
                    'scarcity_level': 'HIGH'
                },
                'liquidity_quantification': {
                    'liquidity_score': 72.3,
                    'turnover_rate': 8.5,
                    'market_depth': 65
                },
                'market_sentiment_quantified': {
                    'sentiment_score': 65.8,
                    'sentiment_level': 'BULLISH',
                    'fear_greed_index': 58,
                    'investor_confidence': 78.2
                },
                'anomaly_detection': {
                    'total_anomalies': 2,
                    'high_risk_anomalies': 0,
                    'medium_risk_anomalies': 1,
                    'low_risk_anomalies': 1
                }
            }
        }
        
        print(f"📊 创建智能图表系统...")
        chart_system = IntelligentChartSystem(mock_analysis_results, "25698")
        
        # 生成图表
        timestamp = "test_" + str(int(time.time())) if 'time' in globals() else "test"
        project_root = Path(__file__).parent
        chart_dir = project_root / "data" / "analysis_results" / "25698"
        chart_dir.mkdir(parents=True, exist_ok=True)
        
        chart_path = chart_dir / f"25698_智能分析仪表板_直接测试.png"
        
        print(f"🎨 生成智能分析仪表板...")
        result_path = chart_system.generate_intelligent_dashboard(
            save_path=str(chart_path),
            show_chart=False
        )
        
        if result_path and Path(result_path).exists():
            file_size = Path(result_path).stat().st_size / 1024  # KB
            print(f"✅ 直接测试成功!")
            print(f"📁 图表路径: {result_path}")
            print(f"📏 文件大小: {file_size:.1f} KB")
            return True
        else:
            print(f"❌ 直接测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("🎮 CS2饰品智能分析图表集成测试")
    print("=" * 60)
    
    try:
        # 测试完整集成
        integration_success = await test_intelligent_chart_integration()
        
        # 测试直接图表系统
        direct_success = test_chart_system_directly()
        
        print(f"\n" + "=" * 60)
        print(f"🏁 测试总结:")
        print(f"   完整集成测试: {'✅ 成功' if integration_success else '❌ 失败'}")
        print(f"   直接系统测试: {'✅ 成功' if direct_success else '❌ 失败'}")
        
        if integration_success or direct_success:
            print(f"\n🎉 智能分析图表集成功能基本可用!")
            print(f"💡 请查看 data/analysis_results/25698/ 目录下的生成图表")
        else:
            print(f"\n❌ 智能分析图表集成功能需要调试")
            print(f"💡 请检查依赖模块和数据文件")
        
    except KeyboardInterrupt:
        print(f"\n🛑 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    import time
    asyncio.run(main())
