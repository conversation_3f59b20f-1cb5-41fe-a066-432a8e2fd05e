"""
平台价格数据访问对象

提供平台价格数据的数据库操作功能。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func
from contextlib import contextmanager
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.models.platform_price import PlatformPrice
from src.cs2_investment.dao.base_dao import BaseDAO


class PlatformPriceDAO(BaseDAO[PlatformPrice]):
    """平台价格数据访问对象"""

    def __init__(self, session: Optional[Session] = None):
        super().__init__(PlatformPrice)
        self.session = session  # 可选的外部session

    @contextmanager
    def _get_session(self):
        """获取session的上下文管理器"""
        if self.session:
            # 使用外部提供的session
            yield self.session
        else:
            # 使用BaseDAO的session管理
            from src.cs2_investment.config.database import get_db_session
            with get_db_session() as session:
                yield session
    
    def save_platform_price(self, 
                           item_id: str,
                           market_hash_name: str,
                           platform: str,
                           platform_item_id: str,
                           sell_price: float,
                           sell_count: int,
                           bidding_price: float,
                           bidding_count: int,
                           steamdt_update_time: int,
                           query_time: datetime) -> PlatformPrice:
        """
        保存平台价格数据
        
        Args:
            item_id: 饰品ID
            market_hash_name: 市场哈希名称
            platform: 平台名称
            platform_item_id: 平台商品ID
            sell_price: 在售价格
            sell_count: 在售数量
            bidding_price: 求购价格
            bidding_count: 求购数量
            steamdt_update_time: SteamDT更新时间戳
            query_time: 查询时间
            
        Returns:
            PlatformPrice: 保存的平台价格对象
        """
        with self._get_session() as session:
            # 检查是否已存在相同的记录
            existing = session.query(PlatformPrice).filter(
                and_(
                    PlatformPrice.item_id == item_id,
                    PlatformPrice.platform_name == platform,
                    PlatformPrice.steamdt_update_time == steamdt_update_time
                )
            ).first()

            if existing:
                # 更新现有记录
                existing.sell_price = sell_price
                existing.sell_count = sell_count
                existing.bidding_price = bidding_price
                existing.bidding_count = bidding_count
                existing.price = sell_price  # 兼容性字段
                existing.update_time = datetime.now()
                existing.query_time = query_time
                existing.is_active = True

                session.commit()
                return existing
            else:
                # 创建新记录
                platform_price = PlatformPrice(
                    item_id=item_id,
                    platform_enum=platform.lower().replace(' ', '_'),
                    platform_name=platform,
                    platform_item_id=platform_item_id,
                    market_hash_name=market_hash_name,
                    sell_price=sell_price,
                    sell_count=sell_count,
                    bidding_price=bidding_price,
                    bidding_count=bidding_count,
                    price=sell_price,  # 兼容性字段
                    steamdt_update_time=steamdt_update_time,
                    update_time=datetime.now(),
                    query_time=query_time,
                    is_active=True,
                    data_source='steamdt'
                )

                session.add(platform_price)
                session.commit()
                return platform_price
    
    def get_latest_prices_by_item(self, item_id: str) -> List[PlatformPrice]:
        """
        获取指定饰品的最新平台价格
        
        Args:
            item_id: 饰品ID
            
        Returns:
            List[PlatformPrice]: 最新平台价格列表
        """
        with self._get_session() as session:
            # 子查询：获取每个平台的最新更新时间
            subquery = session.query(
                PlatformPrice.platform_name,
                func.max(PlatformPrice.steamdt_update_time).label('max_update_time')
            ).filter(
                and_(
                    PlatformPrice.item_id == item_id,
                    PlatformPrice.is_active == True
                )
            ).group_by(PlatformPrice.platform_name).subquery()

            # 主查询：获取最新价格记录
            return session.query(PlatformPrice).join(
                subquery,
                and_(
                    PlatformPrice.platform_name == subquery.c.platform_name,
                    PlatformPrice.steamdt_update_time == subquery.c.max_update_time
                )
            ).filter(
                PlatformPrice.item_id == item_id
            ).all()
    
    def get_price_history_by_item_platform(self, 
                                         item_id: str, 
                                         platform: str, 
                                         days: int = 30) -> List[PlatformPrice]:
        """
        获取指定饰品在指定平台的价格历史
        
        Args:
            item_id: 饰品ID
            platform: 平台名称
            days: 历史天数
            
        Returns:
            List[PlatformPrice]: 价格历史列表
        """
        start_time = datetime.now() - timedelta(days=days)

        with self._get_session() as session:
            return session.query(PlatformPrice).filter(
                and_(
                    PlatformPrice.item_id == item_id,
                    PlatformPrice.platform_name == platform,
                    PlatformPrice.query_time >= start_time,
                    PlatformPrice.is_active == True
                )
            ).order_by(desc(PlatformPrice.query_time)).all()
    
    def get_platform_statistics(self, platform: str, days: int = 7) -> Dict[str, Any]:
        """
        获取平台统计信息
        
        Args:
            platform: 平台名称
            days: 统计天数
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        start_time = datetime.now() - timedelta(days=days)

        with self._get_session() as session:
            # 基础统计
            total_items = session.query(func.count(func.distinct(PlatformPrice.item_id))).filter(
                and_(
                    PlatformPrice.platform_name == platform,
                    PlatformPrice.query_time >= start_time,
                    PlatformPrice.is_active == True
                )
            ).scalar()

            # 价格统计
            price_stats = session.query(
                func.avg(PlatformPrice.sell_price).label('avg_price'),
                func.min(PlatformPrice.sell_price).label('min_price'),
                func.max(PlatformPrice.sell_price).label('max_price'),
                func.sum(PlatformPrice.sell_count).label('total_sell_count'),
                func.sum(PlatformPrice.bidding_count).label('total_bidding_count')
            ).filter(
                and_(
                    PlatformPrice.platform_name == platform,
                    PlatformPrice.query_time >= start_time,
                    PlatformPrice.is_active == True,
                    PlatformPrice.sell_price > 0
                )
            ).first()

            return {
                'platform': platform,
                'days': days,
                'total_items': total_items or 0,
                'avg_price': float(price_stats.avg_price) if price_stats.avg_price else 0,
                'min_price': float(price_stats.min_price) if price_stats.min_price else 0,
                'max_price': float(price_stats.max_price) if price_stats.max_price else 0,
                'total_sell_count': price_stats.total_sell_count or 0,
                'total_bidding_count': price_stats.total_bidding_count or 0
            }
    
    def get_items_by_market_hash_name(self, market_hash_names: List[str]) -> Dict[str, str]:
        """
        根据市场哈希名称获取饰品ID映射
        
        Args:
            market_hash_names: 市场哈希名称列表
            
        Returns:
            Dict[str, str]: 市场哈希名称到饰品ID的映射
        """
        from src.cs2_investment.models.item import Item

        with self._get_session() as session:
            results = session.query(
                Item.market_hash_name,
                Item.item_id
            ).filter(
                Item.market_hash_name.in_(market_hash_names)
            ).all()

            return {result.market_hash_name: result.item_id for result in results}
    
    def cleanup_old_data(self, days: int = 90) -> int:
        """
        清理旧数据
        
        Args:
            days: 保留天数
            
        Returns:
            int: 删除的记录数
        """
        cutoff_time = datetime.now() - timedelta(days=days)

        with self._get_session() as session:
            deleted_count = session.query(PlatformPrice).filter(
                PlatformPrice.query_time < cutoff_time
            ).delete()

            session.commit()
            return deleted_count
    
    def deactivate_old_prices(self, hours: int = 24) -> int:
        """
        将过期价格标记为无效
        
        Args:
            hours: 过期小时数
            
        Returns:
            int: 更新的记录数
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)

        with self._get_session() as session:
            updated_count = session.query(PlatformPrice).filter(
                and_(
                    PlatformPrice.query_time < cutoff_time,
                    PlatformPrice.is_active == True
                )
            ).update({'is_active': False})

            session.commit()
            return updated_count
    
    def get_all_platforms(self) -> List[str]:
        """
        获取所有平台列表
        
        Returns:
            List[str]: 平台名称列表
        """
        with self._get_session() as session:
            results = session.query(
                func.distinct(PlatformPrice.platform_name)
            ).filter(
                PlatformPrice.is_active == True
            ).all()

            return [result[0] for result in results if result[0]]
    
    def batch_save_platform_prices(self, price_data_list: List[Dict[str, Any]]) -> int:
        """
        批量保存平台价格数据
        
        Args:
            price_data_list: 价格数据列表
            
        Returns:
            int: 保存的记录数
        """
        saved_count = 0
        
        for price_data in price_data_list:
            try:
                self.save_platform_price(**price_data)
                saved_count += 1
            except Exception as e:
                # 记录错误但继续处理其他数据
                print(f"保存价格数据失败: {e}, 数据: {price_data}")
                continue
        
        return saved_count
