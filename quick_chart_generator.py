#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品分析图表快速生成器

简化版本，快速生成分析图表
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def quick_generate_charts(item_name: str, chart_type: str = "all"):
    """
    快速生成图表
    
    Args:
        item_name: 饰品名称
        chart_type: 图表类型 ("all", "dashboard", "technical")
    """
    print(f"🎯 开始为 {item_name} 生成图表...")
    print(f"📊 图表类型: {chart_type}")
    print("-" * 50)
    
    try:
        from generate_analysis_charts import AnalysisChartGenerator
        
        # 创建生成器
        generator = AnalysisChartGenerator()
        
        # 根据类型生成图表
        if chart_type == "all":
            result = generator.generate_all_charts(item_name)
            success = result.get('overall_success', False)
        elif chart_type == "dashboard":
            result = generator.generate_professional_dashboard(item_name)
            success = result.get('success', False)
        elif chart_type == "technical":
            result = generator.generate_technical_chart(item_name)
            success = result.get('success', False)
        else:
            print(f"❌ 不支持的图表类型: {chart_type}")
            return False
        
        if success:
            print(f"✅ 图表生成成功!")
            
            # 显示生成的文件
            if chart_type == "all" and 'charts' in result:
                for chart in result['charts']:
                    if chart['success']:
                        print(f"📈 {chart['chart_type']}: {chart['chart_path']}")
            elif 'chart_path' in result:
                print(f"📈 图表路径: {result['chart_path']}")
            
            return True
        else:
            print(f"❌ 图表生成失败")
            if 'error' in result:
                print(f"错误信息: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        return False


def list_available_items():
    """列出可用的饰品"""
    try:
        from generate_analysis_charts import AnalysisChartGenerator
        
        generator = AnalysisChartGenerator()
        items = generator.list_available_items()
        
        if items:
            print("📋 可用的饰品数据:")
            for i, item in enumerate(items[:20], 1):
                print(f"   {i:2d}. {item}")
            
            if len(items) > 20:
                print(f"   ... 还有 {len(items) - 20} 个饰品")
            
            return items
        else:
            print("❌ 未找到可用的饰品数据")
            return []
            
    except Exception as e:
        print(f"❌ 获取饰品列表失败: {e}")
        return []


def interactive_mode():
    """交互模式"""
    print("🎮 CS2饰品分析图表快速生成器")
    print("=" * 50)
    
    # 列出可用饰品
    items = list_available_items()
    
    if not items:
        print("\n💡 请确保 data/scraped_data 目录下有饰品数据")
        return
    
    print(f"\n💡 找到 {len(items)} 个可用饰品")
    
    try:
        while True:
            print("\n" + "-" * 50)
            print("请选择操作:")
            print("1. 输入饰品名称生成图表")
            print("2. 从列表选择饰品")
            print("3. 重新显示饰品列表")
            print("4. 退出")
            
            choice = input("\n请输入选择 (1-4): ").strip()
            
            if choice == "1":
                item_name = input("请输入饰品名称: ").strip()
                if item_name:
                    chart_type = input("图表类型 (all/dashboard/technical, 默认all): ").strip() or "all"
                    quick_generate_charts(item_name, chart_type)
                else:
                    print("❌ 饰品名称不能为空")
            
            elif choice == "2":
                print("\n📋 可用饰品:")
                display_count = min(len(items), 20)
                for i, item in enumerate(items[:display_count], 1):
                    print(f"   {i:2d}. {item}")
                
                try:
                    index = int(input(f"\n请选择饰品编号 (1-{display_count}): ")) - 1
                    if 0 <= index < display_count:
                        selected_item = items[index]
                        chart_type = input("图表类型 (all/dashboard/technical, 默认all): ").strip() or "all"
                        quick_generate_charts(selected_item, chart_type)
                    else:
                        print("❌ 无效的编号")
                except ValueError:
                    print("❌ 请输入有效的数字")
            
            elif choice == "3":
                list_available_items()
            
            elif choice == "4":
                print("👋 再见!")
                break
            
            else:
                print("❌ 无效的选择")
                
    except KeyboardInterrupt:
        print("\n🛑 用户中断操作")


def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行模式
        item_name = sys.argv[1]
        chart_type = sys.argv[2] if len(sys.argv) > 2 else "all"
        
        print(f"🚀 命令行模式")
        success = quick_generate_charts(item_name, chart_type)
        
        if success:
            print(f"\n🎉 完成!")
        else:
            print(f"\n❌ 失败!")
            sys.exit(1)
    else:
        # 交互模式
        interactive_mode()


if __name__ == "__main__":
    main()
