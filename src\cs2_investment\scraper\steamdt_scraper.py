"""
SteamDT数据抓取器

基于Playwright的SteamDT网站数据抓取实现
"""

import asyncio
import json
import time
import re
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from urllib.parse import unquote
from playwright.async_api import async_playwright, <PERSON>, Browser

from .data_models import (
    ItemInfo, TrendData, TrendDataPoint,
    KlineData, KlineDataPoint, ScrapingResult
)

# 使用项目统一日志系统
from ..utils.logger import get_logger
logger = get_logger(__name__)


class SteamDTScraper:
    """SteamDT数据抓取器"""

    def __init__(self):
        self.base_url = "https://steamdt.com"
        self.api_base = "https://sdt-api.ok-skins.com"
        self.playwright = None  # 添加playwright实例保存
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        # 设置浏览器窗口大小，确保图表区域可见
        self.viewport_size = {"width": 1920, "height": 1080}
        
        # API端点
        self.trend_api = f"{self.api_base}/user/steam/type-trend/v2/item/details"
        self.kline_api = f"{self.api_base}/user/steam/category/v1/kline"
        
        # K线类型映射
        self.kline_types = {
            'hourly': {'type': 1, 'name': '时K'},
            'daily': {'type': 2, 'name': '日K'}, 
            'weekly': {'type': 3, 'name': '周K'}
        }
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
        
    async def start(self):
        """启动浏览器"""
        self.playwright = await async_playwright().start()  # 保存playwright实例
        self.browser = await self.playwright.chromium.launch(
            headless=False,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        self.page = await self.browser.new_page()

        # 设置浏览器窗口大小，确保图表区域可见 - 关键修复！
        await self.page.set_viewport_size(self.viewport_size)

        # 设置用户代理
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                         '(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        })

        logger.info("浏览器启动成功")

    async def close_popup_if_exists(self):
        """关闭可能存在的弹窗（简化版本，与排行榜抓取器完全一致）"""
        try:
            # 使用与排行榜抓取器完全相同的逻辑
            close_button = await self.page.query_selector('button[aria-label="关闭此对话框"]')
            if close_button:
                await close_button.click()
                await asyncio.sleep(2)
                logger.info("✅ 已关闭弹窗")
                return True
        except Exception as e:
            logger.debug(f"关闭弹窗时出现异常（可能不存在弹窗）: {e}")
        return False

    async def close(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
                self.page = None
            if self.browser:
                await self.browser.close()
                self.browser = None
            if self.playwright:
                await self.playwright.stop()  # 正确关闭playwright实例
                self.playwright = None
            logger.info("浏览器已关闭")
        except Exception as e:
            logger.warning(f"关闭浏览器时出现异常: {e}")
        
    def extract_item_id_from_url(self, url: str) -> Optional[str]:
        """从URL中提取饰品ID"""
        try:
            # 访问页面获取itemId
            # 这里需要通过页面分析获取，暂时返回None
            return None
        except Exception as e:
            logger.error(f"提取饰品ID失败: {e}")
            return None
            
    async def get_item_info_from_page(self, url: str) -> Optional[ItemInfo]:
        """从页面获取饰品基本信息（简化版本）"""
        try:
            logger.info("🌐 正在访问SteamDT页面...")
            await self.page.goto(url)  # 简化页面访问
            await asyncio.sleep(5)  # 固定等待5秒

            # 关闭可能存在的弹窗
            await self.close_popup_if_exists()

            logger.info("✅ 页面访问完成")

            # 注意：平台和时间范围设置在get_item_id_from_network中统一处理
            
            # 提取饰品名称
            name_element = await self.page.query_selector('[class*="item-name"]')
            name = await name_element.inner_text() if name_element else "未知饰品"
            
            # 提取当前价格
            price_element = await self.page.query_selector('[class*="current-price"]')
            price_text = await price_element.inner_text() if price_element else "¥0"
            current_price = float(re.findall(r'[\d.]+', price_text)[0]) if re.findall(r'[\d.]+', price_text) else 0.0
            
            # 提取价格变化
            change_element = await self.page.query_selector('[class*="price-change"]')
            change_text = await change_element.inner_text() if change_element else "0%"
            price_change_percent = float(re.findall(r'-?[\d.]+', change_text)[0]) if re.findall(r'-?[\d.]+', change_text) else 0.0
            
            # 从URL中提取market_hash_name
            market_hash_name = unquote(url.split('/')[-1])
            
            return ItemInfo(
                item_id="",  # 需要从API响应中获取
                name=name,
                market_hash_name=market_hash_name,
                wear="",
                rarity="",
                current_price=current_price,
                price_change=0.0,
                price_change_percent=price_change_percent,
                volume_24h=0,
                inventory_count=0
            )
            
        except Exception as e:
            logger.error(f"获取饰品信息失败: {e}")
            return None

    async def _set_platform_and_timerange(self):
        """设置平台为总览和时间范围为近3个月 - 使用用户提供的精确CSS选择器"""
        try:
            logger.info("使用精确CSS选择器设置平台为总览和时间范围为近3个月...")

            # 等待页面完全加载
            await asyncio.sleep(5)

            # 精确检查当前选择器状态（而不是页面内容）
            platform_ok = False
            time_ok = False

            try:
                # 检查平台选择器的实际状态
                platform_element = await self.page.query_selector('.el-select.platform-select .el-select__selection')
                if platform_element:
                    platform_text = await platform_element.text_content()
                    platform_ok = platform_text and "总览" in platform_text
                    logger.info(f"平台选择器状态: '{platform_text}' -> 总览={platform_ok}")
                else:
                    logger.info("未找到平台选择器元素")

                # 检查时间范围选择器的实际状态（尝试多个选择器）
                time_selectors = [
                    '.charts-menu .mr-20 > div > div',
                    'div.mr-20 > div > div',
                    '.mr-20 > div > div',
                    '.el-select__selection:has(.el-select__placeholder):has-text("近")',
                ]

                time_element = None
                time_text = ""
                for selector in time_selectors:
                    try:
                        element = await self.page.query_selector(selector)
                        if element:
                            text = await element.text_content()
                            if text and text.strip():  # 确保有实际内容
                                time_element = element
                                time_text = text
                                break
                    except Exception as e:
                        logger.debug(f"时间选择器 {selector} 检查失败: {e}")

                if time_element:
                    time_ok = time_text and "近3个月" in time_text
                    logger.info(f"时间选择器状态: '{time_text}' -> 近3个月={time_ok}")
                else:
                    logger.info("未找到时间选择器元素")

            except Exception as e:
                logger.warning(f"状态检查失败: {e}")

            if platform_ok and time_ok:
                logger.info("✅ 选择器已经是正确状态，无需额外设置")
                return True
            else:
                logger.info(f"需要设置选择器: 平台={platform_ok}, 时间={time_ok}")

            # 设置平台为"总览"（使用用户提供的精确选择器）
            if not platform_ok:
                logger.info("正在设置平台为总览...")

                # 用户提供的精确平台选择器
                platform_selector = '#__nuxt > div > div.common-container > div > div.mt-10.flex > div.relative.rounded-\\[10px\\].bg-white.pt-20 > div:nth-child(3) > div > div.charts-menu.flex.h-60.items-center.justify-between > div > div.filters.flex.items-center > div.el-select.platform-select.ml-10.mr-20.w-100 > div > div.el-select__selection'

                # 备用简化选择器
                platform_selectors = [
                    platform_selector,  # 用户提供的完整选择器
                    '.el-select.platform-select .el-select__selection',  # 简化版本1
                    '.platform-select .el-select__selection',  # 简化版本2
                    '.el-select__selection',  # 通用版本
                ]

                platform_set = False
                for selector in platform_selectors:
                    try:
                        logger.info(f"尝试平台选择器: {selector}")
                        await self.page.wait_for_selector(selector, timeout=5000)
                        element = await self.page.query_selector(selector)
                        if element:
                            # 确保元素可见
                            await element.scroll_into_view_if_needed()
                            await asyncio.sleep(0.5)

                            # 点击打开下拉菜单
                            await element.click()
                            await asyncio.sleep(2)
                            logger.info("✓ 平台下拉菜单已打开")

                            # 使用CSS选择器选择总览选项（避免Role选择器）
                            overview_selectors = [
                                '.el-select-dropdown__item:has-text("总览")',
                                '.el-option:has-text("总览")',
                                '[data-value="总览"]',
                                'li:has-text("总览")',
                            ]

                            option_selected = False
                            for option_selector in overview_selectors:
                                try:
                                    option_element = await self.page.query_selector(option_selector)
                                    if option_element:
                                        await option_element.click()
                                        await asyncio.sleep(2)
                                        logger.info("✅ 平台已设置为总览")
                                        platform_set = True
                                        option_selected = True
                                        break
                                except Exception as e:
                                    logger.debug(f"尝试总览选项选择器 {option_selector} 失败: {e}")

                            if option_selected:
                                break
                            else:
                                # 如果没有找到选项，尝试按文本查找
                                try:
                                    await self.page.click('text="总览"')
                                    await asyncio.sleep(2)
                                    logger.info("✅ 平台已设置为总览（文本选择）")
                                    platform_set = True
                                    break
                                except Exception as e:
                                    logger.debug(f"文本选择总览失败: {e}")

                    except Exception as e:
                        logger.debug(f"平台选择器 {selector} 失败: {e}")

                if not platform_set:
                    logger.warning("平台设置失败")

            # 设置时间范围为"近3个月"（使用用户提供的精确选择器）
            if not time_ok:
                logger.info("正在设置时间范围为近3个月...")

                # 用户提供的精确时间选择器
                time_selector = '#__nuxt > div > div.common-container > div > div.mt-10.flex > div.relative.rounded-\\[10px\\].bg-white.pt-20 > div:nth-child(3) > div > div.charts-menu.flex.h-60.items-center.justify-between > div > div.mr-20 > div > div'

                # 备用简化选择器
                time_selectors = [
                    time_selector,  # 用户提供的完整选择器
                    '.charts-menu .mr-20 > div > div',  # 简化版本1
                    'div.mr-20 > div > div',  # 简化版本2
                    '.mr-20 > div > div',  # 简化版本3
                ]

                time_set = False
                for selector in time_selectors:
                    try:
                        logger.info(f"尝试时间选择器: {selector}")
                        await self.page.wait_for_selector(selector, timeout=5000)
                        element = await self.page.query_selector(selector)
                        if element:
                            # 确保元素可见
                            await element.scroll_into_view_if_needed()
                            await asyncio.sleep(0.5)

                            # 点击打开下拉菜单
                            await element.click()
                            await asyncio.sleep(2)
                            logger.info("✓ 时间范围下拉菜单已打开")

                            # 使用多种方法选择近3个月选项
                            option_selected = False

                            # 方法1: 遍历所有下拉选项
                            try:
                                dropdown_items = await self.page.query_selector_all('.el-select-dropdown__item')
                                logger.info(f"找到 {len(dropdown_items)} 个时间选项")

                                for item in dropdown_items:
                                    item_text = await item.text_content()
                                    if item_text and "近3个月" in item_text:
                                        await item.click()
                                        await asyncio.sleep(2)
                                        logger.info("✅ 时间范围已设置为近3个月（遍历选项）")
                                        time_set = True
                                        option_selected = True
                                        break
                            except Exception as e:
                                logger.debug(f"遍历选项方法失败: {e}")

                            # 方法2: CSS选择器
                            if not option_selected:
                                three_months_selectors = [
                                    '.el-select-dropdown__item:has-text("近3个月")',
                                    '.el-option:has-text("近3个月")',
                                    '[data-value="近3个月"]',
                                    'li:has-text("近3个月")',
                                ]

                                for option_selector in three_months_selectors:
                                    try:
                                        option_element = await self.page.query_selector(option_selector)
                                        if option_element:
                                            await option_element.click()
                                            await asyncio.sleep(2)
                                            logger.info("✅ 时间范围已设置为近3个月（CSS选择器）")
                                            time_set = True
                                            option_selected = True
                                            break
                                    except Exception as e:
                                        logger.debug(f"CSS选择器 {option_selector} 失败: {e}")

                            # 方法3: 文本选择
                            if not option_selected:
                                try:
                                    await self.page.click('text="近3个月"', timeout=5000)
                                    await asyncio.sleep(2)
                                    logger.info("✅ 时间范围已设置为近3个月（文本选择）")
                                    time_set = True
                                    option_selected = True
                                except Exception as e:
                                    logger.debug(f"文本选择近3个月失败: {e}")

                            if option_selected:
                                break

                    except Exception as e:
                        logger.debug(f"时间选择器 {selector} 失败: {e}")

                if not time_set:
                    logger.warning("时间范围设置失败")

            # 等待设置生效
            await asyncio.sleep(3)
            logger.info("平台和时间范围设置完成")
            return True

        except Exception as e:
            logger.error(f"设置平台和时间范围失败: {e}")
            return False

    async def _force_set_platform_and_timerange(self):
        """强制设置平台和时间范围（使用JavaScript）"""
        try:
            logger.info("使用JavaScript强制设置平台和时间范围...")

            # 等待页面加载
            await asyncio.sleep(3)

            # 使用JavaScript设置
            await self.page.evaluate("""
                // 等待一下确保页面加载完成
                setTimeout(() => {
                    try {
                        // 1. 点击走势图标签
                        const trendTab = document.querySelector('tab[role="tab"]:has-text("走势图"), [role="tab"]:has-text("走势图")');
                        if (trendTab) {
                            trendTab.click();
                            console.log('✓ 已切换到走势图标签');
                        }

                        setTimeout(() => {
                            // 2. 设置时间范围为近3个月
                            const timeSelectors = [
                                'div:has-text("近1个月")',
                                'div:has-text("近3个月")',
                                '[class*="time"] select',
                                'select:has(option:has-text("近3个月"))'
                            ];

                            for (const selector of timeSelectors) {
                                try {
                                    const timeElement = document.querySelector(selector);
                                    if (timeElement) {
                                        timeElement.click();
                                        setTimeout(() => {
                                            const threeMonthsOption = document.querySelector('option:has-text("近3个月")');
                                            if (threeMonthsOption) {
                                                threeMonthsOption.click();
                                                console.log('✓ 时间范围已设置为近3个月');
                                                return;
                                            }
                                        }, 500);
                                        break;
                                    }
                                } catch (e) {
                                    console.log('尝试时间选择器失败:', e);
                                }
                            }
                        }, 1000);

                    } catch (e) {
                        console.log('JavaScript设置失败:', e);
                    }
                }, 1000);
            """)

            await asyncio.sleep(3)
            logger.info("JavaScript设置完成")

        except Exception as e:
            logger.error(f"JavaScript强制设置失败: {e}")

    async def get_item_id_from_network(self, url: str) -> Optional[str]:
        """通过网络请求获取饰品ID"""
        try:
            # 监听网络请求
            item_id = None

            async def handle_response(response):
                nonlocal item_id
                if 'sdt-api.ok-skins.com' in response.url and 'item' in response.url:
                    try:
                        data = await response.json()
                        if 'data' in data and 'itemId' in data['data']:
                            item_id = str(data['data']['itemId'])
                    except:
                        pass

            self.page.on('response', handle_response)

            # 访问页面触发API请求
            await self.page.goto(url, wait_until='networkidle')
            await asyncio.sleep(3)

            # 关闭可能存在的弹窗
            await self.close_popup_if_exists()

            # 确保设置平台和时间范围
            success = await self._set_platform_and_timerange()
            if not success:
                logger.warning("CSS选择器设置失败，尝试JavaScript强制设置...")
                await self._force_set_platform_and_timerange()

            self.page.remove_listener('response', handle_response)
            return item_id

        except Exception as e:
            logger.error(f"获取饰品ID失败: {e}")
            return None

    async def fetch_trend_data(self, item_id: str) -> Optional[TrendData]:
        """通过监听接口获取走势数据（近3个月）"""
        try:
            trend_data = None

            async def handle_trend_response(response):
                nonlocal trend_data
                if 'type-trend/v2/item/details' in response.url:
                    try:
                        data = await response.json()
                        logger.info(f"捕获趋势数据API响应: {response.status}")
                        if response.status == 200 and data:
                            trend_data = data
                    except Exception as e:
                        logger.debug(f"解析趋势数据响应失败: {e}")

            # 设置监听器
            self.page.on('response', handle_trend_response)

            # 等待一下确保监听器设置完成
            await asyncio.sleep(0.5)

            # 触发趋势数据请求 - 通过切换时间范围来触发API调用
            try:
                # 先切换到近1个月，再切换到近3个月，确保触发API调用
                time_selector = '#__nuxt > div > div.common-container > div > div.mt-10.flex > div.relative.rounded-\\[10px\\].bg-white.pt-20 > div:nth-child(3) > div > div.charts-menu.flex.h-60.items-center.justify-between > div > div.mr-20 > div > div'

                # 点击时间选择器
                await self.page.wait_for_selector(time_selector, timeout=5000)
                await self.page.click(time_selector)
                await asyncio.sleep(1)

                # 选择近3个月
                await self.page.click('text="近3个月"')
                await asyncio.sleep(3)  # 等待API响应

            except Exception as e:
                logger.debug(f"触发趋势数据请求失败: {e}")

            # 移除监听器
            self.page.remove_listener('response', handle_trend_response)

            # 处理获取到的数据
            if trend_data:
                return self._parse_trend_data(trend_data, item_id)
            else:
                logger.warning("未能获取到趋势数据")
                return None

        except Exception as e:
            logger.error(f"获取走势数据失败: {e}")
            return None

    def _parse_trend_data(self, raw_data: dict, item_id: str, time_range: str = '近6个月') -> Optional[TrendData]:
        """解析趋势数据 - 直接保存原始数据，不做处理"""
        try:
            # 直接保存原始数据，不做任何处理
            if 'data' in raw_data:
                trend_data = raw_data['data']

                if isinstance(trend_data, list) and len(trend_data) > 0:
                    logger.info(f"成功解析{time_range}趋势数据: {len(trend_data)} 个原始数据点")
                    return TrendData(
                        item_id=item_id,
                        platform='ALL',
                        time_range=time_range,
                        data_points=[],  # 不处理数据点，避免时间错误
                        raw_data=trend_data,  # 保存原始数据
                        collected_at=datetime.now()
                    )
                else:
                    logger.warning(f"{time_range}趋势数据为空")
                    return None
            else:
                logger.warning(f"{time_range}趋势数据响应中没有data字段")
                return None

        except Exception as e:
            logger.error(f"解析{time_range}趋势数据失败: {e}")
            return None

    async def fetch_kline_data(self, item_id: str, kline_type: str) -> Optional[KlineData]:
        """通过监听接口获取K线数据"""
        try:
            if kline_type not in self.kline_types:
                logger.error(f"不支持的K线类型: {kline_type}")
                return None

            type_config = self.kline_types[kline_type]
            kline_data = None

            async def handle_kline_response(response):
                nonlocal kline_data
                if 'kline' in response.url and f"type={type_config['type']}" in response.url:
                    try:
                        data = await response.json()
                        logger.info(f"捕获{kline_type}K线数据API响应: {response.status}")
                        if response.status == 200 and data:
                            kline_data = data
                    except Exception as e:
                        logger.debug(f"解析K线数据响应失败: {e}")

            # 设置监听器
            self.page.on('response', handle_kline_response)

            # 等待一下确保监听器设置完成
            await asyncio.sleep(0.5)

            # 触发K线数据请求 - 通过切换到K线图标签
            try:
                # 点击K线图标签
                await self.page.click('tab[role="tab"]:has-text("K线图")')
                await asyncio.sleep(2)  # 等待K线图加载

                # 如果需要特定类型的K线，可能需要额外操作
                if kline_type != 'daily':
                    # 这里可以添加切换K线类型的逻辑
                    pass

            except Exception as e:
                logger.debug(f"触发K线数据请求失败: {e}")

            # 移除监听器
            self.page.remove_listener('response', handle_kline_response)

            # 处理获取到的数据
            if kline_data:
                return self._parse_kline_data(kline_data, item_id, kline_type)
            else:
                logger.warning(f"未能获取到{kline_type}K线数据")
                return None

        except Exception as e:
            logger.error(f"获取{kline_type}K线数据失败: {e}")
            return None

    def _parse_kline_data(self, raw_data: dict, item_id: str, kline_type: str) -> Optional[KlineData]:
        """解析K线数据 - 直接保存原始数据，不做时间处理"""
        try:
            # 添加调试日志
            logger.info(f"🔍 开始解析{kline_type}数据，原始数据类型: {type(raw_data)}")

            # 直接保存原始数据，不做任何处理
            if 'data' in raw_data:
                kline_data = raw_data['data']
                logger.info(f"🔍 {kline_type}数据字段存在，数据类型: {type(kline_data)}, 长度: {len(kline_data) if isinstance(kline_data, list) else 'N/A'}")

                if isinstance(kline_data, list) and len(kline_data) > 0:
                    logger.info(f"成功解析{kline_type}K线数据: {len(kline_data)} 个原始数据点")
                    return KlineData(
                        item_id=item_id,
                        kline_type=kline_type,
                        platform='ALL',
                        data_points=[],  # 不处理数据点，避免时间错误
                        raw_data=kline_data,  # 保存原始数据
                        collected_at=datetime.now()
                    )
                else:
                    logger.warning(f"❌ {kline_type}K线数据为空或格式错误: 类型={type(kline_data)}, 内容={kline_data}")
                    return None
            else:
                logger.warning(f"❌ {kline_type}K线数据响应中没有data字段，可用字段: {list(raw_data.keys()) if isinstance(raw_data, dict) else 'N/A'}")
                return None

        except Exception as e:
            logger.error(f"❌ 解析{kline_type}K线数据失败: {e}")
            logger.error(f"原始数据: {raw_data}")
            return None

    async def _fetch_single_kline_data_old(self, item_id: str, kline_type: str, url: str) -> Optional[KlineData]:
        """获取单次K线数据"""
        try:
            response = await self.page.evaluate(f"""
                fetch('{url}', {{
                    method: 'GET',
                    headers: {{
                        'Accept': 'application/json'
                    }}
                }}).then(r => r.json())
            """)

            if response and response.get('success') and response.get('data'):
                data_points = []
                kline_data = response['data']

                # K线数据是数组格式: [timestamp, open, high, low, close, volume, amount]
                for point in kline_data:
                    if isinstance(point, list) and len(point) >= 6:
                        try:
                            # 安全转换时间戳
                            timestamp_val = point[0]
                            if timestamp_val is None:
                                continue
                            timestamp = int(timestamp_val)
                            date_str = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

                            # 安全转换价格和成交量数据
                            open_price = float(point[1]) if point[1] is not None else 0.0
                            high_price = float(point[2]) if point[2] is not None else 0.0
                            low_price = float(point[3]) if point[3] is not None else 0.0
                            close_price = float(point[4]) if point[4] is not None else 0.0
                            volume = int(point[5]) if point[5] is not None else 0

                            data_points.append(KlineDataPoint(
                                timestamp=timestamp,
                                open_price=open_price,
                                high_price=high_price,
                                low_price=low_price,
                                close_price=close_price,
                                volume=volume,
                                date_str=date_str
                            ))
                        except (ValueError, TypeError) as e:
                            logger.warning(f"跳过无效的K线数据点: {point}, 错误: {e}")
                            continue

                return KlineData(
                    item_id=item_id,
                    kline_type=self.kline_types[kline_type]['name'],
                    platform='ALL',
                    data_points=data_points,
                    collected_at=datetime.now()
                )

        except Exception as e:
            logger.error(f"获取单次K线数据失败: {e}")
            return None

    async def _fetch_daily_kline_data(self, item_id: str, timestamp: int) -> Optional[KlineData]:
        """获取日K数据（需要合并两次响应）"""
        try:
            type_val = 2  # 日K类型

            # 第一次请求
            url1 = f"{self.kline_api}?timestamp={timestamp}&type={type_val}&maxTime=&typeVal={item_id}&platform=ALL&specialStyle="
            response1 = await self.page.evaluate(f"""
                fetch('{url1}', {{
                    method: 'GET',
                    headers: {{
                        'Accept': 'application/json'
                    }}
                }}).then(r => r.json())
            """)

            await asyncio.sleep(0.5)  # 短暂延迟

            # 第二次请求（通常会有maxTime参数）
            max_time = 1745510400  # 根据实际观察到的值
            url2 = f"{self.kline_api}?timestamp={timestamp + 100}&type={type_val}&maxTime={max_time}&typeVal={item_id}&platform=ALL&specialStyle="
            response2 = await self.page.evaluate(f"""
                fetch('{url2}', {{
                    method: 'GET',
                    headers: {{
                        'Accept': 'application/json'
                    }}
                }}).then(r => r.json())
            """)

            # 合并两次响应的数据
            all_data_points = []

            # 处理第一次响应
            if response1 and response1.get('success') and response1.get('data'):
                for point in response1['data']:
                    if isinstance(point, list) and len(point) >= 6:
                        try:
                            timestamp_val = point[0]
                            if timestamp_val is None:
                                continue
                            timestamp = int(timestamp_val)
                            date_str = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

                            all_data_points.append(KlineDataPoint(
                                timestamp=timestamp,
                                open_price=float(point[1]) if point[1] is not None else 0.0,
                                high_price=float(point[2]) if point[2] is not None else 0.0,
                                low_price=float(point[3]) if point[3] is not None else 0.0,
                                close_price=float(point[4]) if point[4] is not None else 0.0,
                                volume=int(point[5]) if point[5] is not None else 0,
                                date_str=date_str
                            ))
                        except (ValueError, TypeError) as e:
                            logger.warning(f"跳过无效的日K数据点(第一次响应): {point}, 错误: {e}")
                            continue

            # 处理第二次响应
            if response2 and response2.get('success') and response2.get('data'):
                for point in response2['data']:
                    if isinstance(point, list) and len(point) >= 6:
                        try:
                            timestamp_val = point[0]
                            if timestamp_val is None:
                                continue
                            timestamp = int(timestamp_val)
                            date_str = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')

                            all_data_points.append(KlineDataPoint(
                                timestamp=timestamp,
                                open_price=float(point[1]) if point[1] is not None else 0.0,
                                high_price=float(point[2]) if point[2] is not None else 0.0,
                                low_price=float(point[3]) if point[3] is not None else 0.0,
                                close_price=float(point[4]) if point[4] is not None else 0.0,
                                volume=int(point[5]) if point[5] is not None else 0,
                                date_str=date_str
                            ))
                        except (ValueError, TypeError) as e:
                            logger.warning(f"跳过无效的日K数据点(第二次响应): {point}, 错误: {e}")
                            continue

            # 按时间戳排序（确保数据顺序正确）
            all_data_points.sort(key=lambda x: x.timestamp)

            return KlineData(
                item_id=item_id,
                kline_type='日K',
                platform='ALL',
                data_points=all_data_points,
                collected_at=datetime.now()
            )

        except Exception as e:
            logger.error(f"获取日K数据失败: {e}")
            return None

    async def scrape_item_data(self, item_url: str, data_requirements: dict = None) -> ScrapingResult:
        """抓取单个饰品的数据 - 支持按需抓取"""
        try:
            # 设置默认数据需求（全部抓取）
            if data_requirements is None:
                data_requirements = {
                    'daily_kline_1': True,
                    'daily_kline_2': True,
                    'weekly_kline': True,
                    'hourly_kline': True,
                    'trend_data_3m': True,
                    'trend_data_6m': True
                }

            logger.info(f"开始抓取饰品数据: {item_url}")
            logger.info(f"数据需求: {[k for k, v in data_requirements.items() if v]}")

            # 存储监听到的数据
            captured_data = {
                'trend_data_3m': None,  # 3个月趋势数据（小时级）
                'trend_data_6m': None,  # 6个月趋势数据（日级）
                'kline_data_hourly': None,  # 时K数据
                'kline_data_daily_1': None,   # 日K数据第一次响应
                'kline_data_daily_2': None,   # 日K数据第二次响应
                'kline_data_weekly': None,  # 周K数据
                'item_id': None
            }

            # 设置统一的API监听器
            async def handle_api_response(response):
                try:
                    # 记录所有API响应的URL
                    if 'sdt-api.ok-skins.com' in response.url:
                        logger.info(f"🌐 双重抓取API响应URL: {response.url}")
                        logger.info(f"📡 响应状态: {response.status}")

                        if response.status == 200:
                            data = await response.json()

                            # 获取item_id
                            if 'item' in response.url and 'data' in data and 'itemId' in data['data']:
                                captured_data['item_id'] = str(data['data']['itemId'])
                                logger.info(f"🆔 捕获饰品ID: {captured_data['item_id']}")

                            # 趋势数据API - 根据当前时间范围设置分别保存
                            elif 'type-trend/v2/item/details' in response.url:
                                data_count = len(data.get('data', []))
                                current_range = getattr(self, '_current_timerange', 'unknown')
                                set_time = getattr(self, '_timerange_set_time', 0)
                                current_time = asyncio.get_event_loop().time()
                                time_since_set = current_time - set_time

                                logger.info(f"📈 双重抓取趋势数据API响应: {data_count}个数据点")
                                logger.info(f"🔄 通信状态: timerange={current_range}, 设置后经过={time_since_set:.1f}秒")

                                # 验证数据时间范围并获取实际天数
                                actual_days = 0
                                if data.get('data') and len(data['data']) > 0:
                                    timerange_label = "3个月" if current_range == '3m' else "6个月" if current_range == '6m' else "未知"
                                    actual_days = await self._validate_and_log_trend_data_time(data['data'], f"双重抓取-{timerange_label}")

                                # 改进的通信机制：只保存时间跨度合理的数据
                                if hasattr(self, '_current_timerange'):
                                    if self._current_timerange == '3m' and not captured_data['trend_data_3m']:
                                        # 保存3个月趋势数据（移除时间跨度验证，支持新品饰品）
                                        captured_data['trend_data_3m'] = data
                                        logger.info(f"✅ 保存3个月趋势数据: {data_count}个数据点, {actual_days}天跨度")
                                    elif self._current_timerange == '6m' and not captured_data['trend_data_6m']:
                                        # 保存6个月趋势数据（移除时间跨度验证，支持新品饰品）
                                        captured_data['trend_data_6m'] = data
                                        logger.info(f"✅ 保存6个月趋势数据: {data_count}个数据点, {actual_days}天跨度")
                                    else:
                                        logger.info(f"⏳ 已有{self._current_timerange}数据，忽略重复响应")

                            # K线数据API - 扩大匹配范围，包括SteamDT特有的API
                            elif any(keyword in response.url.lower() for keyword in ['kline', 'candle', 'ohlc', 'chart-data', 'price-history', 'k-line', 'candlestick']):
                                logger.info(f"📊 双重抓取K线数据API响应: {response.url}")

                                # 验证K线数据时间范围
                                if data.get('data') and len(data['data']) > 0:
                                    current_range = getattr(self, '_current_timerange', 'unknown')
                                    timerange_label = "3个月" if current_range == '3m' else "6个月" if current_range == '6m' else "未知"
                                    await self._validate_and_log_kline_data_time(data['data'], f"双重抓取-{timerange_label}")

                                # 根据URL参数判断K线类型
                                if 'type=1' in response.url:  # 时K
                                    captured_data['kline_data_hourly'] = data
                                    logger.info("✅ 捕获时K数据")
                                elif 'type=2' in response.url:  # 日K
                                    # 日K数据会响应两次，分别保存
                                    if captured_data['kline_data_daily_1'] is None:
                                        captured_data['kline_data_daily_1'] = data
                                        logger.info("✅ 捕获日K数据（第1次）")
                                    elif captured_data['kline_data_daily_2'] is None:
                                        captured_data['kline_data_daily_2'] = data
                                        logger.info("✅ 捕获日K数据（第2次）")
                                    else:
                                        logger.info("⏳ 日K数据已捕获两次，忽略后续响应")
                                elif 'type=3' in response.url:  # 周K
                                    # 只在第一次响应时保存周K数据，避免被空响应覆盖
                                    if captured_data['kline_data_weekly'] is None:
                                        captured_data['kline_data_weekly'] = data
                                        logger.info(f"✅ 捕获周K数据（第1次），数据类型: {type(data)}")
                                        if isinstance(data, dict) and 'data' in data:
                                            logger.info(f"🔍 周K数据内容: data字段类型={type(data['data'])}, 长度={len(data['data']) if isinstance(data['data'], list) else 'N/A'}")
                                        else:
                                            logger.warning(f"⚠️ 周K数据格式异常: {data}")
                                    else:
                                        logger.info(f"⏳ 周K数据已捕获，忽略后续响应（避免覆盖）")
                                        if isinstance(data, dict) and 'data' in data:
                                            logger.info(f"🔍 被忽略的周K数据长度: {len(data['data']) if isinstance(data['data'], list) else 'N/A'}")
                                else:
                                    # 默认存储为日K第一次
                                    if captured_data['kline_data_daily_1'] is None:
                                        captured_data['kline_data_daily_1'] = data
                                        logger.info("✅ 捕获K线数据（默认为日K第1次）")
                                    elif captured_data['kline_data_daily_2'] is None:
                                        captured_data['kline_data_daily_2'] = data
                                        logger.info("✅ 捕获K线数据（默认为日K第2次）")

                            # SteamDT可能的K线API模式
                            elif '/chart/' in response.url or '/kdata/' in response.url or 'type=2' in response.url:
                                logger.info(f"📊 发现SteamDT K线API: {response.url}")
                                if data.get('data') and isinstance(data['data'], list) and len(data['data']) > 0:
                                    # 检查是否为K线数据格式
                                    first_item = data['data'][0]
                                    if isinstance(first_item, list) and len(first_item) >= 5:
                                        logger.info(f"✅ 确认为K线数据格式")
                                        captured_data['kline_data'] = data

                            # 其他可能包含价格历史数据的API
                            elif any(keyword in response.url.lower() for keyword in ['price', 'history']) and 'data' in data and isinstance(data['data'], list):
                                # 检查数据格式是否像K线数据（包含OHLC格式）
                                if len(data['data']) > 0:
                                    first_item = data['data'][0]
                                    if isinstance(first_item, list) and len(first_item) >= 5:
                                        logger.info(f"📊 发现疑似K线数据API: {response.url}")
                                        captured_data['kline_data'] = data
                        else:
                            logger.warning(f"❌ API响应状态异常: {response.status}")

                except Exception as e:
                    logger.error(f"处理双重抓取API响应失败: {e}")
                    logger.error(f"响应URL: {response.url}")
                    logger.error(f"响应状态: {response.status}")

            # 设置监听器
            self.page.on('response', handle_api_response)
            self._settings_completed = False

            # 1. 访问页面（简化版本，与排行榜抓取器保持一致）
            logger.info("🌐 正在访问SteamDT页面...")
            await self.page.goto(item_url)  # 简化页面访问，不使用wait_until
            await asyncio.sleep(5)  # 固定等待5秒，与排行榜抓取器一致

            # 关闭可能存在的弹窗
            await self.close_popup_if_exists()

            logger.info("✅ 页面访问完成")

            # 等待item_id被捕获
            for _ in range(10):  # 最多等待5秒
                if captured_data['item_id']:
                    break
                await asyncio.sleep(0.5)

            if not captured_data['item_id']:
                self.page.remove_listener('response', handle_api_response)
                return ScrapingResult(
                    success=False,
                    error_message="无法获取饰品ID",
                    item_info=None
                )

            logger.info(f"饰品ID: {captured_data['item_id']}")

            # 2. 按需获取趋势数据
            need_3m = data_requirements.get('trend_data_3m', False)
            need_6m = data_requirements.get('trend_data_6m', False)

            if need_3m or need_6m:
                logger.info(f"🎯 开始按需获取趋势数据: 3个月={need_3m}, 6个月={need_6m}")

                if need_3m:
                    # 2.1 获取3个月数据
                    logger.info("📊 第一步：选择3个月，保存3个月数据")
                    self._current_timerange = '3m'
                    await self._set_platform_and_timerange_specific('近3个月')
                    await asyncio.sleep(3)  # 等待3个月数据加载

                if need_6m:
                    # 2.2 获取6个月数据
                    logger.info("📊 第二步：选择6个月，保存6个月数据")
                    self._current_timerange = '6m'
                    await self._set_platform_and_timerange_specific('近6个月')
                    await asyncio.sleep(3)  # 等待6个月数据加载
            else:
                logger.info("⏭️ 跳过趋势数据抓取（按需求配置）")

            # 2.3 按需触发K线数据加载
            need_kline = any([
                data_requirements.get('hourly_kline', False),
                data_requirements.get('daily_kline_1', False),
                data_requirements.get('daily_kline_2', False),
                data_requirements.get('weekly_kline', False)
            ])

            if need_kline:
                logger.info("📊 第三步：触发K线数据加载")
                await self._trigger_kline_data_loading()
                await asyncio.sleep(2)  # 等待K线数据加载
            else:
                logger.info("⏭️ 跳过K线数据抓取（按需求配置）")

            # 3. 等待所有数据被捕获，增加超时时间
            await self._wait_for_dual_data_capture(captured_data, data_requirements, timeout=60)  # 60秒超时

            # 4. 移除监听器
            self.page.remove_listener('response', handle_api_response)

            # 5. 按需解析捕获的数据
            trend_data_3m = None
            trend_data_6m = None
            hourly_kline = None
            daily_kline_1 = None
            daily_kline_2 = None
            weekly_kline = None

            # 解析趋势数据
            if data_requirements.get('trend_data_3m', False) and captured_data['trend_data_3m']:
                trend_data_3m = self._parse_trend_data(captured_data['trend_data_3m'], captured_data['item_id'], '近3个月')
                logger.info("✅ 3个月趋势数据解析完成")

            if data_requirements.get('trend_data_6m', False) and captured_data['trend_data_6m']:
                trend_data_6m = self._parse_trend_data(captured_data['trend_data_6m'], captured_data['item_id'], '近6个月')
                logger.info("✅ 6个月趋势数据解析完成")

            # 解析K线数据
            if data_requirements.get('hourly_kline', False) and captured_data['kline_data_hourly']:
                hourly_kline = self._parse_kline_data(captured_data['kline_data_hourly'], captured_data['item_id'], '时K')
                logger.info("✅ 时K数据解析完成")

            if data_requirements.get('daily_kline_1', False) and captured_data['kline_data_daily_1']:
                daily_kline_1 = self._parse_kline_data(captured_data['kline_data_daily_1'], captured_data['item_id'], '日K_1')
                logger.info("✅ 日K_1数据解析完成")

            if data_requirements.get('daily_kline_2', False) and captured_data['kline_data_daily_2']:
                daily_kline_2 = self._parse_kline_data(captured_data['kline_data_daily_2'], captured_data['item_id'], '日K_2')
                logger.info("✅ 日K_2数据解析完成")

            if data_requirements.get('weekly_kline', False) and captured_data['kline_data_weekly']:
                weekly_kline = self._parse_kline_data(captured_data['kline_data_weekly'], captured_data['item_id'], '周K')
                logger.info("✅ 周K数据解析完成")

            # 选择主要趋势数据（优先使用6个月数据，如果没有则使用3个月数据）
            main_trend_data = trend_data_6m if trend_data_6m else trend_data_3m

            result = ScrapingResult(
                success=True,
                item_info=None,  # 不再保存饰品信息
                trend_data=main_trend_data,
                trend_data_3m=trend_data_3m,  # 3个月数据
                trend_data_6m=trend_data_6m,  # 6个月数据
                daily_kline=daily_kline_1,    # 向后兼容，使用第一次日K数据
                daily_kline_1=daily_kline_1,  # 日K数据第一次响应
                daily_kline_2=daily_kline_2,  # 日K数据第二次响应
                hourly_kline=hourly_kline,    # 时K数据
                weekly_kline=weekly_kline,    # 周K数据
                collected_at=datetime.now()
            )

            logger.info(f"饰品数据抓取完成: {item_url}")
            return result

        except Exception as e:
            logger.error(f"抓取饰品数据失败: {e}")
            if hasattr(self, 'page'):
                try:
                    self.page.remove_listener('response', handle_api_response)
                except:
                    pass
            return ScrapingResult(
                success=False,
                error_message=str(e),
                item_info=None
            )

    async def scrape_single_timerange_data(self, item_url: str, timerange_option: str, timerange_label: str) -> ScrapingResult:
        """抓取单个时间范围的饰品数据 - 用于调试"""
        try:
            logger.info(f"开始抓取{timerange_label}数据: {item_url}")

            # 存储监听到的数据
            captured_data = {
                'trend_data': None,
                'kline_data': None,
                'item_id': None
            }

            # 设置API监听器
            async def handle_api_response(response):
                try:
                    # 记录所有API响应的URL
                    if 'sdt-api.ok-skins.com' in response.url:
                        logger.info(f"🌐 API响应URL: {response.url}")
                        logger.info(f"📡 响应状态: {response.status}")

                        if response.status == 200:
                            data = await response.json()

                            # 获取item_id
                            if 'item' in response.url and 'data' in data and 'itemId' in data['data']:
                                captured_data['item_id'] = str(data['data']['itemId'])
                                logger.info(f"🆔 捕获饰品ID: {captured_data['item_id']}")

                            # 趋势数据API
                            elif 'type-trend/v2/item/details' in response.url:
                                data_count = len(data.get('data', []))
                                set_time = getattr(self, '_timerange_set_time', 0)
                                current_time = asyncio.get_event_loop().time()
                                time_since_set = current_time - set_time

                                logger.info(f"📈 {timerange_label}趋势数据API响应: {data_count}个数据点")
                                logger.info(f"🔄 单独抓取通信状态: 设置后经过={time_since_set:.1f}秒")

                                # 验证数据时间范围并获取实际天数
                                actual_days = 0
                                if data.get('data') and len(data['data']) > 0:
                                    actual_days = await self._validate_and_log_trend_data_time(data['data'], timerange_label)

                                # 精确的通信机制：只有在正确的时间范围设置后且数据符合预期才保存
                                if time_since_set >= 2:  # 至少等待2秒确保设置生效
                                    should_save = False

                                    if "3个月" in timerange_label:
                                        # 3个月数据应该有80-120天的跨度
                                        if 80 <= actual_days <= 120:
                                            should_save = True
                                            logger.info(f"✅ 确认{timerange_label}数据有效: {actual_days}天跨度")
                                        else:
                                            logger.warning(f"⚠️ {timerange_label}数据时间跨度不符合预期: {actual_days}天")
                                    elif "6个月" in timerange_label:
                                        # 6个月数据应该有150-200天的跨度
                                        if 150 <= actual_days <= 200:
                                            should_save = True
                                            logger.info(f"✅ 确认{timerange_label}数据有效: {actual_days}天跨度")
                                        else:
                                            logger.warning(f"⚠️ {timerange_label}数据时间跨度不符合预期: {actual_days}天")
                                    else:
                                        # 其他时间范围，直接保存
                                        should_save = True
                                        logger.info(f"✅ 保存{timerange_label}数据: {actual_days}天跨度")

                                    if should_save and not captured_data['trend_data']:
                                        captured_data['trend_data'] = data
                                        logger.info(f"✅ 成功保存{timerange_label}趋势数据: {data_count}个数据点")
                                    elif captured_data['trend_data']:
                                        logger.info(f"⏳ 已有{timerange_label}数据，忽略重复响应")
                                else:
                                    logger.info(f"⏳ 等待时间范围设置生效: 已等待{time_since_set:.1f}秒")

                            # K线数据API
                            elif 'kline' in response.url:
                                logger.info(f"📊 K线数据API响应")

                                # 验证K线数据时间范围
                                if data.get('data') and len(data['data']) > 0:
                                    await self._validate_and_log_kline_data_time(data['data'], timerange_label)

                                captured_data['kline_data'] = data
                        else:
                            logger.warning(f"❌ API响应状态异常: {response.status}")

                except Exception as e:
                    logger.error(f"处理API响应失败: {e}")
                    logger.error(f"响应URL: {response.url}")
                    logger.error(f"响应状态: {response.status}")

            # 设置监听器
            self.page.on('response', handle_api_response)

            # 1. 访问页面并获取基本信息
            item_info = await self.get_item_info_from_page(item_url)
            if not item_info:
                self.page.remove_listener('response', handle_api_response)
                return ScrapingResult(
                    success=False,
                    error_message="无法获取饰品基本信息",
                    item_info=None
                )

            # 等待item_id被捕获
            for _ in range(10):
                if captured_data['item_id']:
                    break
                await asyncio.sleep(0.5)

            if not captured_data['item_id']:
                self.page.remove_listener('response', handle_api_response)
                return ScrapingResult(
                    success=False,
                    error_message="无法获取饰品ID",
                    item_info=item_info
                )

            logger.info(f"饰品ID: {captured_data['item_id']}")
            item_info.item_id = captured_data['item_id']

            # 2. 设置指定的时间范围
            logger.info(f"设置时间范围为: {timerange_option}")
            self._timerange_set_time = asyncio.get_event_loop().time()  # 记录设置时间
            logger.info(f"🔄 单独抓取通信状态: timerange={timerange_label}, 设置时间={self._timerange_set_time}")

            success = await self._set_platform_and_timerange_specific(timerange_option)
            if not success:
                logger.warning(f"时间范围设置失败，尝试强制设置...")

            # 3. 等待数据被捕获
            logger.info(f"⏳ 等待{timerange_label}数据捕获...")
            start_time = asyncio.get_event_loop().time()
            timeout = 15

            while asyncio.get_event_loop().time() - start_time < timeout:
                if captured_data['trend_data']:
                    logger.info(f"✅ {timerange_label}趋势数据已捕获")
                    break
                await asyncio.sleep(1)

            # 额外等待K线数据
            if not captured_data['kline_data']:
                logger.info("⏳ 继续等待K线数据...")
                await asyncio.sleep(3)

            # 4. 移除监听器
            self.page.remove_listener('response', handle_api_response)

            # 5. 解析捕获的数据
            trend_data = self._parse_trend_data(captured_data['trend_data'], captured_data['item_id'], timerange_option) if captured_data['trend_data'] else None
            daily_kline = self._parse_kline_data(captured_data['kline_data'], captured_data['item_id'], 'daily') if captured_data['kline_data'] else None

            result = ScrapingResult(
                success=True,
                item_info=item_info,
                trend_data=trend_data,
                daily_kline=daily_kline,
                hourly_kline=None,
                weekly_kline=None,
                collected_at=datetime.now()
            )

            logger.info(f"{timerange_label}数据抓取完成: {item_url}")
            return result

        except Exception as e:
            logger.error(f"抓取{timerange_label}数据失败: {e}")
            if hasattr(self, 'page'):
                try:
                    self.page.remove_listener('response', handle_api_response)
                except:
                    pass
            return ScrapingResult(
                success=False,
                error_message=str(e),
                item_info=None
            )

    async def _trigger_data_refresh(self):
        """触发数据刷新，确保获取最新的趋势和K线数据"""
        try:
            logger.info("🔄 开始触发数据刷新...")

            # 方法1: 切换到K线图标签触发K线数据加载
            try:
                # 确保在K线图标签
                await self.page.click('tab[role="tab"]:has-text("K线图")', timeout=3000)
                await asyncio.sleep(2)
                logger.info("✓ 已确认在K线图标签")

                # 尝试点击日K按钮
                try:
                    await self.page.click('button:has-text("日K")', timeout=3000)
                    await asyncio.sleep(2)
                    logger.info("✓ 已点击日K按钮")
                except Exception as e:
                    logger.debug(f"点击日K按钮失败: {e}")

                # 尝试其他可能的K线触发方式
                try:
                    await self.page.click('.kline-chart', timeout=3000)
                    await asyncio.sleep(1)
                    logger.info("✓ 已点击K线图区域")
                except Exception as e:
                    logger.debug(f"点击K线图区域失败: {e}")

            except Exception as e:
                logger.debug(f"K线图操作失败: {e}")

            # 方法2: 重新确认时间范围设置，触发趋势数据刷新
            try:
                time_selector = '#__nuxt > div > div.common-container > div > div.mt-10.flex > div.relative.rounded-\\[10px\\].bg-white.pt-20 > div:nth-child(3) > div > div.charts-menu.flex.h-60.items-center.justify-between > div > div.mr-20 > div > div'

                # 点击时间选择器
                await self.page.click(time_selector, timeout=3000)
                await asyncio.sleep(1)

                # 重新选择近3个月
                await self.page.click('text="近3个月"', timeout=3000)
                await asyncio.sleep(3)
                logger.info("✓ 已重新确认时间范围设置")
            except Exception as e:
                logger.debug(f"重新设置时间范围失败: {e}")

            # 方法3: 刷新页面数据（不刷新整个页面）
            try:
                # 滚动页面触发懒加载
                await self.page.evaluate("window.scrollTo(0, document.body.scrollHeight)")
                await asyncio.sleep(1)
                await self.page.evaluate("window.scrollTo(0, 0)")
                await asyncio.sleep(2)
                logger.info("✓ 已触发页面滚动刷新")
            except Exception as e:
                logger.debug(f"页面滚动刷新失败: {e}")

        except Exception as e:
            logger.debug(f"触发数据刷新失败: {e}")

    async def _wait_for_dual_data_capture(self, captured_data: dict, data_requirements: dict, timeout: int = 60):
        """等待数据被捕获（按需等待）"""
        try:
            # 确定需要等待的数据类型
            need_3m = data_requirements.get('trend_data_3m', False)
            need_6m = data_requirements.get('trend_data_6m', False)

            if need_3m or need_6m:
                logger.info(f"⏳ 等待趋势数据捕获: 3个月={need_3m}, 6个月={need_6m}")
            else:
                logger.info("⏭️ 跳过趋势数据等待")

            start_time = asyncio.get_event_loop().time()
            while asyncio.get_event_loop().time() - start_time < timeout:
                # 检查需要的趋势数据是否已捕获
                has_3m = captured_data['trend_data_3m'] is not None
                has_6m = captured_data['trend_data_6m'] is not None

                # 检查是否满足需求
                trend_satisfied = True
                if need_3m and not has_3m:
                    trend_satisfied = False
                if need_6m and not has_6m:
                    trend_satisfied = False

                if trend_satisfied and (need_3m or need_6m):
                    if need_3m and need_6m:
                        logger.info("✅ 3个月和6个月趋势数据都已捕获")
                    elif need_3m:
                        logger.info("✅ 3个月趋势数据已捕获")
                    elif need_6m:
                        logger.info("✅ 6个月趋势数据已捕获")
                    break
                elif not (need_3m or need_6m):
                    # 不需要趋势数据，直接跳出
                    break

                await asyncio.sleep(1)

            # 按需等待K线数据
            need_kline = any([
                data_requirements.get('hourly_kline', False),
                data_requirements.get('daily_kline_1', False),
                data_requirements.get('daily_kline_2', False),
                data_requirements.get('weekly_kline', False)
            ])

            if need_kline:
                has_any_kline = any([
                    captured_data['kline_data_hourly'],
                    captured_data['kline_data_daily_1'],
                    captured_data['kline_data_daily_2'],
                    captured_data['kline_data_weekly']
                ])

                if not has_any_kline:
                    logger.info("⏳ 继续等待K线数据...")
                    await asyncio.sleep(5)  # 增加等待时间
            else:
                logger.info("⏭️ 跳过K线数据等待")

            # 显示最终捕获状态
            trend_3m_status = "✅" if captured_data['trend_data_3m'] else "❌"
            trend_6m_status = "✅" if captured_data['trend_data_6m'] else "❌"
            hourly_kline_status = "✅" if captured_data['kline_data_hourly'] else "❌"
            daily_kline_1_status = "✅" if captured_data['kline_data_daily_1'] else "❌"
            daily_kline_2_status = "✅" if captured_data['kline_data_daily_2'] else "❌"
            weekly_kline_status = "✅" if captured_data['kline_data_weekly'] else "❌"

            logger.info(f"数据捕获完成: 3个月{trend_3m_status} 6个月{trend_6m_status}")
            logger.info(f"K线数据: 时K{hourly_kline_status} 日K1{daily_kline_1_status} 日K2{daily_kline_2_status} 周K{weekly_kline_status}")

        except Exception as e:
            logger.error(f"等待双重数据捕获失败: {e}")

    async def _trigger_kline_data_loading(self):
        """主动触发K线数据加载"""
        try:
            logger.info("🔄 尝试触发K线数据加载...")

            # 方法1: 点击K线图标签并抓取所有类型K线数据
            try:
                # 首先尝试点击K线图标签
                await self.page.click('text="K线图"', timeout=5000)
                logger.info("✅ 成功点击K线图标签")
                await asyncio.sleep(2)  # 等待K线图加载

                # 依次点击时K、日K、周K，触发所有类型的K线数据加载
                kline_types = [
                    ("时K", "hourly"),
                    ("日K", "daily"),
                    ("周K", "weekly")
                ]

                for kline_name, kline_type in kline_types:
                    try:
                        await self.page.click(f'text="{kline_name}"', timeout=3000)
                        logger.info(f"✅ 成功点击{kline_name}选项")
                        await asyncio.sleep(3)  # 等待数据加载
                    except Exception as e:
                        logger.warning(f"点击{kline_name}选项失败: {e}")

            except Exception as e:
                logger.warning(f"点击K线图标签失败: {e}")

                # 备用方法: 使用role选择器
                try:
                    await self.page.get_by_role('tab', name='K线图').click()
                    logger.info("✅ 使用role选择器成功点击K线图标签")
                    await asyncio.sleep(2)

                    # 备用方法也要点击所有K线类型
                    for kline_name, kline_type in kline_types:
                        try:
                            await self.page.click(f'text="{kline_name}"', timeout=3000)
                            logger.info(f"✅ 备用方法成功点击{kline_name}选项")
                            await asyncio.sleep(3)
                        except Exception as e:
                            logger.warning(f"备用方法点击{kline_name}选项失败: {e}")

                except Exception as e2:
                    logger.debug(f"使用role选择器也失败: {e2}")

            # 删除多余的滚动和悬停操作

        except Exception as e:
            logger.warning(f"触发K线数据加载失败: {e}")

    async def _set_platform_and_timerange_specific(self, timerange: str, max_retries: int = 2):
        """设置平台为总览和指定时间范围（带重试机制）"""
        for retry in range(max_retries + 1):
            try:
                if retry > 0:
                    logger.info(f"🔄 第{retry + 1}次尝试设置平台和时间范围: 总览 + {timerange}")
                    # 刷新页面重试
                    await self._refresh_page_safely()
                else:
                    logger.info(f"设置平台和时间范围: 总览 + {timerange}")

                # 1. 设置平台为总览
                try:
                    # 方法1: 直接点击总览文本
                    try:
                        await self.page.click('text="总览"', timeout=3000)
                        logger.info("✅ 直接点击总览成功")
                        await asyncio.sleep(1)
                    except:
                        # 方法2: 查找平台选择器
                        platform_selectors = [
                            '[class*="platform"]',
                            '[class*="dropdown"]',
                            '.charts-menu [class*="ml-20"]',
                            'div:has-text("总览")',
                            'button:has-text("总览")'
                        ]

                        platform_set = False
                        for selector in platform_selectors:
                            try:
                                await self.page.click(selector, timeout=2000)
                                await asyncio.sleep(0.5)
                                await self.page.click('text="总览"', timeout=2000)
                                logger.info(f"✅ 使用选择器 {selector} 设置平台成功")
                                platform_set = True
                                break
                            except:
                                continue

                        if not platform_set:
                            logger.warning("所有平台选择器都失败，跳过平台设置")

                except Exception as e:
                    logger.warning(f"设置平台为总览失败: {e}")

                # 2. 设置时间范围
                time_selector = '#__nuxt > div > div.common-container > div > div.mt-10.flex > div.relative.rounded-\\[10px\\].bg-white.pt-20 > div:nth-child(3) > div > div.charts-menu.flex.h-60.items-center.justify-between > div > div.mr-20 > div > div'

                try:
                    await self.page.wait_for_selector(time_selector, timeout=5000)
                    await self.page.click(time_selector)
                    await asyncio.sleep(1)

                    # 选择指定时间范围
                    await self.page.click(f'text="{timerange}"', timeout=5000)
                    await asyncio.sleep(2)

                    logger.info(f"✅ 时间范围已设置为{timerange}")
                    return True

                except Exception as e:
                    logger.warning(f"设置{timerange}失败: {e}")
                    if retry < max_retries:
                        logger.info(f"⏳ 准备刷新页面重试...")
                        continue
                    else:
                        return False

            except Exception as e:
                logger.error(f"设置时间范围失败: {e}")
                if retry < max_retries:
                    logger.info(f"⏳ 准备刷新页面重试...")
                    continue
                else:
                    return False

        return False

    async def _refresh_page_safely(self):
        """安全地刷新页面"""
        try:
            logger.info("🔄 刷新页面以重置状态...")

            # 获取当前URL
            current_url = self.page.url

            # 刷新页面
            await self.page.reload(wait_until='networkidle')
            await asyncio.sleep(3)

            # 关闭可能的弹窗
            await self.close_popup_if_exists()

            logger.info("✅ 页面刷新完成")

        except Exception as e:
            logger.warning(f"页面刷新失败: {e}")
            # 如果刷新失败，尝试重新导航
            try:
                current_url = self.page.url
                await self.page.goto(current_url, wait_until='networkidle')
                await asyncio.sleep(3)
                await self.close_popup_if_exists()
                logger.info("✅ 重新导航完成")
            except Exception as e2:
                logger.error(f"重新导航也失败: {e2}")

    async def _validate_and_log_trend_data_time(self, trend_data: list, timerange_label: str) -> int:
        """验证并记录趋势数据的时间范围，返回实际天数"""
        try:
            if not trend_data or len(trend_data) == 0:
                logger.warning(f"⚠️ {timerange_label}趋势数据为空")
                return 0

            # 获取第一个和最后一个数据点的时间戳
            first_point = trend_data[0]
            last_point = trend_data[-1]

            if isinstance(first_point, list) and len(first_point) >= 2:
                first_timestamp = int(first_point[0]) if first_point[0] else 0
                last_timestamp = int(last_point[0]) if last_point[0] else 0

                # 修复时间戳格式
                if first_timestamp > 0:
                    if first_timestamp < 1000000000000:  # 秒转毫秒
                        first_timestamp = first_timestamp * 1000
                    first_time = datetime.fromtimestamp(first_timestamp/1000)
                else:
                    first_time = None

                if last_timestamp > 0:
                    if last_timestamp < 1000000000000:  # 秒转毫秒
                        last_timestamp = last_timestamp * 1000
                    last_time = datetime.fromtimestamp(last_timestamp/1000)
                else:
                    last_time = None

                if first_time and last_time:
                    time_span_days = (last_time - first_time).days
                    logger.info(f"⏰ {timerange_label}趋势数据时间验证:")
                    logger.info(f"   📅 开始时间: {first_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    logger.info(f"   📅 结束时间: {last_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    logger.info(f"   📊 时间跨度: {time_span_days} 天")
                    logger.info(f"   📈 数据密度: {len(trend_data) / time_span_days:.1f} 点/天")

                    # 记录时间跨度信息（移除验证，支持新品饰品）
                    logger.info(f"   📊 {timerange_label}数据时间跨度: {time_span_days}天")

                    return time_span_days
                else:
                    logger.warning(f"⚠️ {timerange_label}趋势数据时间戳解析失败")
                    return 0
            else:
                logger.warning(f"⚠️ {timerange_label}趋势数据格式异常")
                return 0

        except Exception as e:
            logger.error(f"验证{timerange_label}趋势数据时间失败: {e}")
            return 0

    async def _validate_and_log_kline_data_time(self, kline_data: list, timerange_label: str):
        """验证并记录K线数据的时间范围"""
        try:
            if not kline_data or len(kline_data) == 0:
                logger.warning(f"⚠️ {timerange_label}K线数据为空")
                return

            # K线数据格式通常是 [timestamp, open, high, low, close, volume]
            first_point = kline_data[0]
            last_point = kline_data[-1]

            if isinstance(first_point, list) and len(first_point) >= 1:
                first_timestamp = int(first_point[0]) if first_point[0] else 0
                last_timestamp = int(last_point[0]) if last_point[0] else 0

                # 修复时间戳格式
                if first_timestamp > 0:
                    if first_timestamp < 1000000000000:  # 秒转毫秒
                        first_timestamp = first_timestamp * 1000
                    first_time = datetime.fromtimestamp(first_timestamp/1000)
                else:
                    first_time = None

                if last_timestamp > 0:
                    if last_timestamp < 1000000000000:  # 秒转毫秒
                        last_timestamp = last_timestamp * 1000
                    last_time = datetime.fromtimestamp(last_timestamp/1000)
                else:
                    last_time = None

                if first_time and last_time:
                    time_span_days = (last_time - first_time).days
                    logger.info(f"⏰ {timerange_label}K线数据时间验证:")
                    logger.info(f"   📅 开始时间: {first_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    logger.info(f"   📅 结束时间: {last_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    logger.info(f"   📊 时间跨度: {time_span_days} 天")
                    logger.info(f"   📈 数据密度: {len(kline_data) / time_span_days:.1f} 点/天")
                else:
                    logger.warning(f"⚠️ {timerange_label}K线数据时间戳解析失败")
            else:
                logger.warning(f"⚠️ {timerange_label}K线数据格式异常")

        except Exception as e:
            logger.error(f"验证{timerange_label}K线数据时间失败: {e}")

    async def scrape_multiple_items(self, item_urls: List[str]) -> List[ScrapingResult]:
        """批量抓取多个饰品数据"""
        results = []

        for i, url in enumerate(item_urls, 1):
            logger.info(f"抓取进度: {i}/{len(item_urls)}")

            result = await self.scrape_item_data(url)
            results.append(result)

            # 添加延迟避免请求过快
            if i < len(item_urls):
                await asyncio.sleep(2)

        return results
