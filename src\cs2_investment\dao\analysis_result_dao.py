"""
分析结果数据访问对象 - 简化版本
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy import desc, func, and_

from .base_dao import BaseDAO
from ..models.analysis_result import AnalysisResult, RealtimeAnalysisResult
from ..config.database import get_db_session


class AnalysisResultDAO(BaseDAO[AnalysisResult]):
    """常规分析结果DAO"""
    
    def __init__(self):
        super().__init__(AnalysisResult)
        self.logger = logging.getLogger(__name__)
    
    def create_analysis_result(self, analysis_data: Dict[str, Any]) -> Optional[AnalysisResult]:
        """创建分析结果记录"""
        try:
            # 直接使用数据库会话创建记录
            with get_db_session() as session:
                result = AnalysisResult(**analysis_data)
                session.add(result)
                session.commit()
                session.refresh(result)
                self.logger.info(f"创建分析结果成功: {result.id} - {result.item_name}")
                
                # 返回分离的对象，避免Session绑定问题
                session.expunge(result)
                return result
        except Exception as e:
            self.logger.error(f"创建分析结果失败: {e}")
            return None
    
    def get_latest_analysis(self, item_id: str, analysis_type: str = 'regular') -> Optional[AnalysisResult]:
        """获取最新的分析结果"""
        try:
            with get_db_session() as session:
                query = session.query(AnalysisResult).filter(
                    AnalysisResult.item_id == item_id
                )
                
                result = query.order_by(desc(AnalysisResult.created_at)).first()
                
                if result:
                    # 分离对象避免Session绑定问题
                    session.expunge(result)
                
                return result
        except Exception as e:
            self.logger.error(f"获取最新分析结果失败: {e}")
            return None
    
    def get_analysis_history(self, item_id: str, days: int = 30) -> List[AnalysisResult]:
        """获取分析历史记录"""
        try:
            with get_db_session() as session:
                start_date = datetime.now() - timedelta(days=days)
                
                query = session.query(AnalysisResult).filter(
                    and_(
                        AnalysisResult.item_id == item_id,
                        AnalysisResult.created_at >= start_date
                    )
                )
                
                results = query.order_by(desc(AnalysisResult.created_at)).all()
                
                # 分离所有对象避免Session绑定问题
                for result in results:
                    session.expunge(result)
                
                return results
        except Exception as e:
            self.logger.error(f"获取分析历史失败: {e}")
            return []
    
    def get_top_investment_scores(self, limit: int = 50) -> List[AnalysisResult]:
        """获取投资评分最高的饰品"""
        try:
            with get_db_session() as session:
                results = session.query(AnalysisResult).filter(
                    AnalysisResult.investment_score.isnot(None)
                ).order_by(
                    desc(AnalysisResult.investment_score)
                ).limit(limit).all()
                
                # 分离所有对象
                for result in results:
                    session.expunge(result)
                
                return results
        except Exception as e:
            self.logger.error(f"获取投资评分排行失败: {e}")
            return []
    
    def get_risk_distribution(self) -> Dict[str, int]:
        """获取风险等级分布"""
        try:
            with get_db_session() as session:
                results = session.query(
                    AnalysisResult.risk_level,
                    func.count(AnalysisResult.id).label('count')
                ).filter(
                    AnalysisResult.risk_level.isnot(None)
                ).group_by(
                    AnalysisResult.risk_level
                ).all()
                
                return {risk_level: count for risk_level, count in results}
        except Exception as e:
            self.logger.error(f"获取风险分布失败: {e}")
            return {}
    
    def get_trading_signals_summary(self) -> Dict[str, int]:
        """获取交易信号汇总"""
        try:
            with get_db_session() as session:
                results = session.query(
                    AnalysisResult.trading_signal,
                    func.count(AnalysisResult.id).label('count')
                ).filter(
                    AnalysisResult.trading_signal.isnot(None)
                ).group_by(
                    AnalysisResult.trading_signal
                ).all()
                
                return {signal: count for signal, count in results}
        except Exception as e:
            self.logger.error(f"获取交易信号汇总失败: {e}")
            return {}


class RealtimeAnalysisResultDAO(BaseDAO[RealtimeAnalysisResult]):
    """实时分析结果DAO"""
    
    def __init__(self):
        super().__init__(RealtimeAnalysisResult)
        self.logger = logging.getLogger(__name__)
    
    def create_realtime_result(self, analysis_data: Dict[str, Any]) -> Optional[RealtimeAnalysisResult]:
        """创建实时分析结果记录"""
        try:
            with get_db_session() as session:
                result = RealtimeAnalysisResult(**analysis_data)
                session.add(result)
                session.commit()
                session.refresh(result)
                self.logger.info(f"创建实时分析结果成功: {result.id} - {result.item_name}")
                
                # 返回分离的对象
                session.expunge(result)
                return result
        except Exception as e:
            self.logger.error(f"创建实时分析结果失败: {e}")
            return None
    
    def get_latest_realtime_analysis(self, item_id: str) -> Optional[RealtimeAnalysisResult]:
        """获取最新的实时分析结果"""
        try:
            with get_db_session() as session:
                result = session.query(RealtimeAnalysisResult).filter(
                    RealtimeAnalysisResult.item_id == item_id
                ).order_by(desc(RealtimeAnalysisResult.created_at)).first()
                
                if result:
                    session.expunge(result)
                
                return result
        except Exception as e:
            self.logger.error(f"获取最新实时分析结果失败: {e}")
            return None
    
    def get_anomaly_summary(self, item_id: str, hours: int = 24) -> Dict[str, Any]:
        """获取异常汇总统计"""
        try:
            with get_db_session() as session:
                start_time = datetime.now() - timedelta(hours=hours)
                
                results = session.query(RealtimeAnalysisResult).filter(
                    and_(
                        RealtimeAnalysisResult.item_id == item_id,
                        RealtimeAnalysisResult.created_at >= start_time
                    )
                ).all()
                
                if not results:
                    return {}
                
                total_anomalies = sum(r.anomaly_count or 0 for r in results)
                avg_frequency = sum(r.anomaly_frequency or 0 for r in results) / len(results)
                
                return {
                    'total_records': len(results),
                    'total_anomalies': total_anomalies,
                    'average_frequency': round(avg_frequency, 2),
                    'latest_severity': results[0].anomaly_severity if results else None
                }
        except Exception as e:
            self.logger.error(f"获取异常汇总失败: {e}")
            return {}
