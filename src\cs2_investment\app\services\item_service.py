"""
饰品服务层

提供饰品查询和相关业务逻辑。
"""

from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.item_dao import ItemDAO
from src.cs2_investment.dao.market_snapshot_dao import MarketSnapshotDAO
from src.cs2_investment.models.item import Item
from src.cs2_investment.models.market_snapshot import MarketSnapshot


class ItemService:
    """饰品服务类"""
    
    def __init__(self):
        self.item_dao = ItemDAO()
        self.snapshot_dao = MarketSnapshotDAO()
    
    def search_items_with_prices(self,
                                name_query: Optional[str] = None,
                                item_types: Optional[List[str]] = None,
                                qualities: Optional[List[str]] = None,
                                rarities: Optional[List[str]] = None,
                                price_min: Optional[float] = None,
                                price_max: Optional[float] = None,
                                sort_by: str = "updated_desc",
                                limit: int = 1000,
                                offset: int = 0) -> List[Dict[str, Any]]:
        """搜索带价格信息的饰品"""
        try:
            # 直接在DAO层获取字典格式的数据，避免ORM对象的Session问题
            items_data = self._get_items_as_dict(
                name_query=name_query,
                item_types=item_types,
                qualities=qualities,
                rarities=rarities,
                sort_by=sort_by if sort_by not in ["price_asc", "price_desc"] else "updated_desc",
                limit=limit * 2,  # 获取更多数据以便价格筛选
                offset=offset
            )

            if not items_data:
                return []

            # 获取最新的市场快照数据 - 直接获取字典格式
            item_ids = [item['item_id'] for item in items_data]
            snapshots_data = self._get_snapshots_as_dict(item_ids)

            # 创建快照字典以便快速查找
            snapshot_dict = {snap['item_id']: snap for snap in snapshots_data}

            # 合并饰品和价格信息
            results = []
            for item_data in items_data:
                snapshot_data = snapshot_dict.get(item_data['item_id'])

                # 构建结果字典
                result = item_data.copy()  # 复制基础数据

                # 添加价格信息
                if snapshot_data:
                    result.update({
                        'current_price': snapshot_data.get('current_price', 0),
                        'price_change_7d': snapshot_data.get('diff_7d', 0),
                        'price_change_30d': snapshot_data.get('diff_1m', 0),
                        'volume_30d': snapshot_data.get('trans_count_1m', 0),
                        'amount_30d': snapshot_data.get('trans_amount_1m', 0),
                        'hot_rank': snapshot_data.get('hot_rank'),
                        'snapshot_time': snapshot_data.get('snapshot_time')
                    })
                else:
                    result.update({
                        'current_price': 0,
                        'price_change_7d': 0,
                        'price_change_30d': 0,
                        'volume_30d': 0,
                        'amount_30d': 0,
                        'hot_rank': None,
                        'snapshot_time': None
                    })
                
                # 价格筛选
                current_price = result['current_price']
                if price_min is not None and current_price < price_min:
                    continue
                if price_max is not None and current_price > price_max:
                    continue
                
                results.append(result)
            
            # 按价格排序（如果需要）
            if sort_by == "price_asc":
                results.sort(key=lambda x: x['current_price'])
            elif sort_by == "price_desc":
                results.sort(key=lambda x: x['current_price'], reverse=True)
            
            # 限制结果数量
            return results[:limit]
            
        except Exception as e:
            print(f"搜索饰品失败: {e}")
            return []
    
    def get_item_detail(self, item_id: str) -> Optional[Dict[str, Any]]:
        """获取饰品详细信息"""
        try:
            # 直接在数据库层获取数据并转换为字典
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.item import Item

            with get_db_session() as session:
                item = session.query(Item).filter(Item.item_id == item_id).first()
                if not item:
                    return None

                # 立即转换为字典
                result = {
                    'item_id': item.item_id,
                    'name': item.name,
                    'item_type': item.item_type,
                    'quality': item.quality,
                    'rarity': item.rarity,
                    'exterior': getattr(item, 'exterior', None),
                    'image_url': getattr(item, 'image_url', None),
                    'market_hash_name': getattr(item, 'market_hash_name', None),
                    'def_index_name': getattr(item, 'def_index_name', None),
                    'created_at': getattr(item, 'created_at', None),
                    'updated_at': getattr(item, 'updated_at', None)
                }

            # 获取最新快照 - 直接在数据库层获取
            snapshot_data = self._get_single_snapshot_as_dict(item_id)

            if snapshot_data:
                result.update({
                    'current_price': snapshot_data.get('current_price', 0),
                    'price_change_1d': snapshot_data.get('diff_1d', 0),
                    'price_change_7d': snapshot_data.get('diff_7d', 0),
                    'price_change_30d': snapshot_data.get('diff_1m', 0),
                    'volume_1d': snapshot_data.get('trans_count_1d', 0),
                    'volume_7d': snapshot_data.get('trans_count_7d', 0),
                    'volume_30d': snapshot_data.get('trans_count_1m', 0),
                    'amount_1d': snapshot_data.get('trans_amount_1d', 0),
                    'amount_7d': snapshot_data.get('trans_amount_7d', 0),
                    'amount_30d': snapshot_data.get('trans_amount_1m', 0),
                    'hot_rank': snapshot_data.get('hot_rank'),
                    'sell_nums': snapshot_data.get('sell_nums'),
                    'survive_num': snapshot_data.get('survive_num'),
                    'snapshot_time': snapshot_data.get('snapshot_time')
                })
            
            return result
            
        except Exception as e:
            print(f"获取饰品详情失败: {e}")
            return None
    
    def get_filter_options(self) -> Dict[str, List[str]]:
        """获取筛选选项"""
        try:
            return {
                'item_types': self.item_dao.get_distinct_values('item_type'),
                'qualities': self.item_dao.get_distinct_values('quality'),
                'rarities': self.item_dao.get_distinct_values('rarity')
            }
        except Exception as e:
            print(f"获取筛选选项失败: {e}")
            return {
                'item_types': [],
                'qualities': [],
                'rarities': []
            }
    
    def get_item_statistics(self) -> Dict[str, Any]:
        """获取饰品统计信息"""
        try:
            stats = self.item_dao.get_item_statistics()
            
            # 获取最新更新时间
            latest_snapshot = self.snapshot_dao.get_latest_snapshot()
            if latest_snapshot:
                stats['last_update'] = latest_snapshot.snapshot_time
            
            return stats
        except Exception as e:
            print(f"获取统计信息失败: {e}")
            return {}
    
    def count_search_results(self,
                           name_query: Optional[str] = None,
                           item_types: Optional[List[str]] = None,
                           qualities: Optional[List[str]] = None,
                           rarities: Optional[List[str]] = None,
                           price_min: Optional[float] = None,
                           price_max: Optional[float] = None) -> int:
        """统计搜索结果数量"""
        try:
            # 这里简化实现，实际应该在数据库层面进行计数
            results = self.search_items_with_prices(
                name_query=name_query,
                item_types=item_types,
                qualities=qualities,
                rarities=rarities,
                price_min=price_min,
                price_max=price_max,
                limit=10000  # 设置一个较大的限制来获取总数
            )
            return len(results)
        except Exception as e:
            print(f"统计搜索结果失败: {e}")
            return 0

    def _get_items_as_dict(self, name_query: Optional[str] = None,
                          item_types: Optional[List[str]] = None,
                          qualities: Optional[List[str]] = None,
                          rarities: Optional[List[str]] = None,
                          sort_by: str = "updated_desc",
                          limit: int = 1000,
                          offset: int = 0) -> List[Dict[str, Any]]:
        """获取饰品数据并直接转换为字典格式，避免Session问题"""
        try:
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.item import Item
            from sqlalchemy import desc, asc

            with get_db_session() as session:
                query = session.query(Item)

                # 名称搜索
                if name_query:
                    query = query.filter(
                        Item.name.like(f"%{name_query}%") |
                        Item.market_hash_name.like(f"%{name_query}%")
                    )

                # 类型过滤
                if item_types:
                    query = query.filter(Item.item_type.in_(item_types))

                # 品质过滤
                if qualities:
                    query = query.filter(Item.quality.in_(qualities))

                # 稀有度过滤
                if rarities:
                    query = query.filter(Item.rarity.in_(rarities))

                # 排序
                if sort_by == "name_asc":
                    query = query.order_by(Item.name.asc())
                elif sort_by == "name_desc":
                    query = query.order_by(Item.name.desc())
                elif sort_by == "updated_desc":
                    query = query.order_by(Item.updated_at.desc())
                elif sort_by == "updated_asc":
                    query = query.order_by(Item.updated_at.asc())
                else:
                    query = query.order_by(Item.updated_at.desc())

                # 获取结果并立即转换为字典
                items = query.offset(offset).limit(limit).all()

                results = []
                for item in items:
                    item_dict = {
                        'item_id': item.item_id,
                        'name': item.name,
                        'item_type': item.item_type,
                        'quality': item.quality,
                        'rarity': item.rarity,
                        'exterior': getattr(item, 'exterior', None),
                        'image_url': getattr(item, 'image_url', None),
                        'market_hash_name': getattr(item, 'market_hash_name', None),
                        'created_at': getattr(item, 'created_at', None),
                        'updated_at': getattr(item, 'updated_at', None)
                    }
                    results.append(item_dict)

                return results

        except Exception as e:
            print(f"获取饰品数据失败: {e}")
            return []

    def _get_snapshots_as_dict(self, item_ids: List[str]) -> List[Dict[str, Any]]:
        """获取快照数据并直接转换为字典格式，避免Session问题"""
        try:
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.market_snapshot import MarketSnapshot
            from sqlalchemy import func, and_

            with get_db_session() as session:
                # 获取每个饰品的最新快照时间
                subquery = session.query(
                    MarketSnapshot.item_id,
                    func.max(MarketSnapshot.snapshot_time).label('max_time')
                ).filter(
                    MarketSnapshot.item_id.in_(item_ids)
                ).group_by(MarketSnapshot.item_id).subquery()

                # 获取最新快照
                snapshots = session.query(MarketSnapshot)\
                    .join(
                        subquery,
                        and_(
                            MarketSnapshot.item_id == subquery.c.item_id,
                            MarketSnapshot.snapshot_time == subquery.c.max_time
                        )
                    ).all()

                # 立即转换为字典
                results = []
                for snapshot in snapshots:
                    snapshot_dict = {
                        'item_id': snapshot.item_id,
                        'current_price': float(snapshot.current_price) if snapshot.current_price else 0,
                        'diff_7d': float(snapshot.diff_7d) if snapshot.diff_7d else 0,
                        'diff_1m': float(snapshot.diff_1m) if snapshot.diff_1m else 0,
                        'trans_count_1m': snapshot.trans_count_1m or 0,
                        'trans_amount_1m': float(snapshot.trans_amount_1m) if snapshot.trans_amount_1m else 0,
                        'hot_rank': snapshot.hot_rank,
                        'snapshot_time': snapshot.snapshot_time
                    }
                    results.append(snapshot_dict)

                return results

        except Exception as e:
            print(f"获取快照数据失败: {e}")
            return []

    def _get_single_snapshot_as_dict(self, item_id: str) -> Optional[Dict[str, Any]]:
        """获取单个饰品的最新快照数据并转换为字典格式"""
        try:
            from src.cs2_investment.config.database import get_db_session
            from src.cs2_investment.models.market_snapshot import MarketSnapshot
            from sqlalchemy import desc

            with get_db_session() as session:
                snapshot = session.query(MarketSnapshot)\
                    .filter(MarketSnapshot.item_id == item_id)\
                    .order_by(desc(MarketSnapshot.snapshot_time))\
                    .first()

                if not snapshot:
                    return None

                # 立即转换为字典
                return {
                    'item_id': snapshot.item_id,
                    'current_price': float(snapshot.current_price) if snapshot.current_price else 0,
                    'diff_1d': float(snapshot.diff_1d) if snapshot.diff_1d else 0,
                    'diff_7d': float(snapshot.diff_7d) if snapshot.diff_7d else 0,
                    'diff_1m': float(snapshot.diff_1m) if snapshot.diff_1m else 0,
                    'trans_count_1d': snapshot.trans_count_1d or 0,
                    'trans_count_7d': snapshot.trans_count_7d or 0,
                    'trans_count_1m': snapshot.trans_count_1m or 0,
                    'trans_amount_1d': float(snapshot.trans_amount_1d) if snapshot.trans_amount_1d else 0,
                    'trans_amount_7d': float(snapshot.trans_amount_7d) if snapshot.trans_amount_7d else 0,
                    'trans_amount_1m': float(snapshot.trans_amount_1m) if snapshot.trans_amount_1m else 0,
                    'hot_rank': snapshot.hot_rank,
                    'sell_nums': snapshot.sell_nums,
                    'survive_num': snapshot.survive_num,
                    'snapshot_time': snapshot.snapshot_time
                }

        except Exception as e:
            print(f"获取单个快照数据失败: {e}")
            return None
