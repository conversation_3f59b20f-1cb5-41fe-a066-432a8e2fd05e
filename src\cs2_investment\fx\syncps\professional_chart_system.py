#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品专业图表生成系统 - 为syncps分析系统设计
支持多维度分析结果的专业可视化
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ProfessionalChartSystem:
    """专业图表生成系统 - 为syncps分析系统定制"""
    
    def __init__(self, analysis_system, display_name=None):
        """
        初始化专业图表系统

        Args:
            analysis_system: CS2AnalysisSystemV2实例
            display_name: 可选的显示名称，如果提供则直接使用，否则提取ID
        """
        self.analysis_system = analysis_system
        self.skin_name = analysis_system.skin_name

        # 使用传入的显示名称，或者提取ID
        if display_name:
            self.display_name = display_name
            print(f"🔍 使用传入的饰品名称: {display_name}")
        else:
            self.display_name = self._extract_id_from_path(self.skin_name)
            print(f"🔍 提取到饰品ID: {self.display_name}")
        
        # 专业配色方案
        self.colors = {
            'primary': '#1f77b4',        # 主色调蓝色
            'secondary': '#ff7f0e',      # 次色调橙色
            'success': '#2ca02c',        # 成功绿色
            'danger': '#d62728',         # 危险红色
            'warning': '#ff9800',        # 警告橙色
            'info': '#17a2b8',          # 信息青色
            'light': '#f8f9fa',         # 浅色
            'dark': '#343a40',          # 深色
            'background': '#1e1e1e',    # 背景色
            'grid': '#404040'           # 网格色
        }
    
    def generate_comprehensive_dashboard(self, save_path=None, show_chart=False):
        """
        生成综合分析仪表板
        
        Args:
            save_path: 保存路径
            show_chart: 是否显示图表
        """
        # 创建大型仪表板布局 - 专业交易软件风格
        fig = plt.figure(figsize=(24, 24))
        fig.patch.set_facecolor(self.colors['background'])

        # 设置专业样式
        plt.style.use('dark_background')

        # 创建专业交易软件风格布局 - 主图+5个副图占满整行
        gs = fig.add_gridspec(8, 4, height_ratios=[2.5, 0.8, 0.8, 0.8, 0.8, 0.8, 1.2, 1],
                             width_ratios=[1, 1, 1, 1], hspace=0.4, wspace=0.3)

        # 主图区域：价格走势 + 技术指标 (占用整行)
        ax_main = fig.add_subplot(gs[0, :])
        self._plot_price_technical_analysis(ax_main)

        # 副图1：MACD指标 (在价格图下方，占用整行)
        ax_macd_sub = fig.add_subplot(gs[1, :])
        self._plot_macd_subplot(ax_macd_sub)

        # 副图2：RSI指标 (在MACD下方，占用整行)
        ax_rsi_sub = fig.add_subplot(gs[2, :])
        self._plot_rsi_subplot(ax_rsi_sub)

        # 副图3：KDJ指标 (在RSI下方，占用整行)
        ax_kdj_sub = fig.add_subplot(gs[3, :])
        self._plot_kdj_subplot(ax_kdj_sub)

        # 副图4：成交量指标 (在KDJ下方，占用整行)
        ax_volume_sub = fig.add_subplot(gs[4, :])
        self._plot_volume_subplot(ax_volume_sub)

        # 副图5：资金流向指标 (在成交量下方，占用整行)
        ax_money_flow_sub = fig.add_subplot(gs[5, :])
        self._plot_money_flow_subplot(ax_money_flow_sub)

        # 第六行：分析图表区域
        # 市场情绪雷达图 (使用极坐标投影)
        ax_sentiment = fig.add_subplot(gs[6, 0], projection='polar')
        self._plot_sentiment_radar(ax_sentiment)

        # 策略推荐 (扩展占用更多空间)
        ax_strategy = fig.add_subplot(gs[6, 1:3])
        self._plot_strategy_recommendation(ax_strategy)

        # 波动率分析
        ax_volatility = fig.add_subplot(gs[6, 3])
        self._plot_volatility_analysis(ax_volatility)

        # 底部：综合评分
        ax_score = fig.add_subplot(gs[7, :])
        self._plot_comprehensive_score(ax_score)
        
        # 设置整体标题
        current_price = self._get_current_price()
        fig.suptitle(f'{self.display_name} - 专业投资分析仪表板\n当前价格: ¥{current_price:.2f} | 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M")}',
                    fontsize=18, color='white', y=0.98)
        
        # 保存和显示
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', 
                       facecolor=self.colors['background'], edgecolor='none')
            print(f"📊 专业仪表板已保存: {save_path}")
        
        if show_chart:
            plt.show()
        else:
            plt.close()
        
        return fig
    
    def _plot_price_technical_analysis(self, ax):
        """绘制价格走势和技术分析"""
        try:
            # 获取数据
            data = self.analysis_system.data_manager.get_daily_data()
            if data is None or len(data) == 0:
                ax.text(0.5, 0.5, '数据不可用', ha='center', va='center', transform=ax.transAxes, color='white')
                return

            # 获取技术指标
            indicators = self.analysis_system.tech_calculator.indicators
            if not indicators:
                self.analysis_system.tech_calculator.calculate_all_indicators()
                indicators = self.analysis_system.tech_calculator.indicators

            # 绘制价格线
            x_range = range(len(data))
            ax.plot(x_range, data['close'], color=self.colors['primary'], linewidth=2, label='价格走势')

            # 添加EMA指标
            if 'ema_12' in indicators and len(indicators['ema_12']) > 0:
                ax.plot(x_range, indicators['ema_12'], color=self.colors['success'], linewidth=1.5, alpha=0.8, label='EMA12')
            if 'ema_26' in indicators and len(indicators['ema_26']) > 0:
                ax.plot(x_range, indicators['ema_26'], color=self.colors['danger'], linewidth=1.5, alpha=0.8, label='EMA26')

            # 添加布林线
            if all(key in indicators for key in ['bb_upper', 'bb_middle', 'bb_lower']):
                ax.plot(x_range, indicators['bb_upper'], color=self.colors['warning'], linewidth=1, alpha=0.6, linestyle='--', label='布林上轨')
                ax.plot(x_range, indicators['bb_middle'], color=self.colors['info'], linewidth=1, alpha=0.6, label='布林中轨')
                ax.plot(x_range, indicators['bb_lower'], color=self.colors['warning'], linewidth=1, alpha=0.6, linestyle='--', label='布林下轨')
                # 填充布林线区域
                ax.fill_between(x_range, indicators['bb_upper'], indicators['bb_lower'], alpha=0.1, color=self.colors['warning'])

            # 标注支撑阻力位
            support_resistance = self.analysis_system.tech_calculator.get_support_resistance_levels()
            current_price = support_resistance['current_price']
            resistance = support_resistance['recent_resistance']
            support = support_resistance['recent_support']

            # 绘制支撑阻力线
            ax.axhline(y=resistance, color=self.colors['danger'], linestyle=':', alpha=0.8, label=f'阻力位 ¥{resistance:.2f}')
            ax.axhline(y=support, color=self.colors['success'], linestyle=':', alpha=0.8, label=f'支撑位 ¥{support:.2f}')

            # 标注当前价格
            ax.annotate(f'当前价格: ¥{current_price:.2f}',
                       xy=(len(data)-1, current_price),
                       xytext=(len(data)-30, current_price*1.05),
                       arrowprops=dict(arrowstyle='->', color='white', lw=1.5),
                       color='white', fontsize=12, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

            ax.set_title('价格走势 + 技术指标', color='white', fontsize=14)
            ax.set_ylabel('价格 (¥)', color='white')
            ax.legend(loc='upper left', fontsize=8)
            ax.grid(True, alpha=0.3, color=self.colors['grid'])

        except Exception as e:
            ax.text(0.5, 0.5, f'图表生成错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_macd_subplot(self, ax):
        """绘制MACD副图"""
        try:
            # 获取MACD数据
            indicators = self.analysis_system.tech_calculator.indicators
            if not indicators or 'macd' not in indicators:
                ax.text(0.5, 0.5, 'MACD数据不可用', ha='center', va='center', transform=ax.transAxes, color='white')
                return

            macd = indicators['macd'].dropna()
            macd_signal = indicators['macd_signal'].dropna()
            macd_histogram = indicators['macd_histogram'].dropna()

            x_range = range(len(macd))

            # 绘制MACD线和信号线
            ax.plot(x_range, macd, color=self.colors['primary'], linewidth=1.5, label='MACD', alpha=0.9)
            ax.plot(x_range, macd_signal, color=self.colors['warning'], linewidth=1.2, label='信号线', alpha=0.8)

            # 绘制MACD柱状图
            colors = [self.colors['success'] if h > 0 else self.colors['danger'] for h in macd_histogram]
            ax.bar(x_range, macd_histogram, color=colors, alpha=0.6, width=0.8, label='MACD柱')

            # 添加零轴线
            ax.axhline(y=0, color='white', linestyle='-', alpha=0.5, linewidth=0.8)

            # 标注当前MACD状态
            current_macd = macd.iloc[-1]
            current_signal = macd_signal.iloc[-1]
            trend = '多头' if current_macd > current_signal else '空头'
            trend_color = self.colors['success'] if current_macd > current_signal else self.colors['danger']

            ax.text(0.02, 0.95, f'MACD: {current_macd:.3f}',
                   transform=ax.transAxes, color='white', fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=trend_color, alpha=0.7))

            ax.text(0.02, 0.05, f'趋势: {trend}',
                   transform=ax.transAxes, color=trend_color, fontsize=9, fontweight='bold')

            ax.set_title('MACD指标', color='white', fontsize=11, pad=10)
            ax.set_ylabel('MACD', color='white', fontsize=9)
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.2, color=self.colors['grid'])

            # 隐藏x轴标签（因为是副图）
            ax.set_xticklabels([])

        except Exception as e:
            ax.text(0.5, 0.5, f'MACD副图错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_rsi_subplot(self, ax):
        """绘制RSI副图"""
        try:
            # 获取RSI数据
            indicators = self.analysis_system.tech_calculator.indicators
            if not indicators or 'rsi' not in indicators:
                ax.text(0.5, 0.5, 'RSI数据不可用', ha='center', va='center', transform=ax.transAxes, color='white')
                return

            rsi = indicators['rsi'].dropna()
            x_range = range(len(rsi))

            # 绘制RSI线
            ax.plot(x_range, rsi, color=self.colors['primary'], linewidth=2, label='RSI')

            # 添加超买超卖线
            ax.axhline(y=70, color=self.colors['danger'], linestyle='--', alpha=0.8, linewidth=1, label='超买(70)')
            ax.axhline(y=30, color=self.colors['success'], linestyle='--', alpha=0.8, linewidth=1, label='超卖(30)')
            ax.axhline(y=50, color=self.colors['info'], linestyle=':', alpha=0.6, linewidth=1, label='中线(50)')

            # 填充超买超卖区域
            ax.fill_between(x_range, 70, 100, alpha=0.1, color=self.colors['danger'])
            ax.fill_between(x_range, 0, 30, alpha=0.1, color=self.colors['success'])

            # 标注当前RSI值和状态
            current_rsi = rsi.iloc[-1]
            if current_rsi > 70:
                rsi_status = '超买'
                status_color = self.colors['danger']
            elif current_rsi < 30:
                rsi_status = '超卖'
                status_color = self.colors['success']
            else:
                rsi_status = '正常'
                status_color = self.colors['info']

            ax.text(0.02, 0.95, f'RSI: {current_rsi:.1f}',
                   transform=ax.transAxes, color='white', fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=status_color, alpha=0.7))

            ax.text(0.02, 0.05, f'状态: {rsi_status}',
                   transform=ax.transAxes, color=status_color, fontsize=9, fontweight='bold')

            ax.set_ylim(0, 100)
            ax.set_title('RSI相对强弱指标', color='white', fontsize=11, pad=10)
            ax.set_ylabel('RSI', color='white', fontsize=9)
            ax.set_xlabel('时间', color='white', fontsize=9)
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.2, color=self.colors['grid'])

        except Exception as e:
            ax.text(0.5, 0.5, f'RSI副图错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_kdj_subplot(self, ax):
        """绘制KDJ副图"""
        try:
            # 计算KDJ指标
            data = self.analysis_system.data_manager.get_daily_data()
            if data is None or len(data) < 14:
                ax.text(0.5, 0.5, 'KDJ数据不足', ha='center', va='center', transform=ax.transAxes, color='white')
                return

            # 计算KDJ
            high = data['high']
            low = data['low']
            close = data['close']

            # 计算RSV
            period = 9
            lowest_low = low.rolling(window=period).min()
            highest_high = high.rolling(window=period).max()
            rsv = (close - lowest_low) / (highest_high - lowest_low) * 100

            # 计算K、D、J值
            k = rsv.ewm(com=2).mean()
            d = k.ewm(com=2).mean()
            j = 3 * k - 2 * d

            x_range = range(len(k))

            # 绘制KDJ线
            ax.plot(x_range, k, color=self.colors['primary'], linewidth=1.5, label='K线', alpha=0.9)
            ax.plot(x_range, d, color=self.colors['warning'], linewidth=1.5, label='D线', alpha=0.9)
            ax.plot(x_range, j, color=self.colors['info'], linewidth=1.5, label='J线', alpha=0.9)

            # 添加超买超卖线
            ax.axhline(y=80, color=self.colors['danger'], linestyle='--', alpha=0.6, linewidth=1)
            ax.axhline(y=20, color=self.colors['success'], linestyle='--', alpha=0.6, linewidth=1)
            ax.axhline(y=50, color='white', linestyle=':', alpha=0.4, linewidth=1)

            # 填充超买超卖区域
            ax.fill_between(x_range, 80, 100, alpha=0.1, color=self.colors['danger'])
            ax.fill_between(x_range, 0, 20, alpha=0.1, color=self.colors['success'])

            # 标注当前KDJ值
            current_k = k.iloc[-1]
            current_d = d.iloc[-1]
            current_j = j.iloc[-1]

            # 判断KDJ状态
            if current_k > current_d:
                kdj_status = '多头'
                status_color = self.colors['success']
            else:
                kdj_status = '空头'
                status_color = self.colors['danger']

            ax.text(0.02, 0.95, f'K: {current_k:.1f}',
                   transform=ax.transAxes, color='white', fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=status_color, alpha=0.7))

            ax.text(0.02, 0.05, f'D: {current_d:.1f} | J: {current_j:.1f}',
                   transform=ax.transAxes, color=status_color, fontsize=9, fontweight='bold')

            ax.set_ylim(0, 100)
            ax.set_title('KDJ随机指标', color='white', fontsize=11, pad=10)
            ax.set_ylabel('KDJ', color='white', fontsize=9)
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.2, color=self.colors['grid'])

            # 隐藏x轴标签（因为是副图）
            ax.set_xticklabels([])

        except Exception as e:
            ax.text(0.5, 0.5, f'KDJ副图错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_volume_subplot(self, ax):
        """绘制成交量副图"""
        try:
            # 获取数据
            data = self.analysis_system.data_manager.get_daily_data()
            if data is None or len(data) == 0:
                ax.text(0.5, 0.5, '成交量数据不可用', ha='center', va='center', transform=ax.transAxes, color='white')
                return

            volume = data['volume']
            close = data['close']
            x_range = range(len(volume))

            # 计算价格变化，决定成交量柱的颜色
            price_change = close.diff()
            colors = [self.colors['success'] if change >= 0 else self.colors['danger']
                     for change in price_change]
            colors[0] = self.colors['info']  # 第一个数据点用中性色

            # 绘制成交量柱状图
            ax.bar(x_range, volume, color=colors, alpha=0.7, width=0.8)

            # 计算成交量移动平均线
            if len(volume) >= 20:
                volume_ma = volume.rolling(20).mean()
                ax.plot(x_range, volume_ma, color=self.colors['warning'], linewidth=1.5,
                       alpha=0.8, label='成交量MA20')

            # 标注当前成交量
            current_volume = volume.iloc[-1]
            avg_volume = volume.mean()
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1

            if volume_ratio > 1.5:
                volume_status = '放量'
                status_color = self.colors['warning']
            elif volume_ratio < 0.5:
                volume_status = '缩量'
                status_color = self.colors['info']
            else:
                volume_status = '正常'
                status_color = self.colors['success']

            ax.text(0.02, 0.95, f'成交量: {current_volume:.0f}',
                   transform=ax.transAxes, color='white', fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=status_color, alpha=0.7))

            ax.text(0.02, 0.05, f'状态: {volume_status} ({volume_ratio:.1f}x)',
                   transform=ax.transAxes, color=status_color, fontsize=9, fontweight='bold')

            ax.set_title('成交量', color='white', fontsize=11, pad=10)
            ax.set_ylabel('成交量', color='white', fontsize=9)
            if len(volume) >= 20:
                ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.2, color=self.colors['grid'], axis='y')

            # 隐藏x轴标签（因为是副图）
            ax.set_xticklabels([])

        except Exception as e:
            ax.text(0.5, 0.5, f'成交量副图错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_money_flow_subplot(self, ax):
        """绘制资金流向副图"""
        try:
            # 获取数据
            data = self.analysis_system.data_manager.get_daily_data()
            if data is None or len(data) < 10:
                ax.text(0.5, 0.5, '资金流向数据不足', ha='center', va='center', transform=ax.transAxes, color='white')
                return

            # 计算资金流向指标
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            money_flow = typical_price * data['volume']

            # 计算正负资金流
            price_change = typical_price.diff()
            positive_flow = money_flow.where(price_change > 0, 0)
            negative_flow = money_flow.where(price_change < 0, 0)

            # 计算资金流向比率
            period = 14
            positive_sum = positive_flow.rolling(period).sum()
            negative_sum = negative_flow.rolling(period).sum().abs()
            money_flow_ratio = positive_sum / (positive_sum + negative_sum) * 100

            x_range = range(len(money_flow_ratio))

            # 绘制资金流向比率
            ax.plot(x_range, money_flow_ratio, color=self.colors['primary'], linewidth=2, label='资金流向比率')

            # 添加参考线
            ax.axhline(y=80, color=self.colors['danger'], linestyle='--', alpha=0.6, linewidth=1)
            ax.axhline(y=20, color=self.colors['success'], linestyle='--', alpha=0.6, linewidth=1)
            ax.axhline(y=50, color='white', linestyle=':', alpha=0.4, linewidth=1)

            # 填充区域
            ax.fill_between(x_range, 50, money_flow_ratio,
                           where=(money_flow_ratio >= 50), alpha=0.2, color=self.colors['success'])
            ax.fill_between(x_range, 50, money_flow_ratio,
                           where=(money_flow_ratio < 50), alpha=0.2, color=self.colors['danger'])

            # 标注当前状态
            current_ratio = money_flow_ratio.iloc[-1]
            if current_ratio > 80:
                flow_status = '强势流入'
                status_color = self.colors['success']
            elif current_ratio < 20:
                flow_status = '强势流出'
                status_color = self.colors['danger']
            else:
                flow_status = '平衡'
                status_color = self.colors['info']

            ax.text(0.02, 0.95, f'资金流向: {current_ratio:.1f}%',
                   transform=ax.transAxes, color='white', fontsize=9, fontweight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor=status_color, alpha=0.7))

            ax.text(0.02, 0.05, f'状态: {flow_status}',
                   transform=ax.transAxes, color=status_color, fontsize=9, fontweight='bold')

            ax.set_ylim(0, 100)
            ax.set_title('资金流向指标', color='white', fontsize=11, pad=10)
            ax.set_ylabel('资金流向(%)', color='white', fontsize=9)
            ax.set_xlabel('时间', color='white', fontsize=9)
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.2, color=self.colors['grid'])

        except Exception as e:
            ax.text(0.5, 0.5, f'资金流向副图错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_sentiment_radar(self, ax):
        """绘制市场情绪雷达图"""
        try:
            # 获取真实的市场情绪数据
            sentiment_data = self.analysis_system.analysis_results.get('market_sentiment', {})

            if sentiment_data:
                # 从真实数据中提取情绪指标
                categories = ['技术面', '基本面', '情绪面', '风险度', '流动性', '趋势强度']

                # 计算各维度得分 (0-1之间)
                technical_score = self._get_technical_sentiment_score()
                fundamental_score = self._get_fundamental_sentiment_score()
                emotion_score = sentiment_data.get('comprehensive_sentiment', {}).get('overall_sentiment_score', 0.5) / 100
                risk_score = 1 - self._get_risk_sentiment_score()  # 风险越低，情绪越好
                liquidity_score = self._get_liquidity_sentiment_score()
                trend_score = self._get_trend_sentiment_score()

                values = [technical_score, fundamental_score, emotion_score, risk_score, liquidity_score, trend_score]
            else:
                # 如果没有情绪数据，使用技术指标计算基础情绪
                categories = ['技术面', '基本面', '情绪面', '风险度', '流动性', '趋势强度']
                values = [
                    self._get_technical_sentiment_score(),
                    self._get_fundamental_sentiment_score(),
                    0.5,  # 默认中性情绪
                    1 - self._get_risk_sentiment_score(),
                    self._get_liquidity_sentiment_score(),
                    self._get_trend_sentiment_score()
                ]

            # 计算角度
            angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
            values += values[:1]  # 闭合图形
            angles += angles[:1]

            # 绘制雷达图
            ax.plot(angles, values, 'o-', linewidth=2.5, color=self.colors['primary'],
                   markersize=6, markerfacecolor=self.colors['primary'], markeredgecolor='white', markeredgewidth=1)
            ax.fill(angles, values, alpha=0.25, color=self.colors['primary'])

            # 设置极坐标参数
            ax.set_theta_offset(np.pi / 2)  # 从顶部开始
            ax.set_theta_direction(-1)  # 顺时针方向

            # 设置标签
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(categories, color='white', fontsize=9, fontweight='bold')
            ax.set_ylim(0, 1)
            ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
            ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'], color='white', fontsize=8)
            ax.set_title('市场情绪雷达', color='white', fontsize=12, pad=20)
            ax.grid(True, alpha=0.3, color=self.colors['grid'])

            # 添加数值标签
            for angle, value, category in zip(angles[:-1], values[:-1], categories):
                # 计算标签位置，稍微向外偏移
                label_radius = value + 0.08
                ax.text(angle, label_radius, f'{value:.2f}',
                       ha='center', va='center', color='white', fontsize=8, fontweight='bold',
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='black', alpha=0.7))

            # 添加中心点
            ax.plot(0, 0, 'o', markersize=4, color='white', alpha=0.8)

        except Exception as e:
            ax.text(0.5, 0.5, f'雷达图错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')
    
    def _plot_backtest_results(self, ax):
        """绘制回测结果"""
        try:
            # 获取真实的回测数据
            backtest_data = self.analysis_system.analysis_results.get('backtest_results', {})
            strategy_data = self.analysis_system.analysis_results.get('strategy_selection', {})

            if backtest_data and 'performance_metrics' in backtest_data:
                # 使用真实的回测数据
                performance = backtest_data['performance_metrics']

                # 创建简化的收益曲线展示
                total_return = performance.get('total_return', 0)
                sharpe_ratio = performance.get('sharpe_ratio', 0)
                max_drawdown = performance.get('max_drawdown', 0)
                win_rate = performance.get('win_rate', 0)

                # 绘制关键指标
                metrics = ['总收益率', '夏普比率', '最大回撤', '胜率']
                values = [total_return, sharpe_ratio * 10, abs(max_drawdown), win_rate]  # 调整比例便于显示
                colors = [
                    self.colors['success'] if total_return > 0 else self.colors['danger'],
                    self.colors['success'] if sharpe_ratio > 1 else self.colors['warning'] if sharpe_ratio > 0 else self.colors['danger'],
                    self.colors['success'] if abs(max_drawdown) < 10 else self.colors['warning'] if abs(max_drawdown) < 20 else self.colors['danger'],
                    self.colors['success'] if win_rate > 60 else self.colors['warning'] if win_rate > 40 else self.colors['danger']
                ]

                bars = ax.bar(metrics, values, color=colors, alpha=0.8)

                # 添加数值标签
                for i, (bar, value, metric) in enumerate(zip(bars, values, metrics)):
                    if metric == '总收益率':
                        label = f'{value:.1f}%'
                    elif metric == '夏普比率':
                        label = f'{value/10:.2f}'
                    elif metric == '最大回撤':
                        label = f'{value:.1f}%'
                    else:  # 胜率
                        label = f'{value:.1f}%'

                    ax.text(i, value + max(values) * 0.02, label, ha='center', va='bottom',
                           color='white', fontsize=9, fontweight='bold')

                # 添加策略信息
                strategy_name = strategy_data.get('recommended_strategy', '未知策略')
                ax.text(0.02, 0.95, f'策略: {strategy_name}', transform=ax.transAxes,
                       color='white', fontsize=9, bbox=dict(boxstyle='round', facecolor='black', alpha=0.7))

            else:
                # 如果没有回测数据，显示基于当前分析的预期表现
                data = self.analysis_system.data_manager.get_daily_data()
                if data is not None and len(data) > 30:
                    # 基于历史数据计算简单的表现指标
                    returns = data['close'].pct_change().dropna()

                    # 计算基础指标
                    total_return = (data['close'].iloc[-1] / data['close'].iloc[0] - 1) * 100
                    volatility = returns.std() * np.sqrt(252) * 100  # 年化波动率
                    sharpe_estimate = total_return / volatility if volatility > 0 else 0
                    max_dd = ((data['close'] / data['close'].cummax()) - 1).min() * 100

                    metrics = ['历史收益', '年化波动', '夏普估值', '最大回撤']
                    values = [total_return, volatility, sharpe_estimate * 10, abs(max_dd)]

                    colors = [self.colors['info']] * 4  # 使用信息色表示这是历史数据

                    bars = ax.bar(metrics, values, color=colors, alpha=0.8)

                    # 添加数值标签
                    for i, (bar, value, metric) in enumerate(zip(bars, values, metrics)):
                        if metric in ['历史收益', '年化波动', '最大回撤']:
                            label = f'{value:.1f}%'
                        else:  # 夏普估值
                            label = f'{value/10:.2f}'

                        ax.text(i, value + max(values) * 0.02, label, ha='center', va='bottom',
                               color='white', fontsize=9, fontweight='bold')

                    ax.text(0.02, 0.95, '基于历史数据估算', transform=ax.transAxes,
                           color='white', fontsize=9, bbox=dict(boxstyle='round', facecolor='blue', alpha=0.7))
                else:
                    ax.text(0.5, 0.5, '回测数据不可用', ha='center', va='center', transform=ax.transAxes, color='white')
                    return

            ax.set_title('策略表现分析', color='white', fontsize=12)
            ax.set_ylabel('指标值', color='white')
            ax.grid(True, alpha=0.3, color=self.colors['grid'], axis='y')
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')

        except Exception as e:
            ax.text(0.5, 0.5, f'回测图错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')
    


    def _convert_risk_level(self, risk_level):
        """将风险等级转换为数值"""
        risk_mapping = {
            'LOW': 0.2,
            'MEDIUM': 0.5,
            'HIGH': 0.8,
            '低': 0.2,
            '中': 0.5,
            '高': 0.8
        }
        return risk_mapping.get(risk_level, 0.5)
    
    def _plot_strategy_recommendation(self, ax):
        """绘制策略推荐"""
        try:
            # 获取真实的策略推荐数据
            strategy_data = self.analysis_system.analysis_results.get('strategy_selection', {})
            trading_advice = self.analysis_system.analysis_results.get('trading_advice', {})

            if strategy_data and 'recommended_strategy' in strategy_data:
                # 使用策略选择系统的数据
                recommended_strategy = strategy_data['recommended_strategy']
                strategy_confidence = strategy_data.get('strategy_confidence', 70)

                # 根据推荐策略计算概率分布
                if 'buy' in recommended_strategy.lower() or 'bullish' in recommended_strategy.lower():
                    probabilities = [strategy_confidence/100, (100-strategy_confidence)/2/100, (100-strategy_confidence)/2/100]
                elif 'sell' in recommended_strategy.lower() or 'bearish' in recommended_strategy.lower():
                    probabilities = [(100-strategy_confidence)/2/100, (100-strategy_confidence)/2/100, strategy_confidence/100]
                else:
                    probabilities = [0.2, 0.6, 0.2]  # 持有为主
            elif trading_advice and 'action' in trading_advice:
                # 使用交易建议数据
                action = trading_advice['action']
                confidence = trading_advice.get('confidence', 50)

                if action == 'BUY':
                    probabilities = [confidence/100, (100-confidence)/2/100, (100-confidence)/2/100]
                elif action == 'SELL':
                    probabilities = [(100-confidence)/2/100, (100-confidence)/2/100, confidence/100]
                else:  # HOLD
                    probabilities = [0.2, 0.6, 0.2]
            else:
                # 默认中性策略
                probabilities = [0.3, 0.4, 0.3]

            strategies = ['买入', '持有', '卖出']
            colors = [self.colors['success'], self.colors['warning'], self.colors['danger']]

            wedges, texts, autotexts = ax.pie(probabilities, labels=strategies, colors=colors,
                                            autopct='%1.1f%%', startangle=90)

            # 设置文字颜色
            for text in texts:
                text.set_color('white')
                text.set_fontsize(10)
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')
                autotext.set_fontsize(9)

            # 添加推荐策略信息
            if strategy_data and 'recommended_strategy' in strategy_data:
                strategy_name = strategy_data['recommended_strategy']
                ax.text(0, -1.3, f'推荐策略: {strategy_name}',
                       ha='center', va='center', color='white', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

            ax.set_title('策略推荐', color='white', fontsize=12)

        except Exception as e:
            ax.text(0.5, 0.5, f'策略图错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')
    
    def _plot_volume_analysis(self, ax):
        """绘制成交量分析"""
        try:
            # 获取成交量数据
            data = self.analysis_system.data_manager.get_hourly_data()
            if data is None or len(data) == 0:
                ax.text(0.5, 0.5, '成交量数据不可用', ha='center', va='center', transform=ax.transAxes, color='white')
                return
            
            volume = data['volume'].tail(50)  # 最近50个数据点
            ax.bar(range(len(volume)), volume, color=self.colors['info'], alpha=0.7)
            
            # 添加平均线
            avg_volume = volume.mean()
            ax.axhline(y=avg_volume, color=self.colors['warning'], linestyle='--', alpha=0.8, label=f'平均: {avg_volume:.0f}')
            
            ax.set_title('成交量分析', color='white', fontsize=12)
            ax.set_ylabel('成交量', color='white')
            ax.legend(loc='upper right')
            ax.grid(True, alpha=0.3, color=self.colors['grid'])
            
        except Exception as e:
            ax.text(0.5, 0.5, f'成交量图错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')
    
    def _plot_volatility_analysis(self, ax):
        """绘制波动率分析"""
        try:
            # 计算波动率
            data = self.analysis_system.data_manager.get_hourly_data()
            if data is None or len(data) == 0:
                ax.text(0.5, 0.5, '波动率数据不可用', ha='center', va='center', transform=ax.transAxes, color='white')
                return
            
            returns = data['close'].pct_change().dropna()
            volatility = returns.rolling(24).std() * np.sqrt(24) * 100  # 24小时波动率
            
            ax.plot(range(len(volatility)), volatility, color=self.colors['danger'], linewidth=2)
            ax.fill_between(range(len(volatility)), volatility, alpha=0.3, color=self.colors['danger'])
            
            current_vol = volatility.iloc[-1] if len(volatility) > 0 else 0
            ax.text(0.05, 0.95, f'当前波动率: {current_vol:.1f}%', transform=ax.transAxes, 
                   color='white', fontsize=10, bbox=dict(boxstyle='round', facecolor='black', alpha=0.7))
            
            ax.set_title('波动率分析', color='white', fontsize=12)
            ax.set_ylabel('波动率 (%)', color='white')
            ax.grid(True, alpha=0.3, color=self.colors['grid'])
            
        except Exception as e:
            ax.text(0.5, 0.5, f'波动率图错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')
    
    def _plot_returns_distribution(self, ax):
        """绘制收益分布"""
        try:
            # 计算收益率分布
            data = self.analysis_system.data_manager.get_hourly_data()
            if data is None or len(data) == 0:
                ax.text(0.5, 0.5, '收益数据不可用', ha='center', va='center', transform=ax.transAxes, color='white')
                return
            
            returns = data['close'].pct_change().dropna() * 100
            
            ax.hist(returns, bins=30, color=self.colors['primary'], alpha=0.7, edgecolor='white')
            
            # 添加统计信息
            mean_return = returns.mean()
            std_return = returns.std()
            ax.axvline(mean_return, color=self.colors['warning'], linestyle='--', label=f'均值: {mean_return:.2f}%')
            
            ax.set_title('收益率分布', color='white', fontsize=12)
            ax.set_xlabel('收益率 (%)', color='white')
            ax.set_ylabel('频次', color='white')
            ax.legend(loc='upper right')
            ax.grid(True, alpha=0.3, color=self.colors['grid'])
            
        except Exception as e:
            ax.text(0.5, 0.5, f'分布图错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')
    
    def _plot_comprehensive_score(self, ax):
        """绘制综合评分"""
        try:
            # 综合评分指标 (移除策略适配项)
            metrics = ['技术分析', '基本面', '风险控制', '流动性', '趋势强度', '市场情绪']

            # 基于真实数据计算各项得分
            technical_score = self._calculate_technical_score()
            fundamental_score = self._calculate_fundamental_score()
            risk_score = self._calculate_risk_control_score()
            liquidity_score = self._calculate_liquidity_score_for_comprehensive()
            trend_score = self._calculate_trend_strength_score()
            sentiment_score = self._calculate_sentiment_score_for_comprehensive()

            scores = [technical_score, fundamental_score, risk_score, liquidity_score,
                     trend_score, sentiment_score]

            # 添加调试信息，检查市场情绪得分
            print(f"🔍 市场情绪得分调试: {sentiment_score}")
            sentiment_data = self.analysis_system.analysis_results.get('market_sentiment', {})
            if sentiment_data:
                print(f"🔍 找到市场情绪数据: {sentiment_data.keys()}")
                if 'comprehensive_sentiment' in sentiment_data:
                    comp_sentiment = sentiment_data['comprehensive_sentiment']
                    print(f"🔍 综合情绪数据: {comp_sentiment}")
                    overall_sentiment = comp_sentiment.get('overall_sentiment_score', 'N/A')
                    print(f"🔍 整体情绪得分: {overall_sentiment}")
            else:
                print("⚠️ 没有找到市场情绪数据，使用技术指标估算")

            # 创建水平条形图
            bars = ax.barh(metrics, scores, color=[
                self.colors['success'] if score >= 80 else
                self.colors['warning'] if score >= 60 else
                self.colors['danger'] for score in scores
            ], alpha=0.8)

            # 添加分数标签
            for i, (bar, score) in enumerate(zip(bars, scores)):
                grade = 'A' if score >= 90 else 'B' if score >= 80 else 'C' if score >= 60 else 'D'
                ax.text(score + 1, i, f'{score:.0f}分({grade})', va='center', color='white', fontsize=9, fontweight='bold')

            # 计算总分和等级
            total_score = np.mean(scores)
            overall_grade = 'A' if total_score >= 90 else 'B' if total_score >= 80 else 'C' if total_score >= 60 else 'D'

            # 投资建议
            if total_score >= 80:
                investment_advice = '强烈推荐'
                advice_color = self.colors['success']
            elif total_score >= 60:
                investment_advice = '谨慎推荐'
                advice_color = self.colors['warning']
            else:
                investment_advice = '不建议投资'
                advice_color = self.colors['danger']

            ax.text(0.95, 0.95, f'综合评分: {total_score:.0f}分({overall_grade}级)\n投资建议: {investment_advice}',
                   transform=ax.transAxes, color='white', fontsize=12, fontweight='bold', ha='right',
                   bbox=dict(boxstyle='round', facecolor=advice_color, alpha=0.8))

            ax.set_xlim(0, 100)
            ax.set_title('综合评分体系', color='white', fontsize=14)
            ax.set_xlabel('评分 (0-100)', color='white')
            ax.grid(True, alpha=0.3, color=self.colors['grid'], axis='x')

        except Exception as e:
            ax.text(0.5, 0.5, f'评分图错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _calculate_technical_score(self):
        """计算技术分析得分"""
        try:
            signals = self.analysis_system.analysis_results.get('current_signals', {})
            if not signals:
                return 50

            score = 50  # 基础分数

            # RSI得分
            rsi = signals.get('rsi', 50)
            if 30 <= rsi <= 70:
                score += 15
            elif rsi < 30:
                score += 20  # 超卖机会
            else:
                score -= 10  # 超买风险

            # MACD得分
            if signals.get('macd_trend') == 'BULLISH':
                score += 15
            else:
                score -= 10

            # EMA趋势得分
            if signals.get('ema_trend') == 'UP':
                score += 10
            else:
                score -= 5

            return max(0, min(100, score))
        except:
            return 50

    def _calculate_fundamental_score(self):
        """计算基本面得分"""
        try:
            snapshot = self.analysis_system.analysis_results.get('fundamental_snapshot', {})
            if not snapshot:
                return 50

            score = 50

            # 求购溢价得分
            bid_premium = snapshot.get('bid_premium', 0)
            if bid_premium > 5:
                score += 25
            elif bid_premium > 2:
                score += 15
            elif bid_premium > 0:
                score += 5
            else:
                score -= 15

            # 供给比例得分
            supply_ratio = snapshot.get('supply_ratio', 50)
            if supply_ratio < 20:
                score += 15  # 稀缺性高
            elif supply_ratio < 40:
                score += 10
            elif supply_ratio > 80:
                score -= 15  # 供给过剩

            return max(0, min(100, score))
        except:
            return 50

    def _calculate_risk_control_score(self):
        """计算风险控制得分"""
        try:
            risk_data = self.analysis_system.analysis_results.get('risk_assessment', {})
            if not risk_data:
                return 50

            overall_risk = risk_data.get('overall_risk', 'MEDIUM')
            if overall_risk == 'LOW':
                return 85
            elif overall_risk == 'MEDIUM':
                return 65
            else:
                return 35
        except:
            return 50

    def _calculate_liquidity_score_for_comprehensive(self):
        """计算流动性得分（用于综合评分）"""
        try:
            market_chars = self.analysis_system.analysis_results.get('market_characteristics', {})
            if not market_chars:
                return 50

            liquidity_level = market_chars.get('liquidity_level', '中等流动性')
            avg_volume = market_chars.get('avg_daily_volume', 0)

            base_score = 50
            if '高流动性' in liquidity_level:
                base_score = 80
            elif '中等流动性' in liquidity_level:
                base_score = 60
            else:
                base_score = 30

            # 根据成交量调整
            if avg_volume > 100:
                base_score += 10
            elif avg_volume > 50:
                base_score += 5

            return max(0, min(100, base_score))
        except:
            return 50

    def _calculate_trend_strength_score(self):
        """计算趋势强度得分"""
        try:
            multi_timeframe = self.analysis_system.analysis_results.get('multi_timeframe', {})
            signals = self.analysis_system.analysis_results.get('current_signals', {})

            score = 50

            # 短期趋势
            if signals.get('ema_trend') == 'UP':
                score += 15
            elif signals.get('ema_trend') == 'DOWN':
                score -= 10

            # 中期趋势
            if signals.get('macd_trend') == 'BULLISH':
                score += 15
            elif signals.get('macd_trend') == 'BEARISH':
                score -= 10

            # 趋势一致性
            consistency = multi_timeframe.get('trend_consistency', '趋势分歧')
            if '强烈上升' in consistency:
                score += 20
            elif '强烈下降' in consistency:
                score -= 15
            elif '趋势分歧' in consistency:
                score -= 5

            return max(0, min(100, score))
        except:
            return 50

    def _calculate_sentiment_score_for_comprehensive(self):
        """计算市场情绪得分（用于综合评分）"""
        try:
            sentiment_data = self.analysis_system.analysis_results.get('market_sentiment', {})

            # 添加详细调试信息
            print(f"🔍 [情绪调试] 原始情绪数据: {sentiment_data}")

            if sentiment_data and 'comprehensive_sentiment' in sentiment_data:
                comp_sentiment = sentiment_data['comprehensive_sentiment']
                print(f"🔍 [情绪调试] 综合情绪数据: {comp_sentiment}")

                overall_score = comp_sentiment.get('overall_sentiment_score', None)
                print(f"🔍 [情绪调试] 整体情绪得分: {overall_score}")

                if overall_score is not None:
                    # 确保得分在合理范围内
                    final_score = max(0, min(100, overall_score))
                    print(f"🔍 [情绪调试] 最终情绪得分: {final_score}")
                    return final_score

            # 如果没有情绪数据，基于技术指标估算
            print("⚠️ [情绪调试] 没有找到综合情绪数据，使用技术指标估算")
            technical_score = self._get_technical_sentiment_score() * 100
            print(f"🔍 [情绪调试] 技术指标估算得分: {technical_score}")
            return max(0, min(100, technical_score))

        except Exception as e:
            print(f"❌ [情绪调试] 计算情绪得分时出错: {e}")
            return 50



    def _get_current_price(self):
        """获取当前价格"""
        try:
            data = self.analysis_system.data_manager.get_daily_data()
            if data is not None and len(data) > 0:
                return data['close'].iloc[-1]
            return 0
        except:
            return 0

    def _get_technical_sentiment_score(self):
        """计算技术面情绪得分"""
        try:
            signals = self.analysis_system.analysis_results.get('current_signals', {})
            if not signals:
                return 0.5

            score = 0.5  # 基础分数

            # RSI贡献
            rsi = signals.get('rsi', 50)
            if 30 <= rsi <= 70:
                score += 0.2  # RSI在正常范围
            elif rsi < 30:
                score += 0.3  # 超卖，可能反弹
            else:
                score -= 0.2  # 超买，风险较高

            # MACD贡献
            if signals.get('macd_trend') == 'BULLISH':
                score += 0.2
            else:
                score -= 0.1

            # EMA趋势贡献
            if signals.get('ema_trend') == 'UP':
                score += 0.1
            else:
                score -= 0.1

            return max(0, min(1, score))
        except:
            return 0.5

    def _get_fundamental_sentiment_score(self):
        """计算基本面情绪得分"""
        try:
            snapshot = self.analysis_system.analysis_results.get('fundamental_snapshot', {})
            if not snapshot:
                return 0.5

            score = 0.5

            # 求购溢价贡献
            bid_premium = snapshot.get('bid_premium', 0)
            if bid_premium > 2:
                score += 0.3
            elif bid_premium > 0:
                score += 0.1
            else:
                score -= 0.2

            # 供给比例贡献
            supply_ratio = snapshot.get('supply_ratio', 50)
            if supply_ratio < 30:
                score += 0.2  # 供给稀缺
            elif supply_ratio > 70:
                score -= 0.2  # 供给过剩

            return max(0, min(1, score))
        except:
            return 0.5

    def _get_risk_sentiment_score(self):
        """计算风险情绪得分"""
        try:
            risk_data = self.analysis_system.analysis_results.get('risk_assessment', {})
            if not risk_data:
                return 0.5

            overall_risk = risk_data.get('overall_risk', 'MEDIUM')
            if overall_risk == 'LOW':
                return 0.2
            elif overall_risk == 'MEDIUM':
                return 0.5
            else:
                return 0.8
        except:
            return 0.5

    def _get_liquidity_sentiment_score(self):
        """计算流动性情绪得分"""
        try:
            market_chars = self.analysis_system.analysis_results.get('market_characteristics', {})
            if not market_chars:
                return 0.5

            liquidity_level = market_chars.get('liquidity_level', '中等流动性')
            if '高流动性' in liquidity_level:
                return 0.8
            elif '中等流动性' in liquidity_level:
                return 0.6
            else:
                return 0.3
        except:
            return 0.5

    def _get_trend_sentiment_score(self):
        """计算趋势强度情绪得分"""
        try:
            multi_timeframe = self.analysis_system.analysis_results.get('multi_timeframe', {})
            if not multi_timeframe:
                return 0.5

            trend_consistency = multi_timeframe.get('trend_consistency', '趋势分歧')
            if '强烈上升' in trend_consistency:
                return 0.9
            elif '强烈下降' in trend_consistency:
                return 0.1
            elif '趋势分歧' in trend_consistency:
                return 0.4
            else:
                return 0.5
        except:
            return 0.5

    def _plot_rsi_indicator(self, ax):
        """绘制RSI指标"""
        try:
            # 获取RSI数据
            indicators = self.analysis_system.tech_calculator.indicators
            if not indicators or 'rsi' not in indicators:
                ax.text(0.5, 0.5, 'RSI数据不可用', ha='center', va='center', transform=ax.transAxes, color='white')
                return

            rsi = indicators['rsi'].dropna()
            x_range = range(len(rsi))

            # 绘制RSI线
            ax.plot(x_range, rsi, color=self.colors['primary'], linewidth=2, label='RSI')

            # 添加超买超卖线
            ax.axhline(y=70, color=self.colors['danger'], linestyle='--', alpha=0.8, label='超买线(70)')
            ax.axhline(y=30, color=self.colors['success'], linestyle='--', alpha=0.8, label='超卖线(30)')
            ax.axhline(y=50, color=self.colors['info'], linestyle=':', alpha=0.6, label='中线(50)')

            # 填充超买超卖区域
            ax.fill_between(x_range, 70, 100, alpha=0.1, color=self.colors['danger'])
            ax.fill_between(x_range, 0, 30, alpha=0.1, color=self.colors['success'])

            # 标注当前RSI值
            current_rsi = rsi.iloc[-1]
            rsi_status = '超买' if current_rsi > 70 else '超卖' if current_rsi < 30 else '正常'
            ax.text(0.02, 0.95, f'当前RSI: {current_rsi:.1f} ({rsi_status})',
                   transform=ax.transAxes, color='white', fontsize=10,
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

            ax.set_ylim(0, 100)
            ax.set_title('RSI相对强弱指标', color='white', fontsize=12)
            ax.set_ylabel('RSI值', color='white')
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.3, color=self.colors['grid'])

        except Exception as e:
            ax.text(0.5, 0.5, f'RSI图表错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_macd_indicator(self, ax):
        """绘制MACD指标"""
        try:
            # 获取MACD数据
            indicators = self.analysis_system.tech_calculator.indicators
            if not indicators or 'macd' not in indicators:
                ax.text(0.5, 0.5, 'MACD数据不可用', ha='center', va='center', transform=ax.transAxes, color='white')
                return

            macd = indicators['macd'].dropna()
            macd_signal = indicators['macd_signal'].dropna()
            macd_histogram = indicators['macd_histogram'].dropna()

            x_range = range(len(macd))

            # 绘制MACD线和信号线
            ax.plot(x_range, macd, color=self.colors['primary'], linewidth=2, label='MACD')
            ax.plot(x_range, macd_signal, color=self.colors['warning'], linewidth=1.5, label='信号线')

            # 绘制MACD柱状图
            colors = [self.colors['success'] if h > 0 else self.colors['danger'] for h in macd_histogram]
            ax.bar(x_range, macd_histogram, color=colors, alpha=0.6, width=0.8, label='MACD柱')

            # 添加零轴线
            ax.axhline(y=0, color='white', linestyle='-', alpha=0.5)

            # 标注当前MACD状态
            current_macd = macd.iloc[-1]
            current_signal = macd_signal.iloc[-1]
            trend = '多头' if current_macd > current_signal else '空头'
            ax.text(0.02, 0.95, f'MACD: {current_macd:.4f}\n趋势: {trend}',
                   transform=ax.transAxes, color='white', fontsize=9,
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

            ax.set_title('MACD指标', color='white', fontsize=12)
            ax.set_ylabel('MACD值', color='white')
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.3, color=self.colors['grid'])

        except Exception as e:
            ax.text(0.5, 0.5, f'MACD图表错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_kdj_indicator(self, ax):
        """绘制KDJ指标"""
        try:
            # 计算KDJ指标
            data = self.analysis_system.data_manager.get_daily_data()
            if data is None or len(data) < 14:
                ax.text(0.5, 0.5, 'KDJ数据不足', ha='center', va='center', transform=ax.transAxes, color='white')
                return

            # 计算KDJ
            high = data['high']
            low = data['low']
            close = data['close']

            # 计算RSV
            period = 9
            lowest_low = low.rolling(window=period).min()
            highest_high = high.rolling(window=period).max()
            rsv = (close - lowest_low) / (highest_high - lowest_low) * 100

            # 计算K、D、J值
            k = rsv.ewm(com=2).mean()
            d = k.ewm(com=2).mean()
            j = 3 * k - 2 * d

            x_range = range(len(k))

            # 绘制KDJ线
            ax.plot(x_range, k, color=self.colors['primary'], linewidth=2, label='K线')
            ax.plot(x_range, d, color=self.colors['warning'], linewidth=2, label='D线')
            ax.plot(x_range, j, color=self.colors['info'], linewidth=2, label='J线')

            # 添加超买超卖线
            ax.axhline(y=80, color=self.colors['danger'], linestyle='--', alpha=0.8)
            ax.axhline(y=20, color=self.colors['success'], linestyle='--', alpha=0.8)
            ax.axhline(y=50, color='white', linestyle=':', alpha=0.5)

            # 填充超买超卖区域
            ax.fill_between(x_range, 80, 100, alpha=0.1, color=self.colors['danger'])
            ax.fill_between(x_range, 0, 20, alpha=0.1, color=self.colors['success'])

            # 标注当前KDJ值
            current_k = k.iloc[-1]
            current_d = d.iloc[-1]
            current_j = j.iloc[-1]
            ax.text(0.02, 0.95, f'K: {current_k:.1f}\nD: {current_d:.1f}\nJ: {current_j:.1f}',
                   transform=ax.transAxes, color='white', fontsize=9,
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

            ax.set_ylim(0, 100)
            ax.set_title('KDJ随机指标', color='white', fontsize=12)
            ax.set_ylabel('KDJ值', color='white')
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.3, color=self.colors['grid'])

        except Exception as e:
            ax.text(0.5, 0.5, f'KDJ图表错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_money_flow(self, ax):
        """绘制资金流向"""
        try:
            # 获取数据
            data = self.analysis_system.data_manager.get_daily_data()
            if data is None or len(data) < 10:
                ax.text(0.5, 0.5, '资金流向数据不足', ha='center', va='center', transform=ax.transAxes, color='white')
                return

            # 计算资金流向指标
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            money_flow = typical_price * data['volume']

            # 计算正负资金流
            price_change = typical_price.diff()
            positive_flow = money_flow.where(price_change > 0, 0)
            negative_flow = money_flow.where(price_change < 0, 0)

            # 计算资金流向比率
            period = 14
            positive_sum = positive_flow.rolling(period).sum()
            negative_sum = negative_flow.rolling(period).sum().abs()
            money_flow_ratio = positive_sum / (positive_sum + negative_sum) * 100

            x_range = range(len(money_flow_ratio))

            # 绘制资金流向比率
            ax.plot(x_range, money_flow_ratio, color=self.colors['primary'], linewidth=2, label='资金流向比率')

            # 添加参考线
            ax.axhline(y=80, color=self.colors['danger'], linestyle='--', alpha=0.8, label='强势线(80)')
            ax.axhline(y=20, color=self.colors['success'], linestyle='--', alpha=0.8, label='弱势线(20)')
            ax.axhline(y=50, color='white', linestyle=':', alpha=0.6, label='中性线(50)')

            # 填充区域
            ax.fill_between(x_range, 50, money_flow_ratio,
                           where=(money_flow_ratio >= 50), alpha=0.3, color=self.colors['success'], label='资金流入')
            ax.fill_between(x_range, 50, money_flow_ratio,
                           where=(money_flow_ratio < 50), alpha=0.3, color=self.colors['danger'], label='资金流出')

            # 标注当前状态
            current_ratio = money_flow_ratio.iloc[-1]
            flow_status = '强势流入' if current_ratio > 80 else '弱势流出' if current_ratio < 20 else '中性'
            ax.text(0.02, 0.95, f'资金流向: {current_ratio:.1f}%\n状态: {flow_status}',
                   transform=ax.transAxes, color='white', fontsize=9,
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

            ax.set_ylim(0, 100)
            ax.set_title('资金流向指标', color='white', fontsize=12)
            ax.set_ylabel('资金流向比率(%)', color='white')
            ax.legend(loc='upper right', fontsize=8)
            ax.grid(True, alpha=0.3, color=self.colors['grid'])

        except Exception as e:
            ax.text(0.5, 0.5, f'资金流向图表错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_market_strength(self, ax):
        """绘制市场强度"""
        try:
            # 获取市场特征数据
            market_chars = self.analysis_system.analysis_results.get('market_characteristics', {})
            sentiment_data = self.analysis_system.analysis_results.get('market_sentiment', {})

            # 计算市场强度各维度得分
            dimensions = ['成交活跃度', '价格稳定性', '流动性', '情绪强度', '趋势一致性']

            # 基于真实数据计算得分
            activity_score = self._calculate_activity_score(market_chars)
            stability_score = self._calculate_stability_score()
            liquidity_score = self._calculate_liquidity_score(market_chars)
            sentiment_score = self._calculate_sentiment_strength(sentiment_data)
            trend_score = self._calculate_trend_consistency()

            scores = [activity_score, stability_score, liquidity_score, sentiment_score, trend_score]

            # 创建水平条形图
            colors = [self.colors['success'] if score >= 70 else
                     self.colors['warning'] if score >= 40 else
                     self.colors['danger'] for score in scores]

            bars = ax.barh(dimensions, scores, color=colors, alpha=0.8)

            # 添加分数标签
            for i, (bar, score) in enumerate(zip(bars, scores)):
                ax.text(score + 1, i, f'{score:.0f}', va='center', color='white', fontsize=9, fontweight='bold')

            # 计算综合强度
            overall_strength = np.mean(scores)
            strength_level = '强势' if overall_strength >= 70 else '中性' if overall_strength >= 40 else '弱势'

            ax.text(0.98, 0.95, f'市场强度: {overall_strength:.0f}分\n等级: {strength_level}',
                   transform=ax.transAxes, ha='right', va='top', color='white', fontsize=10,
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

            ax.set_xlim(0, 100)
            ax.set_title('市场强度分析', color='white', fontsize=12)
            ax.set_xlabel('强度得分', color='white')
            ax.grid(True, alpha=0.3, color=self.colors['grid'], axis='x')

        except Exception as e:
            ax.text(0.5, 0.5, f'市场强度图表错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    def _plot_trend_analysis(self, ax):
        """绘制趋势分析"""
        try:
            # 获取趋势数据
            multi_timeframe = self.analysis_system.analysis_results.get('multi_timeframe', {})
            signals = self.analysis_system.analysis_results.get('current_signals', {})

            # 趋势强度指标
            trend_indicators = ['短期趋势', '中期趋势', '长期趋势', '趋势一致性']

            # 计算趋势强度得分
            short_trend = self._get_trend_strength(signals.get('ema_trend', 'NEUTRAL'))
            medium_trend = self._get_trend_strength(signals.get('macd_trend', 'NEUTRAL'))
            long_trend = self._get_trend_strength(multi_timeframe.get('weekly_trend', 'UNKNOWN'))
            consistency = self._get_consistency_score(multi_timeframe.get('trend_consistency', '趋势分歧'))

            trend_scores = [short_trend, medium_trend, long_trend, consistency]

            # 设置颜色（绿色表示上升趋势，红色表示下降趋势，黄色表示中性）
            colors = []
            for score in trend_scores:
                if score > 60:
                    colors.append(self.colors['success'])
                elif score < 40:
                    colors.append(self.colors['danger'])
                else:
                    colors.append(self.colors['warning'])

            bars = ax.barh(trend_indicators, trend_scores, color=colors, alpha=0.8)

            # 添加分数标签
            for i, (bar, score) in enumerate(zip(bars, trend_scores)):
                trend_desc = '上升' if score > 60 else '下降' if score < 40 else '中性'
                ax.text(score + 1, i, f'{score:.0f} ({trend_desc})',
                       va='center', color='white', fontsize=9, fontweight='bold')

            # 计算综合趋势得分
            overall_trend = np.mean(trend_scores)
            trend_direction = '强势上升' if overall_trend > 70 else '弱势下降' if overall_trend < 30 else '震荡整理'

            ax.text(0.98, 0.95, f'趋势得分: {overall_trend:.0f}分\n方向: {trend_direction}',
                   transform=ax.transAxes, ha='right', va='top', color='white', fontsize=10,
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='black', alpha=0.7))

            ax.set_xlim(0, 100)
            ax.set_title('趋势分析', color='white', fontsize=12)
            ax.set_xlabel('趋势强度', color='white')
            ax.grid(True, alpha=0.3, color=self.colors['grid'], axis='x')

        except Exception as e:
            ax.text(0.5, 0.5, f'趋势分析图表错误: {str(e)}', ha='center', va='center', transform=ax.transAxes, color='red')

    # 辅助计算方法
    def _calculate_activity_score(self, market_chars):
        """计算成交活跃度得分"""
        try:
            avg_volume = market_chars.get('avg_daily_volume', 0)
            if avg_volume > 100:
                return 80
            elif avg_volume > 50:
                return 60
            elif avg_volume > 20:
                return 40
            else:
                return 20
        except:
            return 50

    def _calculate_stability_score(self):
        """计算价格稳定性得分"""
        try:
            market_chars = self.analysis_system.analysis_results.get('market_characteristics', {})
            volatility = market_chars.get('daily_volatility', 5)
            # 波动率越低，稳定性越高
            if volatility < 2:
                return 80
            elif volatility < 5:
                return 60
            elif volatility < 10:
                return 40
            else:
                return 20
        except:
            return 50

    def _calculate_liquidity_score(self, market_chars):
        """计算流动性得分"""
        try:
            liquidity_level = market_chars.get('liquidity_level', '中等流动性')
            if '高流动性' in liquidity_level:
                return 80
            elif '中等流动性' in liquidity_level:
                return 60
            else:
                return 30
        except:
            return 50

    def _calculate_sentiment_strength(self, sentiment_data):
        """计算情绪强度得分"""
        try:
            if sentiment_data and 'comprehensive_sentiment' in sentiment_data:
                sentiment_score = sentiment_data['comprehensive_sentiment'].get('overall_sentiment_score', 50)
                return sentiment_score
            return 50
        except:
            return 50

    def _calculate_trend_consistency(self):
        """计算趋势一致性得分"""
        try:
            multi_timeframe = self.analysis_system.analysis_results.get('multi_timeframe', {})
            consistency = multi_timeframe.get('trend_consistency', '趋势分歧')
            if '强烈' in consistency:
                return 90
            elif '趋势分歧' in consistency:
                return 30
            else:
                return 60
        except:
            return 50

    def _get_trend_strength(self, trend):
        """获取趋势强度得分"""
        if trend in ['UP', 'BULLISH', '上升']:
            return 80
        elif trend in ['DOWN', 'BEARISH', '下降']:
            return 20
        else:
            return 50

    def _get_consistency_score(self, consistency):
        """获取一致性得分"""
        if '强烈上升' in consistency:
            return 90
        elif '强烈下降' in consistency:
            return 10
        elif '趋势分歧' in consistency:
            return 30
        else:
            return 50




    def _extract_id_from_path(self, path):
        """从路径中提取ID"""
        try:
            from pathlib import Path

            path_obj = Path(path)
            name = path_obj.name if path_obj.is_absolute() else path

            # 如果是数字ID，直接返回
            if name.isdigit():
                return name

            # 如果包含其他字符，尝试提取数字部分
            import re
            numbers = re.findall(r'\d+', name)
            if numbers:
                # 返回最长的数字串
                return max(numbers, key=len)

            # 如果没有数字，返回原始名称
            return name

        except:
            return path

def main():
    """测试专业图表系统"""
    print("📊 测试syncps专业图表生成系统...")

    # 这里需要实际的分析系统实例
    # 由于依赖关系，这里只是演示框架
    print("✅ 专业图表系统框架已创建")
    print("💡 使用方法:")
    print("   from professional_chart_system import ProfessionalChartSystem")
    print("   chart_system = ProfessionalChartSystem(analysis_system)")
    print("   chart_system.generate_comprehensive_dashboard(save_path='dashboard.png')")

if __name__ == "__main__":
    main()
