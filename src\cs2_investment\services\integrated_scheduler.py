"""
集成定时任务管理器

将价格更新任务集成到应用启动中，支持后台定时执行。
"""

import asyncio
import logging
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ERROR
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.services.smart_price_update_pool import SmartPriceUpdatePool


class IntegratedScheduler:
    """集成定时任务管理器"""
    
    def __init__(self, api_key: str):
        """
        初始化集成定时任务管理器
        
        Args:
            api_key: SteamDT API密钥
        """
        self.api_key = api_key
        self.logger = logging.getLogger(__name__)
        
        # 创建后台调度器
        self.scheduler = BackgroundScheduler(
            timezone='Asia/Shanghai',
            job_defaults={
                'coalesce': True,  # 合并错过的任务
                'max_instances': 1,  # 每个任务最多同时运行1个实例
                'misfire_grace_time': 300  # 错过任务的宽限时间（秒）
            }
        )
        
        # 智能价格更新池
        self.smart_update_pool = SmartPriceUpdatePool(api_key)
        
        # 任务状态
        self.is_running = False
        self.task_status = {
            'last_price_update': None,
            'last_price_update_result': None,
            'next_price_update': None,
            'price_update_count': 0,
            'price_update_errors': 0
        }
        
        # 配置
        self.config = {
            'cleanup_cron': '0 3 * * *',  # 每天凌晨3点清理数据
            'status_check_interval_minutes': 30,  # 状态检查间隔（分钟）
        }
        
        # 设置事件监听器
        self.scheduler.add_listener(self._job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)
    
    def _job_listener(self, event):
        """任务执行监听器"""
        job_id = event.job_id
        
        if event.exception:
            self.logger.error(f"定时任务执行失败: {job_id}, 异常: {event.exception}")
            if 'price_update' in job_id:
                self.task_status['price_update_errors'] += 1
        else:
            self.logger.info(f"定时任务执行成功: {job_id}")
            if 'price_update' in job_id:
                self.task_status['price_update_count'] += 1
    
    async def start(self):
        """启动定时任务调度器"""
        if self.is_running:
            self.logger.warning("定时任务调度器已经在运行")
            return

        try:
            self.logger.info("🚀 启动集成定时任务调度器")

            # 启动智能更新池（持续运行模式）
            await self.smart_update_pool.start()

            # 添加数据清理任务
            self._add_cleanup_jobs()

            # 添加状态检查任务
            self._add_status_check_jobs()

            # 启动调度器
            self.scheduler.start()
            self.is_running = True

            # 显示任务信息
            self._log_scheduled_jobs()

            self.logger.info("✅ 集成定时任务调度器启动成功")
            
        except Exception as e:
            self.logger.error(f"❌ 启动定时任务调度器失败: {e}")
            raise
    
    def stop(self):
        """停止定时任务调度器"""
        if not self.is_running:
            return

        try:
            self.logger.info("⏹️ 停止集成定时任务调度器")

            # 停止智能更新池
            if self.smart_update_pool:
                self.smart_update_pool.stop()

            # 停止调度器
            self.scheduler.shutdown(wait=True)
            self.is_running = False
            self.logger.info("✅ 定时任务调度器已停止")
        except Exception as e:
            self.logger.error(f"❌ 停止定时任务调度器失败: {e}")
    

    
    def _add_cleanup_jobs(self):
        """添加数据清理任务"""
        # 每天凌晨3点清理过期数据
        self.scheduler.add_job(
            func=self._run_cleanup_task,
            trigger=CronTrigger.from_crontab(self.config['cleanup_cron']),
            id='daily_cleanup',
            name='每日数据清理',
            replace_existing=True
        )
    
    def _add_status_check_jobs(self):
        """添加状态检查任务"""
        # 每30分钟检查一次状态
        self.scheduler.add_job(
            func=self._run_status_check_task,
            trigger=IntervalTrigger(minutes=self.config['status_check_interval_minutes']),
            id='status_check',
            name='状态检查',
            replace_existing=True
        )
    

    
    def _run_cleanup_task(self):
        """执行数据清理任务"""
        self.logger.info("🗑️ 开始执行数据清理任务")
        
        try:
            # 清理90天前的数据
            deleted_count = self.price_service.cleanup_old_data(days=90)
            self.logger.info(f"✅ 数据清理完成，删除了{deleted_count}条记录")
            
        except Exception as e:
            self.logger.error(f"❌ 数据清理任务异常: {e}", exc_info=True)
    
    def _run_status_check_task(self):
        """执行状态检查任务"""
        try:
            # 在新的事件循环中运行异步任务
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # 获取更新池状态
                pool_status = self.smart_update_pool.get_status()
                is_connected = pool_status['is_running']
                
                if not is_connected:
                    self.logger.warning("⚠️ API连接检查失败")
                else:
                    self.logger.debug("✅ API连接正常")
                    
            finally:
                loop.close()
                
        except Exception as e:
            self.logger.error(f"❌ 状态检查任务异常: {e}")
    
    def _log_scheduled_jobs(self):
        """记录已调度的任务信息"""
        jobs = self.scheduler.get_jobs()
        
        self.logger.info("📅 已调度的任务:")
        for job in jobs:
            next_run = job.next_run_time
            if next_run:
                self.logger.info(f"  - {job.name}: {next_run.strftime('%Y-%m-%d %H:%M:%S')}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取调度器状态"""
        jobs = self.scheduler.get_jobs()
        job_info = []
        
        for job in jobs:
            job_info.append({
                'id': job.id,
                'name': job.name,
                'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        
        return {
            'is_running': self.is_running,
            'scheduler_running': self.scheduler.running if hasattr(self.scheduler, 'running') else False,
            'jobs': job_info,
            'task_status': self.task_status.copy(),
            'config': self.config.copy()
        }
    
    async def trigger_smart_price_update_now(self):
        """立即触发智能价格更新任务"""
        if not self.is_running:
            raise RuntimeError("调度器未运行")

        self.logger.info("🚀 手动触发智能价格更新任务")

        # 直接执行一次更新周期
        try:
            result = await self.smart_update_pool.run_update_cycle()
            self.logger.info(f"✅ 手动更新完成: "
                           f"高优先级{result.get('high_priority_processed', 0)}个, "
                           f"低优先级{result.get('low_priority_processed', 0)}个")
            return result
        except Exception as e:
            self.logger.error(f"❌ 手动更新失败: {e}")
            raise
    
    def update_config(self, new_config: Dict[str, Any]):
        """更新配置"""
        self.config.update(new_config)
        
        if self.is_running:
            self.logger.info("🔄 重新配置定时任务")
            
            # 移除旧任务
            self.scheduler.remove_job('scheduled_smart_price_update')
            self.scheduler.remove_job('daily_cleanup')
            self.scheduler.remove_job('status_check')

            # 重新添加任务
            self._add_price_update_jobs()
            self._add_cleanup_jobs()
            self._add_status_check_jobs()
            
            self.logger.info("✅ 定时任务重新配置完成")
