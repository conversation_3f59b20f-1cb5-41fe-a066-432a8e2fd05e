"""
收藏服务层

提供统一收藏表的业务逻辑。
"""

from typing import List, Dict, Any, Optional
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from src.cs2_investment.dao.favorite_dao import FavoriteDAO


class FavoriteService:
    """收藏服务类 - 统一收藏表"""
    
    def __init__(self):
        self.favorite_dao = FavoriteDAO()
        self.default_user_id = "default_user"  # 简化实现，使用默认用户
    
    def add_favorite(self, user_id: str, item_id: str,
                    item_name: str = None, notes: str = None) -> bool:
        """添加饰品收藏"""
        try:
            favorite = self.favorite_dao.add_favorite(
                user_id=user_id,
                item_id=item_id,
                item_name=item_name,
                notes=notes
            )
            return favorite is not None
        except Exception as e:
            print(f"添加收藏失败: {e}")
            return False
    
    def remove_favorite(self, user_id: str, item_id: str) -> bool:
        """取消饰品收藏"""
        try:
            return self.favorite_dao.remove_favorite(user_id, item_id)
        except Exception as e:
            print(f"取消收藏失败: {e}")
            return False
    
    def is_favorited(self, user_id: str, item_id: str) -> bool:
        """检查饰品是否已收藏"""
        try:
            return self.favorite_dao.is_favorited(user_id, item_id)
        except Exception as e:
            print(f"检查收藏状态失败: {e}")
            return False
    
    def get_user_favorites(self, user_id: str, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户收藏列表"""
        try:
            return self.favorite_dao.get_user_favorites(user_id, limit, offset)
        except Exception as e:
            print(f"获取收藏列表失败: {e}")
            return []
    
    def get_favorite_count(self, user_id: str) -> int:
        """获取收藏数量"""
        try:
            return self.favorite_dao.get_favorite_count(user_id)
        except Exception as e:
            print(f"获取收藏数量失败: {e}")
            return 0

    def search_user_favorites(self, user_id: str, name_query: str = None,
                             limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """搜索用户收藏列表"""
        try:
            return self.favorite_dao.search_user_favorites(user_id, name_query, limit, offset)
        except Exception as e:
            print(f"搜索收藏列表失败: {e}")
            return []

    def count_search_favorites(self, user_id: str, name_query: str = None) -> int:
        """统计搜索收藏结果数量"""
        try:
            return self.favorite_dao.count_search_favorites(user_id, name_query)
        except Exception as e:
            print(f"统计搜索收藏结果失败: {e}")
            return 0
    
    def get_favorite_statistics(self, user_id: str = None) -> Dict[str, Any]:
        """获取收藏统计信息"""
        if user_id is None:
            user_id = self.default_user_id

        try:
            total_count = self.get_favorite_count(user_id)

            return {
                'total_count': total_count,
                'user_id': user_id
            }
        except Exception as e:
            print(f"获取收藏统计失败: {e}")
            return {
                'total_count': 0,
                'user_id': user_id
            }
    
    # 兼容性方法 - 为了保持与现有代码的兼容性
    def toggle_item_favorite(self, item_id: str, user_id: str = None) -> Dict[str, Any]:
        """切换饰品收藏状态（兼容性方法）"""
        if user_id is None:
            user_id = self.default_user_id

        try:
            # 检查当前收藏状态
            is_favorited = self.is_favorited(user_id, item_id)

            if is_favorited:
                # 取消收藏
                success = self.remove_favorite(user_id, item_id)
                return {
                    'success': success,
                    'action': 'removed',
                    'is_favorited': False,
                    'message': '已取消收藏' if success else '取消收藏失败'
                }
            else:
                # 添加收藏
                success = self.add_favorite(user_id, item_id)
                return {
                    'success': success,
                    'action': 'added',
                    'is_favorited': True,
                    'message': '已添加收藏' if success else '添加收藏失败'
                }
        except Exception as e:
            return {
                'success': False,
                'action': 'error',
                'is_favorited': False,
                'message': f'操作失败: {str(e)}'
            }

    def is_item_favorited(self, item_id: str, user_id: str = None) -> bool:
        """检查饰品是否已收藏（兼容性方法）"""
        if user_id is None:
            user_id = self.default_user_id
        return self.is_favorited(user_id, item_id)
