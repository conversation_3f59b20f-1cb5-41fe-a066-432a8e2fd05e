#!/usr/bin/env python3
"""
定时调度器启动脚本

独立启动定时任务调度器，负责定期创建分析任务
"""

import sys
import logging
import asyncio
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """启动定时调度器"""
    print("=" * 60)
    print("⏰ SteamDT定时调度器启动脚本")
    print("=" * 60)
    
    try:
        # 使用统一日志组件
        from src.cs2_investment.utils.logger import get_logger
        logger = get_logger(__name__)
        
        # 导入调度器
        from src.cs2_investment.scheduler.smart_scheduler import smart_scheduler
        
        print("📋 功能说明:")
        print("  - 每天8点开始常规分析")
        print("  - 每小时执行实时监控")
        print("  - 自动处理失败重试")
        print("  - 支持API调用和直接调用")
        print("")
        print("🚀 正在启动调度器...")
        
        # 启动调度器
        asyncio.run(smart_scheduler.start())
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所需依赖: pip install -r requirements.txt")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 调度器已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
