# 饰品分析通用组件使用指南

## 概述

`ItemAnalysisComponent` 是一个通用的饰品分析组件，从持仓管理页面的分析功能中抽取而来。该组件提供了完整的饰品分析功能，包括：

- 📈 实时分析结果展示
- 📊 常规分析结果展示  
- 🚀 启动新分析功能
- 📋 分析进度跟踪
- 📈 图表展示

## 主要功能

### 1. 分析按钮渲染
提供统一的分析按钮样式和交互逻辑

### 2. 分析对话框
完整的分析结果展示对话框，包含三个标签页：
- 实时分析结果
- 常规分析结果
- 启动新分析

### 3. 任务进度跟踪
实时显示分析任务的执行状态和进度

## 使用方法

### 基本用法

```python
from src.cs2_investment.app.components.item_analysis_component import item_analysis_component

# 1. 在列表中渲染分析按钮
def show_item_card(item_data):
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        st.write(item_data['name'])
    
    with col2:
        # 其他按钮...
        pass
    
    with col3:
        # 渲染分析按钮
        item_analysis_component.render_analysis_button(
            item_id=item_data['item_id'],
            button_key=f"analysis_btn_{item_data['item_id']}",
            help_text="查看分析结果"
        )
    
    # 2. 渲染分析对话框
    item_analysis_component.render_analysis_dialog(
        item_data=item_data,
        dialog_key_suffix="_favorites"  # 用于区分不同页面
    )
```

### 在收藏列表中使用

```python
# favorites.py
from src.cs2_investment.app.components.item_analysis_component import item_analysis_component

def show_favorite_item(favorite_item):
    # ... 其他UI代码 ...
    
    # 分析按钮
    item_analysis_component.render_analysis_button(
        item_id=favorite_item['item_id'],
        button_key=f"fav_analysis_{favorite_item['item_id']}",
        help_text="查看收藏饰品分析结果"
    )
    
    # 分析对话框
    item_analysis_component.render_analysis_dialog(
        item_data={
            'item_id': favorite_item['item_id'],
            'item_name': favorite_item['item_name']
        },
        dialog_key_suffix="_favorites"
    )
```

### 在饰品列表中使用

```python
# items.py  
from src.cs2_investment.app.components.item_analysis_component import item_analysis_component

def show_item_list():
    for item in items:
        # ... 其他UI代码 ...
        
        # 分析按钮
        item_analysis_component.render_analysis_button(
            item_id=item['item_id'],
            button_key=f"item_analysis_{item['item_id']}",
            help_text="查看饰品分析结果"
        )
        
        # 分析对话框
        item_analysis_component.render_analysis_dialog(
            item_data=item,
            dialog_key_suffix="_items"
        )
```

## 参数说明

### render_analysis_button()

- `item_id`: 饰品ID（必需）
- `button_key`: 按钮的唯一key（必需）
- `help_text`: 按钮提示文本（可选，默认："查看分析结果"）

### render_analysis_dialog()

- `item_data`: 饰品数据字典（必需），必须包含：
  - `item_id`: 饰品ID
  - `item_name`: 饰品名称
- `dialog_key_suffix`: 对话框key的后缀（可选），用于区分不同页面的对话框

## 注意事项

### 1. 唯一性保证
- 每个页面使用不同的 `dialog_key_suffix` 来避免key冲突
- 按钮的 `button_key` 必须在页面内唯一

### 2. 数据格式
- `item_data` 必须包含 `item_id` 和 `item_name` 字段
- 其他字段可选，组件会自动处理

### 3. 状态管理
- 组件使用 `st.session_state` 管理对话框显示状态
- 任务进度状态也存储在 `st.session_state` 中

### 4. 服务依赖
- 组件会自动初始化所需的分析服务
- 确保相关的DAO和Service类可以正常导入

## 组件优势

### 1. 代码复用
- 一次编写，多处使用
- 统一的UI风格和交互逻辑

### 2. 维护性
- 集中管理分析功能的代码
- 修改一处，所有使用的地方都会更新

### 3. 扩展性
- 易于添加新的分析类型
- 支持自定义样式和行为

### 4. 一致性
- 所有页面的分析功能保持一致
- 统一的用户体验

## 文件结构

```
src/cs2_investment/app/components/
├── __init__.py
├── item_analysis_component.py  # 主组件文件
└── README.md                   # 使用文档
```

## 依赖关系

组件依赖以下模块：
- `streamlit`: UI框架
- `src.cs2_investment.dao.analysis_result_dao`: 分析结果数据访问
- `src.cs2_investment.app.services.holding_service`: 持仓服务

## 后续扩展

可以考虑添加的功能：
- 分析结果缓存
- 自定义分析参数
- 批量分析功能
- 分析结果导出
- 更多图表类型支持
