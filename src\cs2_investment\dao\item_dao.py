"""
饰品数据访问对象

提供饰品相关的数据库操作。
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from loguru import logger

from .base_dao import BaseDAO
from ..models.item import Item
from ..config.database import get_db_session


class ItemDAO(BaseDAO[Item]):
    """饰品DAO"""
    
    def __init__(self):
        super().__init__(Item)
    
    def get_by_item_id(self, item_id: str) -> Optional[Dict[str, Any]]:
        """根据饰品ID获取记录，返回字典格式避免Session问题"""
        try:
            with get_db_session() as session:
                item = session.query(Item).filter(Item.item_id == item_id).first()
                if item:
                    # 转换为字典格式
                    return {
                        'item_id': item.item_id,
                        'name': item.name,
                        'item_type': item.item_type,
                        'quality': item.quality,
                        'rarity': item.rarity,
                        'exterior': getattr(item, 'exterior', None),
                        'image_url': getattr(item, 'image_url', None),
                        'market_hash_name': getattr(item, 'market_hash_name', None),
                        'def_index_name': getattr(item, 'def_index_name', None),
                        'created_at': getattr(item, 'created_at', None),
                        'updated_at': getattr(item, 'updated_at', None)
                    }
                return None
        except SQLAlchemyError as e:
            self.logger.error(f"根据饰品ID获取记录失败: {e}")
            raise
    
    def get_by_name(self, name: str) -> List[Item]:
        """根据饰品名称搜索"""
        try:
            with get_db_session() as session:
                return session.query(Item).filter(
                    Item.name.like(f"%{name}%")
                ).all()
        except SQLAlchemyError as e:
            self.logger.error(f"根据名称搜索饰品失败: {e}")
            raise

    def search_items_by_name(self, name: str, limit: int = 50) -> List[dict]:
        """根据饰品名称搜索（用于界面选择）"""
        try:
            with get_db_session() as session:
                items = session.query(Item).filter(
                    Item.name.like(f"%{name}%")
                ).limit(limit).all()

                # 在会话内提取数据，避免会话关闭后的访问问题
                result = []
                for item in items:
                    result.append({
                        'item_id': item.item_id,
                        'name': item.name
                    })
                return result
        except SQLAlchemyError as e:
            self.logger.error(f"搜索饰品失败: {e}")
            raise
    
    def get_by_type(self, item_type: str) -> List[Item]:
        """根据饰品类型获取"""
        try:
            with get_db_session() as session:
                return session.query(Item).filter(Item.item_type == item_type).all()
        except SQLAlchemyError as e:
            self.logger.error(f"根据类型获取饰品失败: {e}")
            raise
    
    def get_by_rarity(self, rarity: str) -> List[Item]:
        """根据稀有度获取"""
        try:
            with get_db_session() as session:
                return session.query(Item).filter(Item.rarity == rarity).all()
        except SQLAlchemyError as e:
            self.logger.error(f"根据稀有度获取饰品失败: {e}")
            raise

    def get_all_active_items(self, limit: int = 1000) -> List[Dict[str, Any]]:
        """获取所有活跃饰品（用于价格更新）"""
        try:
            with get_db_session() as session:
                items = session.query(Item).limit(limit).all()
                # 转换为字典格式，避免session绑定问题
                return [
                    {
                        'item_id': item.item_id,
                        'name': item.name,
                        'item_type': item.item_type,
                        'quality': item.quality,
                        'rarity': item.rarity,
                        'exterior': getattr(item, 'exterior', None),
                        'image_url': getattr(item, 'image_url', None),
                        'market_hash_name': getattr(item, 'market_hash_name', None),
                        'def_index_name': getattr(item, 'def_index_name', None)
                    }
                    for item in items
                ]
        except SQLAlchemyError as e:
            self.logger.error(f"获取所有活跃饰品失败: {e}")
            raise
    
    def search_items(self, keyword: str, item_type: Optional[str] = None,
                    rarity: Optional[str] = None, limit: int = 1000) -> List[Item]:
        """综合搜索饰品"""
        try:
            with get_db_session() as session:
                query = session.query(Item)

                # 关键词搜索
                if keyword:
                    query = query.filter(
                        Item.name.like(f"%{keyword}%") |
                        Item.market_hash_name.like(f"%{keyword}%")
                    )

                # 类型过滤
                if item_type:
                    query = query.filter(Item.item_type == item_type)

                # 稀有度过滤
                if rarity:
                    query = query.filter(Item.rarity == rarity)

                # 获取结果并强制加载属性
                items = query.limit(limit).all()

                # 强制加载所有属性
                for item in items:
                    _ = item.item_id
                    _ = item.name
                    _ = item.item_type
                    _ = item.quality
                    _ = item.rarity
                    _ = getattr(item, 'exterior', None)
                    _ = getattr(item, 'image_url', None)
                    _ = getattr(item, 'market_hash_name', None)
                    _ = getattr(item, 'created_at', None)
                    _ = getattr(item, 'updated_at', None)

                return items
        except SQLAlchemyError as e:
            self.logger.error(f"综合搜索饰品失败: {e}")
            raise

    def advanced_search(self, name_query: Optional[str] = None,
                       item_types: Optional[List[str]] = None,
                       qualities: Optional[List[str]] = None,
                       rarities: Optional[List[str]] = None,
                       sort_by: str = "updated_desc",
                       limit: int = 1000,
                       offset: int = 0) -> List[Item]:
        """高级搜索饰品"""
        try:
            with get_db_session() as session:
                query = session.query(Item)

                # 名称搜索
                if name_query:
                    query = query.filter(
                        Item.name.like(f"%{name_query}%") |
                        Item.market_hash_name.like(f"%{name_query}%")
                    )

                # 类型过滤
                if item_types:
                    query = query.filter(Item.item_type.in_(item_types))

                # 品质过滤
                if qualities:
                    query = query.filter(Item.quality.in_(qualities))

                # 稀有度过滤
                if rarities:
                    query = query.filter(Item.rarity.in_(rarities))

                # 排序
                if sort_by == "name_asc":
                    query = query.order_by(Item.name.asc())
                elif sort_by == "name_desc":
                    query = query.order_by(Item.name.desc())
                elif sort_by == "updated_desc":
                    query = query.order_by(Item.updated_at.desc())
                elif sort_by == "updated_asc":
                    query = query.order_by(Item.updated_at.asc())
                else:
                    query = query.order_by(Item.updated_at.desc())

                # 获取结果并立即访问所有属性以避免Session问题
                items = query.offset(offset).limit(limit).all()

                # 强制加载所有属性
                for item in items:
                    # 访问所有属性以确保它们被加载
                    _ = item.item_id
                    _ = item.name
                    _ = item.item_type
                    _ = item.quality
                    _ = item.rarity
                    _ = getattr(item, 'exterior', None)
                    _ = getattr(item, 'image_url', None)
                    _ = getattr(item, 'market_hash_name', None)
                    _ = getattr(item, 'created_at', None)
                    _ = getattr(item, 'updated_at', None)

                return items
        except SQLAlchemyError as e:
            self.logger.error(f"高级搜索饰品失败: {e}")
            raise

    def get_distinct_values(self, field: str) -> List[str]:
        """获取指定字段的不重复值"""
        try:
            with get_db_session() as session:
                if not hasattr(Item, field):
                    raise ValueError(f"Item模型没有字段: {field}")

                column = getattr(Item, field)
                results = session.query(column).distinct().filter(column.isnot(None)).all()
                return [result[0] for result in results if result[0]]
        except SQLAlchemyError as e:
            self.logger.error(f"获取字段不重复值失败: {e}")
            raise
    
    def upsert_item(self, item_data: dict) -> Item:
        """插入或更新饰品信息"""
        try:
            with get_db_session() as session:
                item_id = item_data.get('item_id')
                existing_item = session.query(Item).filter(Item.item_id == item_id).first()
                
                if existing_item:
                    # 更新现有记录
                    for key, value in item_data.items():
                        if hasattr(existing_item, key) and key != 'item_id':
                            setattr(existing_item, key, value)
                    session.flush()
                    session.refresh(existing_item)
                    self.logger.info(f"更新饰品信息: {item_id}")
                    return existing_item
                else:
                    # 创建新记录
                    new_item = Item(**item_data)
                    session.add(new_item)
                    session.flush()
                    session.refresh(new_item)
                    self.logger.info(f"创建饰品信息: {item_id}")
                    return new_item
        except SQLAlchemyError as e:
            self.logger.error(f"插入或更新饰品信息失败: {e}")
            raise
    
    def batch_upsert_items(self, items_data: List[dict]) -> List[Item]:
        """批量插入或更新饰品信息"""
        try:
            with get_db_session() as session:
                results = []
                
                for item_data in items_data:
                    item_id = item_data.get('item_id')
                    existing_item = session.query(Item).filter(Item.item_id == item_id).first()
                    
                    if existing_item:
                        # 更新现有记录
                        for key, value in item_data.items():
                            if hasattr(existing_item, key) and key != 'item_id':
                                setattr(existing_item, key, value)
                        results.append(existing_item)
                    else:
                        # 创建新记录
                        new_item = Item(**item_data)
                        session.add(new_item)
                        results.append(new_item)
                
                session.flush()
                for item in results:
                    session.refresh(item)
                
                self.logger.info(f"批量处理饰品信息: {len(results)}条")
                return results
        except SQLAlchemyError as e:
            self.logger.error(f"批量插入或更新饰品信息失败: {e}")
            raise
    
    def get_item_statistics(self) -> dict:
        """获取饰品统计信息"""
        try:
            with get_db_session() as session:
                total_count = session.query(Item).count()
                
                # 按类型统计
                type_stats = session.query(
                    Item.item_type, 
                    session.query(Item).filter(Item.item_type == Item.item_type).count().label('count')
                ).group_by(Item.item_type).all()
                
                # 按稀有度统计
                rarity_stats = session.query(
                    Item.rarity,
                    session.query(Item).filter(Item.rarity == Item.rarity).count().label('count')
                ).group_by(Item.rarity).all()
                
                return {
                    'total_count': total_count,
                    'type_distribution': {stat[0]: stat[1] for stat in type_stats if stat[0]},
                    'rarity_distribution': {stat[0]: stat[1] for stat in rarity_stats if stat[0]}
                }
        except SQLAlchemyError as e:
            self.logger.error(f"获取饰品统计信息失败: {e}")
            raise


# 创建全局实例
item_dao = ItemDAO()
