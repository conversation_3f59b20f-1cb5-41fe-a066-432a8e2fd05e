"""
收藏数据访问对象

提供收藏相关的数据库操作。
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import desc, asc, and_, or_, func
from loguru import logger

from .base_dao import BaseDAO
from ..models.favorite import Favorite
from ..models.item import Item
from ..models.screening_result import ScreeningResult
from ..config.database import get_db_session


class FavoriteDAO(BaseDAO[Favorite]):
    """收藏DAO"""
    
    def __init__(self):
        super().__init__(Favorite)
    
    def add_favorite(self, user_id: str, item_id: str,
                    item_name: str = None, notes: str = None) -> Optional[Favorite]:
        """添加饰品收藏"""
        try:
            with get_db_session() as session:
                # 检查是否已经收藏
                existing = session.query(Favorite).filter(
                    and_(
                        Favorite.user_id == user_id,
                        Favorite.item_id == item_id
                    )
                ).first()

                if existing:
                    self.logger.info(f"饰品已收藏: user_id={user_id}, item_id={item_id}")
                    return existing

                # 创建新收藏
                favorite = Favorite(
                    user_id=user_id,
                    item_id=item_id,
                    item_name=item_name,
                    notes=notes,
                    created_at=datetime.now()
                )

                session.add(favorite)
                session.flush()
                session.refresh(favorite)

                self.logger.info(f"添加饰品收藏成功: user_id={user_id}, item_id={item_id}")
                return favorite

        except SQLAlchemyError as e:
            self.logger.error(f"添加收藏失败: {e}")
            raise
    
    def remove_favorite(self, user_id: str, item_id: str) -> bool:
        """取消饰品收藏"""
        try:
            with get_db_session() as session:
                deleted_count = session.query(Favorite).filter(
                    and_(
                        Favorite.user_id == user_id,
                        Favorite.item_id == item_id
                    )
                ).delete()

                if deleted_count > 0:
                    self.logger.info(f"取消收藏成功: user_id={user_id}, item_id={item_id}")
                    return True
                else:
                    self.logger.warning(f"收藏不存在: user_id={user_id}, item_id={item_id}")
                    return False

        except SQLAlchemyError as e:
            self.logger.error(f"取消收藏失败: {e}")
            raise
    
    def is_favorited(self, user_id: str, item_id: str) -> bool:
        """检查饰品是否已收藏"""
        try:
            with get_db_session() as session:
                count = session.query(Favorite).filter(
                    and_(
                        Favorite.user_id == user_id,
                        Favorite.item_id == item_id
                    )
                ).count()

                return count > 0

        except SQLAlchemyError as e:
            self.logger.error(f"检查收藏状态失败: {e}")
            return False
    
    def get_user_favorites(self, user_id: str, limit: int = 1000, offset: int = 0) -> List[Dict[str, Any]]:
        """获取用户收藏列表，返回字典格式避免Session问题"""
        try:
            with get_db_session() as session:
                favorites = session.query(Favorite).filter(
                    Favorite.user_id == user_id
                ).order_by(desc(Favorite.created_at))\
                .offset(offset).limit(limit).all()

                # 立即转换为字典格式
                results = []
                for fav in favorites:
                    fav_dict = {
                        'id': fav.id,
                        'user_id': fav.user_id,
                        'item_id': fav.item_id,
                        'item_name': fav.item_name,
                        'created_at': fav.created_at,
                        'updated_at': fav.updated_at,
                        'notes': fav.notes
                    }
                    results.append(fav_dict)

                return results

        except SQLAlchemyError as e:
            self.logger.error(f"获取用户收藏列表失败: {e}")
            raise

    def get_favorite_count(self, user_id: str) -> int:
        """获取收藏数量"""
        try:
            with get_db_session() as session:
                count = session.query(Favorite).filter(
                    Favorite.user_id == user_id
                ).count()

                return count

        except SQLAlchemyError as e:
            self.logger.error(f"获取收藏数量失败: {e}")
            return 0

    def search_user_favorites(self, user_id: str,
                             name_query: Optional[str] = None,
                             limit: int = 1000,
                             offset: int = 0) -> List[Dict[str, Any]]:
        """搜索用户收藏列表（带条件筛选）"""
        try:
            with get_db_session() as session:
                query = session.query(Favorite).filter(
                    Favorite.user_id == user_id
                )

                # 名称搜索
                if name_query:
                    query = query.filter(
                        Favorite.item_name.like(f"%{name_query}%")
                    )

                favorites = query.order_by(desc(Favorite.created_at))\
                    .offset(offset).limit(limit).all()

                # 立即转换为字典格式
                results = []
                for fav in favorites:
                    fav_dict = {
                        'id': fav.id,
                        'user_id': fav.user_id,
                        'item_id': fav.item_id,
                        'item_name': fav.item_name,
                        'created_at': fav.created_at,
                        'updated_at': fav.updated_at,
                        'notes': fav.notes
                    }
                    results.append(fav_dict)

                return results

        except SQLAlchemyError as e:
            self.logger.error(f"搜索用户收藏列表失败: {e}")
            raise

    def count_search_favorites(self, user_id: str, name_query: Optional[str] = None) -> int:
        """统计搜索收藏结果数量"""
        try:
            with get_db_session() as session:
                query = session.query(Favorite).filter(
                    Favorite.user_id == user_id
                )

                # 名称搜索
                if name_query:
                    query = query.filter(
                        Favorite.item_name.like(f"%{name_query}%")
                    )

                return query.count()

        except SQLAlchemyError as e:
            self.logger.error(f"统计搜索收藏结果失败: {e}")
            return 0
    
    def get_favorites_with_prices(self, user_id: str, favorite_type: str = 'item',
                                 limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """获取带价格信息的收藏列表"""
        try:
            from ..dao.market_snapshot_dao import MarketSnapshotDAO
            
            # 获取收藏列表
            favorites = self.get_user_favorites(user_id, favorite_type, limit, offset)
            
            if not favorites:
                return []
            
            # 获取价格信息
            snapshot_dao = MarketSnapshotDAO()
            item_ids = [fav.item_id for fav in favorites]
            snapshots = snapshot_dao.get_latest_snapshots_by_items(item_ids)
            
            # 创建快照字典
            snapshot_dict = {snapshot.item_id: snapshot for snapshot in snapshots}
            
            # 合并数据
            results = []
            for favorite in favorites:
                item = favorite.item
                snapshot = snapshot_dict.get(favorite.item_id)
                
                result = {
                    'favorite_id': favorite.id,
                    'item_id': item.item_id,
                    'name': item.name,
                    'item_type': item.item_type,
                    'quality': item.quality,
                    'rarity': item.rarity,
                    'exterior': item.exterior,
                    'image_url': item.image_url,
                    'favorite_time': favorite.created_at,
                    'notes': favorite.notes
                }
                
                if snapshot:
                    result.update({
                        'current_price': float(snapshot.current_price) if snapshot.current_price else 0,
                        'price_change_7d': float(snapshot.diff_7d) if snapshot.diff_7d else 0,
                        'price_change_30d': float(snapshot.diff_1m) if snapshot.diff_1m else 0,
                        'volume_30d': snapshot.trans_count_1m,
                        'hot_rank': snapshot.hot_rank,
                        'snapshot_time': snapshot.snapshot_time
                    })
                else:
                    result.update({
                        'current_price': 0,
                        'price_change_7d': 0,
                        'price_change_30d': 0,
                        'volume_30d': 0,
                        'hot_rank': None,
                        'snapshot_time': None
                    })
                
                results.append(result)
            
            return results
            
        except SQLAlchemyError as e:
            self.logger.error(f"获取带价格信息的收藏列表失败: {e}")
            raise
    
    def count_user_favorites(self, user_id: str, favorite_type: str = 'item') -> int:
        """统计用户收藏数量"""
        try:
            with get_db_session() as session:
                return session.query(Favorite).filter(
                    and_(
                        Favorite.user_id == user_id,
                        Favorite.favorite_type == favorite_type
                    )
                ).count()
                
        except SQLAlchemyError as e:
            self.logger.error(f"统计用户收藏数量失败: {e}")
            return 0
    
    def get_favorite_statistics(self, user_id: str) -> Dict[str, Any]:
        """获取收藏统计信息"""
        try:
            with get_db_session() as session:
                # 总收藏数
                total_count = session.query(Favorite).filter(Favorite.user_id == user_id).count()
                
                # 按类型统计
                type_stats = session.query(
                    Favorite.favorite_type,
                    func.count(Favorite.id).label('count')
                ).filter(Favorite.user_id == user_id)\
                .group_by(Favorite.favorite_type).all()
                
                # 最近收藏
                recent_count = session.query(Favorite).filter(
                    and_(
                        Favorite.user_id == user_id,
                        Favorite.created_at >= datetime.now() - timedelta(days=7)
                    )
                ).count()
                
                return {
                    'total_count': total_count,
                    'type_distribution': {stat[0]: stat[1] for stat in type_stats},
                    'recent_count': recent_count
                }
                
        except SQLAlchemyError as e:
            self.logger.error(f"获取收藏统计信息失败: {e}")
            return {}


# ScreeningResultFavoriteDAO 已废弃，使用统一的 FavoriteDAO
