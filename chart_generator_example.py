#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CS2饰品分析图表生成示例

演示如何使用图表生成器生成各种分析图表
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from generate_analysis_charts import AnalysisChartGenerator


def example_generate_single_chart():
    """示例：生成单个饰品的图表"""
    print("=" * 60)
    print("📊 示例1: 生成单个饰品的分析图表")
    print("=" * 60)
    
    # 创建图表生成器
    generator = AnalysisChartGenerator()
    
    # 示例饰品名称（请根据实际情况修改）
    item_name = "AK-47 | 红线 (久经沙场)"
    
    print(f"🎯 目标饰品: {item_name}")
    
    # 生成所有类型的图表
    result = generator.generate_all_charts(item_name)
    
    if result['overall_success']:
        print(f"\n🎉 图表生成成功!")
        print(f"📁 查看生成的图表文件:")
        for chart in result['charts']:
            if chart['success']:
                print(f"   📈 {chart['chart_type']}: {chart['chart_path']}")
    else:
        print(f"\n❌ 图表生成失败，请检查饰品名称和数据")


def example_generate_with_data_path():
    """示例：使用数据路径生成图表"""
    print("\n" + "=" * 60)
    print("📊 示例2: 使用数据路径生成图表")
    print("=" * 60)
    
    # 创建图表生成器
    generator = AnalysisChartGenerator()
    
    # 示例数据路径（请根据实际情况修改）
    data_path = "data/scraped_data/某个饰品ID"
    item_name = "示例饰品"
    
    print(f"🎯 数据路径: {data_path}")
    print(f"🎯 饰品名称: {item_name}")
    
    # 只生成专业投资仪表板
    result = generator.generate_professional_dashboard(item_name, data_path)
    
    if result['success']:
        print(f"\n✅ 专业投资仪表板生成成功!")
        print(f"📈 图表路径: {result['chart_path']}")
    else:
        print(f"\n❌ 图表生成失败: {result['error']}")


def example_generate_technical_only():
    """示例：只生成技术指标图表"""
    print("\n" + "=" * 60)
    print("📊 示例3: 只生成技术指标图表")
    print("=" * 60)
    
    # 创建图表生成器
    generator = AnalysisChartGenerator()
    
    # 示例饰品名称
    item_name = "M4A4 | 龙王 (崭新出厂)"
    
    print(f"🎯 目标饰品: {item_name}")
    
    # 只生成技术指标图表
    result = generator.generate_technical_chart(item_name)
    
    if result['success']:
        print(f"\n✅ 技术指标图表生成成功!")
        print(f"📈 图表路径: {result['chart_path']}")
    else:
        print(f"\n❌ 图表生成失败: {result['error']}")


def example_list_available_items():
    """示例：列出可用的饰品数据"""
    print("\n" + "=" * 60)
    print("📊 示例4: 列出可用的饰品数据")
    print("=" * 60)
    
    # 创建图表生成器
    generator = AnalysisChartGenerator()
    
    # 获取可用饰品列表
    items = generator.list_available_items()
    
    if items:
        print("📋 可用的饰品数据:")
        for i, item in enumerate(items[:10], 1):  # 只显示前10个
            print(f"   {i:2d}. {item}")
        
        if len(items) > 10:
            print(f"   ... 还有 {len(items) - 10} 个饰品")
        
        print(f"\n💡 总共找到 {len(items)} 个饰品数据")
        print("💡 您可以使用这些饰品名称来生成图表")
    else:
        print("❌ 未找到可用的饰品数据")
        print("💡 请确保 data/scraped_data 目录下有饰品数据")


def interactive_chart_generation():
    """交互式图表生成"""
    print("\n" + "=" * 60)
    print("📊 交互式图表生成")
    print("=" * 60)
    
    # 创建图表生成器
    generator = AnalysisChartGenerator()
    
    # 获取可用饰品
    items = generator.list_available_items()
    
    if not items:
        print("❌ 未找到可用的饰品数据")
        return
    
    print("📋 可用的饰品数据:")
    for i, item in enumerate(items[:20], 1):  # 显示前20个
        print(f"   {i:2d}. {item}")
    
    if len(items) > 20:
        print(f"   ... 还有 {len(items) - 20} 个饰品")
    
    try:
        # 用户选择
        choice = input(f"\n请选择饰品编号 (1-{min(len(items), 20)}) 或输入饰品名称: ").strip()
        
        if choice.isdigit():
            index = int(choice) - 1
            if 0 <= index < min(len(items), 20):
                selected_item = items[index]
            else:
                print("❌ 无效的编号")
                return
        else:
            selected_item = choice
        
        print(f"\n🎯 选择的饰品: {selected_item}")
        
        # 选择图表类型
        print("\n📊 图表类型选择:")
        print("   1. 所有图表")
        print("   2. 专业投资仪表板")
        print("   3. 技术指标图表")
        
        chart_choice = input("请选择图表类型 (1-3): ").strip()
        
        # 生成图表
        if chart_choice == "1":
            result = generator.generate_all_charts(selected_item)
            success = result['overall_success']
        elif chart_choice == "2":
            result = generator.generate_professional_dashboard(selected_item)
            success = result['success']
        elif chart_choice == "3":
            result = generator.generate_technical_chart(selected_item)
            success = result['success']
        else:
            print("❌ 无效的选择")
            return
        
        if success:
            print(f"\n🎉 图表生成成功!")
        else:
            print(f"\n❌ 图表生成失败")
            
    except KeyboardInterrupt:
        print(f"\n🛑 用户取消操作")
    except Exception as e:
        print(f"\n❌ 操作失败: {e}")


def main():
    """主函数 - 运行所有示例"""
    print("🎮 CS2饰品分析图表生成器示例")
    print("=" * 60)
    
    try:
        # 运行示例
        example_list_available_items()
        
        # 注释掉其他示例，避免实际执行时出错
        # example_generate_single_chart()
        # example_generate_with_data_path()
        # example_generate_technical_only()
        
        # 交互式生成
        interactive_chart_generation()
        
    except KeyboardInterrupt:
        print(f"\n🛑 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
