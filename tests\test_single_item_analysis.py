#!/usr/bin/env python3
"""
CS2饰品智能投资决策系统 - 单个饰品完整分析测试

针对指定饰品进行完整的分析流程测试，详细记录每个步骤的执行情况
"""

import asyncio
import sys
import json
import traceback
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 导入日志模块
try:
    from src.cs2_investment.utils.logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)


class SingleItemAnalysisTester:
    """单个饰品分析测试器"""
    
    def __init__(self, item_id: str, data_base_path: str = None):
        self.item_id = item_id
        # 修复路径问题：从tests目录运行时，需要回到上级目录
        if data_base_path is None:
            project_root = Path(__file__).parent.parent
            self.data_base_path = str(project_root / "data" / "scraped_data")
        else:
            self.data_base_path = data_base_path
        self.logger = logger
        
    def check_data_availability(self) -> Dict[str, Any]:
        """检查数据可用性"""
        item_path = Path(self.data_base_path) / self.item_id
        
        data_files = {
            '周k.json': '战略分析数据',
            '日k1.json': '战术分析数据',
            '日k2.json': '历史验证数据',
            '时k.json': '执行分析数据',
            '走势_3m.json': '短期供需数据',
            '走势_6m.json': '长期供需数据'
        }
        
        availability = {}
        for filename, description in data_files.items():
            file_path = item_path / filename
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        availability[filename] = {
                            'available': True,
                            'description': description,
                            'records_count': len(data) if isinstance(data, list) else 1,
                            'file_size_kb': file_path.stat().st_size / 1024
                        }
                except Exception as e:
                    availability[filename] = {
                        'available': False,
                        'description': description,
                        'error': str(e)
                    }
            else:
                availability[filename] = {
                    'available': False,
                    'description': description,
                    'error': '文件不存在'
                }
        
        return availability
    
    async def run_complete_analysis(self) -> Dict[str, Any]:
        """运行完整的分析流程"""
        test_start_time = datetime.now()
        
        try:
            self.logger.info(f"🎯 开始对饰品 {self.item_id} 进行完整分析测试")
            
            # 1. 检查数据可用性
            self.logger.info("📋 检查数据可用性...")
            data_availability = self.check_data_availability()
            
            # 2. 导入并初始化分析接口
            self.logger.info("🔧 初始化分析接口...")
            from src.cs2_investment.fx.intelligent_analysis.integration.unified_analysis_interface import UnifiedAnalysisInterface
            
            interface = UnifiedAnalysisInterface(self.data_base_path)
            await interface.initialize()
            
            # 3. 执行各层次分析
            analysis_results = {}
            
            # 3.1 综合分析
            self.logger.info("📊 执行综合分析...")
            try:
                comprehensive_result = await interface.analyze_item(
                    self.item_id, 
                    analysis_type="comprehensive"
                )
                analysis_results['comprehensive'] = comprehensive_result
                self.logger.info(f"✅ 综合分析完成: {comprehensive_result.get('success', False)}")
            except Exception as e:
                self.logger.error(f"❌ 综合分析失败: {e}")
                analysis_results['comprehensive'] = {'success': False, 'error': str(e)}
            
            # 3.2 战略分析
            self.logger.info("🎯 执行战略分析...")
            try:
                strategic_result = await interface.analyze_item(
                    self.item_id, 
                    analysis_type="strategic"
                )
                analysis_results['strategic'] = strategic_result
                self.logger.info(f"✅ 战略分析完成: {strategic_result.get('success', False)}")
            except Exception as e:
                self.logger.error(f"❌ 战略分析失败: {e}")
                analysis_results['strategic'] = {'success': False, 'error': str(e)}
            
            # 3.3 战术分析
            self.logger.info("⚔️ 执行战术分析...")
            try:
                tactical_result = await interface.analyze_item(
                    self.item_id, 
                    analysis_type="tactical"
                )
                analysis_results['tactical'] = tactical_result
                self.logger.info(f"✅ 战术分析完成: {tactical_result.get('success', False)}")
            except Exception as e:
                self.logger.error(f"❌ 战术分析失败: {e}")
                analysis_results['tactical'] = {'success': False, 'error': str(e)}
            
            # 3.4 执行分析
            self.logger.info("⚡ 执行执行分析...")
            try:
                execution_result = await interface.analyze_item(
                    self.item_id, 
                    analysis_type="execution"
                )
                analysis_results['execution'] = execution_result
                self.logger.info(f"✅ 执行分析完成: {execution_result.get('success', False)}")
            except Exception as e:
                self.logger.error(f"❌ 执行分析失败: {e}")
                analysis_results['execution'] = {'success': False, 'error': str(e)}
            
            # 4. 生成投资推荐
            self.logger.info("💡 生成投资推荐...")
            try:
                if analysis_results.get('comprehensive', {}).get('success', False):
                    recommendation = interface.get_recommendation_summary(analysis_results['comprehensive'])
                    analysis_results['recommendation'] = recommendation
                    self.logger.info(f"✅ 投资推荐生成完成: {recommendation.get('recommendation_type', 'N/A')}")
                else:
                    analysis_results['recommendation'] = {'error': '综合分析失败，无法生成推荐'}
                    self.logger.warning("⚠️ 无法生成投资推荐：综合分析失败")
            except Exception as e:
                self.logger.error(f"❌ 投资推荐生成失败: {e}")
                analysis_results['recommendation'] = {'error': str(e)}
            
            # 5. 获取接口状态
            interface_status = interface.get_interface_status()
            
            # 计算测试耗时
            test_duration = (datetime.now() - test_start_time).total_seconds()
            
            # 6. 分析结果评估
            evaluation = self.evaluate_analysis_results(analysis_results)
            
            self.logger.info(f"🎉 饰品 {self.item_id} 完整分析测试完成 (耗时: {test_duration:.2f}秒)")
            
            return {
                'status': 'success',
                'item_id': self.item_id,
                'test_duration_seconds': test_duration,
                'data_availability': data_availability,
                'analysis_results': analysis_results,
                'interface_status': interface_status,
                'evaluation': evaluation,
                'summary': f"分析完成，总体评分: {evaluation['overall_score']}/100"
            }
            
        except Exception as e:
            test_duration = (datetime.now() - test_start_time).total_seconds()
            self.logger.error(f"❌ 饰品 {self.item_id} 分析测试失败: {e}")
            
            return {
                'status': 'failed',
                'item_id': self.item_id,
                'test_duration_seconds': test_duration,
                'error': str(e),
                'traceback': traceback.format_exc(),
                'summary': f"分析失败: {str(e)}"
            }
    
    def evaluate_analysis_results(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """评估分析结果"""
        evaluation = {
            'successful_analyses': 0,
            'failed_analyses': 0,
            'analysis_details': {},
            'overall_score': 0,
            'quality_level': 'UNKNOWN'
        }
        
        analysis_types = ['comprehensive', 'strategic', 'tactical', 'execution']
        
        for analysis_type in analysis_types:
            result = analysis_results.get(analysis_type, {})
            success = result.get('success', False)
            
            if success:
                evaluation['successful_analyses'] += 1
                evaluation['analysis_details'][analysis_type] = 'SUCCESS'
            else:
                evaluation['failed_analyses'] += 1
                error = result.get('error', 'Unknown error')
                evaluation['analysis_details'][analysis_type] = f'FAILED: {error}'
        
        # 检查推荐生成
        recommendation = analysis_results.get('recommendation', {})
        if 'recommendation_type' in recommendation:
            evaluation['recommendation_generated'] = True
            evaluation['recommendation_type'] = recommendation.get('recommendation_type', 'N/A')
            evaluation['confidence_level'] = recommendation.get('confidence_level', 0)
        else:
            evaluation['recommendation_generated'] = False
        
        # 计算总体评分
        success_rate = evaluation['successful_analyses'] / len(analysis_types)
        base_score = success_rate * 70  # 基础分数70分
        
        if evaluation['recommendation_generated']:
            base_score += 20  # 推荐生成加20分
        
        if evaluation['successful_analyses'] == len(analysis_types):
            base_score += 10  # 全部成功加10分
        
        evaluation['overall_score'] = min(base_score, 100)
        
        # 质量等级
        if evaluation['overall_score'] >= 90:
            evaluation['quality_level'] = 'EXCELLENT'
        elif evaluation['overall_score'] >= 70:
            evaluation['quality_level'] = 'GOOD'
        elif evaluation['overall_score'] >= 50:
            evaluation['quality_level'] = 'FAIR'
        else:
            evaluation['quality_level'] = 'POOR'
        
        return evaluation

    def add_enhanced_metrics(self, original_results: Dict[str, Any]) -> Dict[str, Any]:
        """将新的量化指标添加到原有的分析结果中"""
        try:
            # 导入增强版分析服务
            from cs2_investment.services.analysis_data_service import AnalysisDataService

            service = AnalysisDataService()

            # 从原有结果中提取基础数据
            market_data = self.extract_market_data_from_original_results(original_results)

            # 从原有分析结果中提取统一供给分析数据，确保一致性
            supply_quantity = market_data['supply_quantity']

            # 从原有分析中获取unified_supply_analysis数据
            unified_supply_data = self.extract_unified_supply_from_original_results(original_results)

            # 构造分析输入数据
            analysis_input = {
                'current_price': market_data['current_price'],
                'supply_demand': {
                    'supply_quantity': supply_quantity,
                    'demand_quantity': market_data['demand_quantity'],
                    'supply_demand_ratio': supply_quantity / market_data['demand_quantity'] if market_data['demand_quantity'] > 0 else 0,
                    'bid_price': market_data['bid_price'],
                    'unified_supply_analysis': unified_supply_data
                },
                'market_sentiment': {
                    'overall_sentiment': 'SLIGHTLY_BULLISH',
                    'sentiment_description': '偏乐观',
                    'comprehensive_score': 58.5,
                    'sentiment_advice': '市场情绪偏乐观，可谨慎买入',
                    'key_factors': {
                        '供需情绪': 'BULLISH' if market_data['supply_quantity'] < market_data['demand_quantity'] * 2 else 'NEUTRAL',
                        '动量情绪': 'NEUTRAL',
                        '恐慌贪婪': 'SLIGHT_GREED',
                        '异常恐慌': 'LOW_PANIC'
                    }
                },
                'volume_status': 'NORMAL'
            }

            # 运行增强版分析获取新指标
            enhanced_metrics = service._extract_realtime_analysis_metrics_v2(
                analysis_input,
                self.item_id,
                f'AK-47 | 红线 (久经沙场)',
                str(Path(self.data_base_path) / self.item_id),
                f'data/exports/chart_{self.item_id}.png',
                f'data/exports/report_{self.item_id}.json',
                2.5
            )

            # 将新指标添加到原有结果的合适位置
            enhanced_results = original_results.copy()

            # 在根级别添加新的量化指标部分
            enhanced_results['enhanced_metrics'] = {
                'supply_demand_unified': {
                    'supply_ratio': enhanced_metrics.get('supply_ratio'),
                    'supply_status': enhanced_metrics.get('supply_status'),
                    'scarcity_level': enhanced_metrics.get('scarcity_level'),
                    'scarcity_description': enhanced_metrics.get('scarcity_description'),
                    'total_circulation': enhanced_metrics.get('total_circulation')
                },
                'bid_premium_analysis': {
                    'bid_premium_percentage': enhanced_metrics.get('bid_premium_percentage'),
                    'bid_premium_level': enhanced_metrics.get('bid_premium_level'),
                    'demand_strength': enhanced_metrics.get('demand_strength'),
                    'bid_premium_description': enhanced_metrics.get('bid_premium_description'),
                    'strength_description': enhanced_metrics.get('strength_description')
                },
                'liquidity_quantification': {
                    'daily_volume_avg': enhanced_metrics.get('daily_volume_avg'),
                    'turnover_rate': enhanced_metrics.get('turnover_rate'),
                    'bid_ask_spread_pct': enhanced_metrics.get('bid_ask_spread_pct'),
                    'liquidity_score': enhanced_metrics.get('liquidity_score'),
                    'execution_difficulty': enhanced_metrics.get('execution_difficulty'),
                    'market_depth': enhanced_metrics.get('market_depth'),
                    'total_orders': enhanced_metrics.get('total_orders'),
                    'liquidity_description': enhanced_metrics.get('liquidity_description')
                },
                'anomaly_detection': {
                    'total_anomalies': enhanced_metrics.get('total_anomalies'),
                    'high_risk_anomalies': enhanced_metrics.get('high_risk_anomalies'),
                    'medium_risk_anomalies': enhanced_metrics.get('medium_risk_anomalies'),
                    'low_risk_anomalies': enhanced_metrics.get('low_risk_anomalies'),
                    'anomaly_types': enhanced_metrics.get('anomaly_types'),
                    'recent_alerts': enhanced_metrics.get('recent_alerts'),
                    'anomaly_summary': enhanced_metrics.get('anomaly_summary'),
                    'anomaly_risk_level': enhanced_metrics.get('anomaly_risk_level'),
                    'anomaly_risk_impact': enhanced_metrics.get('anomaly_risk_impact'),
                    'anomaly_detection_status': enhanced_metrics.get('anomaly_detection_status')
                },
                'market_sentiment_quantified': {
                    'sentiment_score': enhanced_metrics.get('sentiment_score'),
                    'sentiment_level': enhanced_metrics.get('sentiment_level'),
                    'sentiment_description': enhanced_metrics.get('sentiment_description'),
                    'fear_greed_index': enhanced_metrics.get('fear_greed_index'),
                    'investor_confidence': enhanced_metrics.get('investor_confidence'),
                    'sentiment_factors': enhanced_metrics.get('sentiment_factors'),
                    'sentiment_advice': enhanced_metrics.get('sentiment_advice'),
                    'sentiment_trend': enhanced_metrics.get('sentiment_trend'),
                    'sentiment_analysis_status': enhanced_metrics.get('sentiment_analysis_status')
                },
                'data_quality_validation': enhanced_metrics.get('data_quality'),
                'metadata': enhanced_metrics.get('metadata')
            }

            # 在每个分析类型中也添加相关的新指标
            for analysis_type in ['comprehensive', 'strategic', 'tactical', 'execution']:
                if analysis_type in enhanced_results and enhanced_results[analysis_type].get('success'):
                    analysis_data = enhanced_results[analysis_type].get('analysis_data', {})

                    # 添加供需分析增强
                    if 'supply_demand_analysis' in analysis_data:
                        analysis_data['supply_demand_analysis']['enhanced_metrics'] = {
                            'supply_ratio': enhanced_metrics.get('supply_ratio'),
                            'scarcity_level': enhanced_metrics.get('scarcity_level'),
                            'scarcity_description': enhanced_metrics.get('scarcity_description')
                        }

                    # 添加市场情绪增强
                    if 'market_sentiment' in analysis_data:
                        analysis_data['market_sentiment']['quantified_metrics'] = {
                            'sentiment_score': enhanced_metrics.get('sentiment_score'),
                            'fear_greed_index': enhanced_metrics.get('fear_greed_index'),
                            'investor_confidence': enhanced_metrics.get('investor_confidence')
                        }

                    # 添加流动性分析增强
                    analysis_data['liquidity_analysis'] = {
                        'liquidity_score': enhanced_metrics.get('liquidity_score'),
                        'execution_difficulty': enhanced_metrics.get('execution_difficulty'),
                        'market_depth': enhanced_metrics.get('market_depth'),
                        'turnover_rate': enhanced_metrics.get('turnover_rate')
                    }

            self.logger.info("✅ 成功将新的量化指标添加到原有分析结果中")
            return enhanced_results

        except Exception as e:
            self.logger.warning(f"添加增强指标失败: {e}")
            # 如果失败，返回原有结果
            return original_results

    def extract_market_data_from_original_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """从原有分析结果中提取市场数据"""
        market_data = {
            'current_price': 1538.5,  # 默认值
            'supply_quantity': 225,
            'demand_quantity': 75,
            'bid_price': 1480.0
        }

        try:
            # 尝试从comprehensive分析中提取数据
            comprehensive = results.get('comprehensive', {})
            if comprehensive.get('success'):
                analysis_data = comprehensive.get('analysis_data', {})

                # 提取价格信息
                price_analysis = analysis_data.get('price_analysis', {})
                if 'current_price' in price_analysis:
                    market_data['current_price'] = price_analysis['current_price']

                # 提取供需信息
                supply_demand = analysis_data.get('supply_demand_analysis', {})
                if 'supply_quantity' in supply_demand:
                    market_data['supply_quantity'] = supply_demand['supply_quantity']
                if 'demand_quantity' in supply_demand:
                    market_data['demand_quantity'] = supply_demand['demand_quantity']
                if 'bid_price' in supply_demand:
                    market_data['bid_price'] = supply_demand['bid_price']

        except Exception as e:
            self.logger.warning(f"从原有结果提取市场数据失败: {e}")

        return market_data

    def extract_unified_supply_from_original_results(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """从原有分析结果中提取统一供给分析数据，确保与常规分析一致"""
        try:
            # 优先从comprehensive分析中提取
            for analysis_type in ['comprehensive', 'strategic', 'tactical', 'execution']:
                if analysis_type in results and results[analysis_type].get('success'):
                    analysis_data = results[analysis_type].get('analysis_data', {})

                    # 检查多个可能的路径
                    supply_demand_paths = [
                        'supply_demand_analysis',
                        'supply_demand',
                        'market_analysis',
                        'data_analysis'
                    ]

                    for path in supply_demand_paths:
                        if path in analysis_data:
                            supply_demand = analysis_data[path]
                            if isinstance(supply_demand, dict) and 'unified_supply_analysis' in supply_demand:
                                unified_data = supply_demand['unified_supply_analysis']
                                self.logger.info(f"从{analysis_type}.{path}中提取到统一供给数据: total_circulation={unified_data.get('total_circulation')}, supply_ratio={unified_data.get('supply_ratio')}")
                                return unified_data

            # 如果没有找到，尝试直接搜索整个结果
            self.logger.warning("在标准路径中未找到统一供给数据，尝试深度搜索...")
            unified_data = self._deep_search_unified_supply(results)
            if unified_data:
                return unified_data

            # 如果还是没有找到，使用默认估算（基于新的供给紧张度算法）
            self.logger.warning("未找到原有分析中的统一供给数据，使用默认估算")
            return {
                'supply_ratio': 11.11,  # 基于25/225的供给紧张度
                'supply_status': 'TIGHT',
                'scarcity_level': 'HIGH',
                'supply_quantity': 225,
                'daily_volume': 25.0,
                'description': '供给紧张 (11.11%)，成交量较大，在售量相对不足，价格有上涨压力',
                'calculation_method': 'default_estimation'
            }

        except Exception as e:
            self.logger.error(f"提取统一供给数据失败: {e}")
            return {
                'supply_ratio': 11.11,  # 基于25/225的供给紧张度
                'supply_status': 'TIGHT',
                'scarcity_level': 'HIGH',
                'supply_quantity': 225,
                'daily_volume': 25.0,
                'description': '供给紧张 (11.11%)，成交量较大，在售量相对不足，价格有上涨压力',
                'calculation_method': 'error_fallback'
            }

    def _deep_search_unified_supply(self, data: Any, path: str = "") -> Dict[str, Any]:
        """深度搜索unified_supply_analysis数据"""
        try:
            if isinstance(data, dict):
                if 'unified_supply_analysis' in data:
                    unified_data = data['unified_supply_analysis']
                    if isinstance(unified_data, dict) and 'total_circulation' in unified_data:
                        self.logger.info(f"在路径 {path} 中找到统一供给数据: {unified_data}")
                        return unified_data

                # 递归搜索
                for key, value in data.items():
                    result = self._deep_search_unified_supply(value, f"{path}.{key}" if path else key)
                    if result:
                        return result

            elif isinstance(data, list):
                for i, item in enumerate(data):
                    result = self._deep_search_unified_supply(item, f"{path}[{i}]" if path else f"[{i}]")
                    if result:
                        return result

            return None

        except Exception as e:
            self.logger.error(f"深度搜索失败: {e}")
            return None

    def generate_intelligent_chart(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成智能分析图表

        Args:
            analysis_results: 分析结果数据

        Returns:
            Dict: 图表生成结果
        """
        try:
            self.logger.info(f"📊 开始为饰品 {self.item_id} 生成智能分析图表...")

            # 检查分析结果是否成功
            if analysis_results.get('status') != 'success':
                self.logger.warning("分析结果不成功，跳过图表生成")
                return {
                    'success': False,
                    'error': '分析结果不成功',
                    'chart_path': None
                }

            # 导入智能图表系统
            from src.cs2_investment.fx.intelligent_analysis.visualization.intelligent_chart_system import IntelligentChartSystem

            # 创建图表系统
            chart_system = IntelligentChartSystem(analysis_results, self.item_id)

            # 生成保存路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            project_root = Path(__file__).parent.parent
            chart_dir = project_root / "data" / "analysis_results" / self.item_id
            chart_dir.mkdir(parents=True, exist_ok=True)

            chart_filename = f"{self.item_id}_智能分析仪表板_{timestamp}.png"
            chart_path = chart_dir / chart_filename

            # 生成智能分析仪表板
            result_path = chart_system.generate_intelligent_dashboard(
                save_path=str(chart_path),
                show_chart=False
            )

            if result_path:
                self.logger.info(f"✅ 智能分析图表生成成功: {result_path}")
                return {
                    'success': True,
                    'chart_path': result_path,
                    'chart_type': 'intelligent_dashboard',
                    'timestamp': timestamp
                }
            else:
                self.logger.error("❌ 智能分析图表生成失败")
                return {
                    'success': False,
                    'error': '图表生成返回空路径',
                    'chart_path': None
                }

        except ImportError as e:
            self.logger.error(f"❌ 智能图表系统导入失败: {e}")
            return {
                'success': False,
                'error': f'图表系统导入失败: {str(e)}',
                'chart_path': None
            }
        except Exception as e:
            self.logger.error(f"❌ 智能分析图表生成失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'chart_path': None
            }

    def print_detailed_report(self, results: Dict[str, Any]):
        """打印详细报告"""
        print("\n" + "="*80)
        print(f"🎯 饰品 {self.item_id} 智能分析完整测试报告")
        print("="*80)
        
        # 基本信息
        print(f"\n📋 基本信息:")
        print(f"   饰品ID: {results['item_id']}")
        print(f"   测试状态: {'✅ 成功' if results['status'] == 'success' else '❌ 失败'}")
        print(f"   测试耗时: {results.get('test_duration_seconds', 0):.2f}秒")
        
        if results['status'] == 'failed':
            print(f"   失败原因: {results.get('error', 'Unknown')}")
            print("\n" + "="*80)
            return
        
        # 数据可用性
        print(f"\n📊 数据可用性:")
        data_availability = results.get('data_availability', {})
        for filename, info in data_availability.items():
            status = "✅" if info.get('available', False) else "❌"
            records = info.get('records_count', 0)
            size = info.get('file_size_kb', 0)
            print(f"   {status} {filename}: {info.get('description', 'N/A')} ({records} 条记录, {size:.1f}KB)")
        
        # 分析结果
        print(f"\n🔧 分析结果:")
        evaluation = results.get('evaluation', {})
        analysis_details = evaluation.get('analysis_details', {})
        
        for analysis_type, status in analysis_details.items():
            status_icon = "✅" if status == 'SUCCESS' else "❌"
            print(f"   {status_icon} {analysis_type}: {status}")
        
        # 推荐结果
        if evaluation.get('recommendation_generated', False):
            rec_type = evaluation.get('recommendation_type', 'N/A')
            confidence = evaluation.get('confidence_level', 0)
            print(f"   💡 投资推荐: {rec_type} (置信度: {confidence}%)")
        else:
            print(f"   ❌ 投资推荐: 生成失败")
        
        # 总体评估
        print(f"\n🎯 总体评估:")
        print(f"   成功分析: {evaluation.get('successful_analyses', 0)}/4")
        print(f"   失败分析: {evaluation.get('failed_analyses', 0)}/4")
        print(f"   总体评分: {evaluation.get('overall_score', 0)}/100")
        print(f"   质量等级: {evaluation.get('quality_level', 'UNKNOWN')}")

        # 图表生成结果
        chart_generation = results.get('chart_generation', {})
        if chart_generation:
            print(f"\n📊 智能图表生成:")
            if chart_generation.get('success', False):
                print(f"   ✅ 图表生成成功")
                chart_path = chart_generation.get('chart_path')
                if chart_path:
                    print(f"   📈 图表路径: {chart_path}")
                chart_type = chart_generation.get('chart_type', 'unknown')
                timestamp = chart_generation.get('timestamp', 'unknown')
                print(f"   🎨 图表类型: {chart_type}")
                print(f"   ⏰ 生成时间: {timestamp}")
            else:
                print(f"   ❌ 图表生成失败")
                error = chart_generation.get('error', 'Unknown error')
                print(f"   💥 失败原因: {error}")

        print("\n" + "="*80)


async def main():
    """主函数"""
    # 指定要测试的饰品ID
    item_id = "25698"

    print(f"🚀 启动饰品 {item_id} 的智能分析完整测试")

    # 初始化测试器
    tester = SingleItemAnalysisTester(item_id)

    # 运行完整分析测试
    results = await tester.run_complete_analysis()

    # 添加新的量化指标到原有结果中
    enhanced_results = tester.add_enhanced_metrics(results)

    # 生成智能分析图表
    print(f"\n📊 开始生成智能分析图表...")
    chart_result = tester.generate_intelligent_chart(enhanced_results)

    # 将图表结果添加到增强结果中
    enhanced_results['chart_generation'] = chart_result

    # 打印详细报告
    tester.print_detailed_report(enhanced_results)

    # 保存测试结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    # 修复输出路径：使用项目根目录
    project_root = Path(__file__).parent.parent
    output_file = project_root / "data" / "exports" / f"single_item_analysis_{item_id}_{timestamp}.json"

    try:
        output_path = Path(output_file)
        output_path.parent.mkdir(parents=True, exist_ok=True)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(enhanced_results, f, ensure_ascii=False, indent=2, default=str)

        print(f"\n📄 详细测试结果已保存到: {output_path}")

    except Exception as e:
        print(f"\n❌ 保存测试结果失败: {e}")

    print(f"\n🎉 饰品 {item_id} 智能分析完整测试完成!")

    return enhanced_results


if __name__ == "__main__":
    # 运行测试
    asyncio.run(main())
